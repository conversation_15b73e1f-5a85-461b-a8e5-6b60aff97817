<!DOCTYPE html>
<html>
<head>
    <title>Add Sample Provider Data</title>
    <script type="module">
        // Import Firebase
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, doc, setDoc, addDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Your Firebase config (replace with your actual config)
        const firebaseConfig = {
            // Add your Firebase config here
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Sample providers data
        const sampleProviders = [
            {
                id: 'provider-1',
                businessName: 'Happy Paws Pet Care',
                ownerName: '<PERSON>',
                email: '<EMAIL>',
                phone: '(*************',
                address: '123 Main St',
                city: 'Austin',
                state: 'TX',
                zipCode: '78701',
                serviceType: 'Pet Sitting',
                description: 'Professional pet sitting services with over 10 years of experience.',
                specialties: ['Dog Walking', 'Pet Sitting', 'Overnight Care'],
                rating: 4.8,
                reviewCount: 127,
                status: 'approved',
                verified: true,
                featured: false,
                responseTime: '< 30 minutes',
                website: 'https://happypaws.com',
                profilePhoto: '',
                businessHours: {
                    monday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                    tuesday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                    wednesday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                    thursday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                    friday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                    saturday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                    sunday: { open: '9:00 AM', close: '5:00 PM', closed: false }
                }
            },
            {
                id: 'provider-2',
                businessName: 'Paws & Claws Veterinary',
                ownerName: 'Dr. Michael Chen',
                email: '<EMAIL>',
                phone: '(*************',
                address: '456 Oak Ave',
                city: 'Austin',
                state: 'TX',
                zipCode: '78702',
                serviceType: 'Veterinary Care',
                description: 'Full-service veterinary clinic providing comprehensive medical care.',
                specialties: ['Emergency Care', 'Surgery', 'Dental Care', 'Vaccinations'],
                rating: 4.9,
                reviewCount: 89,
                status: 'approved',
                verified: true,
                featured: true,
                responseTime: '< 15 minutes',
                website: 'https://pawsclaws.com',
                profilePhoto: '',
                businessHours: {
                    monday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                    tuesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                    wednesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                    thursday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                    friday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                    saturday: { open: '9:00 AM', close: '4:00 PM', closed: false },
                    sunday: { closed: true }
                }
            },
            {
                id: 'provider-3',
                businessName: 'Furry Friends Grooming',
                ownerName: 'Lisa Rodriguez',
                email: '<EMAIL>',
                phone: '(*************',
                address: '789 Elm St',
                city: 'Austin',
                state: 'TX',
                zipCode: '78703',
                serviceType: 'Pet Grooming',
                description: 'Professional pet grooming services for all breeds.',
                specialties: ['Full Grooming', 'Nail Trimming', 'Teeth Cleaning'],
                rating: 4.7,
                reviewCount: 156,
                status: 'approved',
                verified: true,
                featured: false,
                responseTime: '< 1 hour',
                website: 'https://furryfriends.com',
                profilePhoto: '',
                businessHours: {
                    monday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                    tuesday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                    wednesday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                    thursday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                    friday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                    saturday: { open: '10:00 AM', close: '3:00 PM', closed: false },
                    sunday: { closed: true }
                }
            }
        ];

        // Sample services
        const sampleServices = [
            {
                providerId: 'provider-1',
                name: 'Dog Walking',
                description: 'Professional dog walking service for busy pet owners',
                category: 'Pet Sitting',
                price: 25,
                duration: 30,
                petTypes: ['dog'],
                active: true
            },
            {
                providerId: 'provider-1',
                name: 'Pet Sitting',
                description: 'In-home pet sitting while you\'re away',
                category: 'Pet Sitting',
                price: 50,
                duration: 480,
                petTypes: ['dog', 'cat'],
                active: true
            },
            {
                providerId: 'provider-2',
                name: 'Wellness Exam',
                description: 'Comprehensive health examination for your pet',
                category: 'Veterinary Care',
                price: 75,
                duration: 45,
                petTypes: ['dog', 'cat'],
                active: true
            },
            {
                providerId: 'provider-2',
                name: 'Emergency Care',
                description: '24/7 emergency veterinary services',
                category: 'Veterinary Care',
                price: 150,
                duration: 60,
                petTypes: ['dog', 'cat', 'bird', 'rabbit'],
                active: true
            },
            {
                providerId: 'provider-3',
                name: 'Full Grooming Package',
                description: 'Complete grooming including bath, cut, and nail trim',
                category: 'Pet Grooming',
                price: 65,
                duration: 120,
                petTypes: ['dog'],
                active: true
            },
            {
                providerId: 'provider-3',
                name: 'Cat Grooming',
                description: 'Gentle grooming services for cats',
                category: 'Pet Grooming',
                price: 45,
                duration: 90,
                petTypes: ['cat'],
                active: true
            }
        ];

        // Function to add sample data
        window.addSampleData = async function() {
            try {
                console.log('🚀 Adding sample provider data...');

                // Add providers
                for (const provider of sampleProviders) {
                    await setDoc(doc(db, 'providers', provider.id), provider);
                    console.log(`✅ Added provider: ${provider.businessName}`);
                }

                // Add services
                for (const service of sampleServices) {
                    await addDoc(collection(db, 'services'), service);
                    console.log(`✅ Added service: ${service.name}`);
                }

                console.log('🎉 Sample data added successfully!');
                alert('Sample data added successfully! Check the search page now.');

            } catch (error) {
                console.error('❌ Error adding sample data:', error);
                alert('Error adding sample data. Check console for details.');
            }
        };

        console.log('📝 Sample data script loaded. Run addSampleData() to add providers and services.');
    </script>
</head>
<body>
    <h1>Add Sample Provider Data</h1>
    <p>Open browser console and run: <code>addSampleData()</code></p>
    <button onclick="addSampleData()">Add Sample Data</button>
</body>
</html>
