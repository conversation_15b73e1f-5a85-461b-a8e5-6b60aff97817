# 💳 Fetchly Payment System Documentation

## 🎯 Overview

Fetchly's payment ecosystem supports two core payment flows:

1. **PetOwner Payments**: Credit Card or Fetchly Wallet
2. **Provider Payouts**: Stripe Connect with invoicing capabilities

## 🏗️ Architecture

### Payment Flow Diagram
```
PetOwner → [Credit Card/Wallet] → Fetchly Platform → [Stripe Connect] → Provider
```

### Key Components

- **Payment Service**: Handles credit card payments and wallet operations
- **Provider Service**: Manages Stripe Connect onboarding and invoicing
- **Webhook Handler**: Real-time payment status updates
- **Payment Components**: React components for payment UI

## 🚀 Quick Setup

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Firebase Admin SDK
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com
```

### 2. Stripe Setup

1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Enable Stripe Connect in your dashboard
3. Configure webhook endpoints:
   - `https://your-domain.com/api/webhooks/stripe`
4. Add webhook events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `transfer.paid`
   - `payout.paid`
   - `account.updated`

### 3. Firebase Setup

1. Create a Firebase service account
2. Download the service account key
3. Extract the required fields for environment variables

## 📱 Usage Examples

### PetOwner Payment

```tsx
import PaymentModal from '@/components/payments/PaymentModal';

function BookingPage() {
  const [showPayment, setShowPayment] = useState(false);

  const handlePaymentSuccess = (result) => {
    console.log('Payment successful:', result);
    // Handle successful payment
  };

  return (
    <div>
      <button onClick={() => setShowPayment(true)}>
        Pay for Service
      </button>
      
      <PaymentModal
        isOpen={showPayment}
        onClose={() => setShowPayment(false)}
        amount={50.00}
        providerId="provider_123"
        serviceId="service_456"
        description="Dog Walking Service"
        onSuccess={handlePaymentSuccess}
      />
    </div>
  );
}
```

### Fetchly Wallet Management

```tsx
import WalletManager from '@/components/payments/WalletManager';

function WalletPage() {
  return (
    <div className="container mx-auto p-6">
      <WalletManager className="max-w-md mx-auto" />
    </div>
  );
}
```

### Provider Onboarding

```tsx
import ProviderOnboarding from '@/components/payments/ProviderOnboarding';

function ProviderSetupPage() {
  return (
    <div className="container mx-auto p-6">
      <ProviderOnboarding />
    </div>
  );
}
```

## 🔧 API Endpoints

### Payment APIs

- `POST /api/payments/create-intent` - Create payment intent for credit card
- `POST /api/wallet/topup` - Add funds to Fetchly wallet
- `POST /api/wallet/pay` - Pay using wallet balance

### Provider APIs

- `GET /api/providers/onboard` - Check onboarding status
- `POST /api/providers/onboard` - Start Stripe Connect onboarding
- `POST /api/providers/create-invoice` - Create payment link/invoice

### Webhook API

- `POST /api/webhooks/stripe` - Handle Stripe webhook events

## 💰 Payment Features

### For PetOwners

- ✅ Credit/Debit card payments
- ✅ Fetchly Wallet (preload funds)
- ✅ Saved payment methods
- ✅ Payment history
- ✅ Secure checkout

### For Providers

- ✅ Stripe Connect Express onboarding
- ✅ Automatic payouts
- ✅ Invoice generation
- ✅ Payment link creation
- ✅ Earnings tracking
- ✅ Tax reporting (1099)

### Platform Features

- ✅ 10% platform fee
- ✅ Real-time payment updates
- ✅ Dispute management
- ✅ Fraud protection
- ✅ Mobile responsive

## 🔒 Security

- **PCI Compliance**: Stripe handles all card data
- **Encryption**: All sensitive data encrypted in transit
- **Authentication**: Firebase Auth + JWT tokens
- **Webhooks**: Signature verification for all webhooks
- **Fraud Protection**: Stripe Radar integration

## 📊 Database Schema

### Collections

```typescript
// transactions
{
  type: 'payment' | 'wallet_topup' | 'wallet_payment' | 'payout',
  status: 'pending' | 'succeeded' | 'failed',
  amount: number,
  platformFee: number,
  providerId: string,
  userId: string,
  stripePaymentIntentId: string,
  createdAt: string,
  updatedAt: string
}

// invoices
{
  providerId: string,
  amount: number,
  platformFee: number,
  description: string,
  stripePaymentLinkId: string,
  status: 'pending' | 'paid',
  createdAt: string,
  updatedAt: string
}

// payouts
{
  stripePayoutId: string,
  amount: number,
  currency: string,
  status: 'paid',
  arrivalDate: string,
  createdAt: string
}
```

## 🧪 Testing

### Test Cards

Use Stripe's test cards for development:

- **Success**: `****************`
- **Decline**: `****************`
- **Insufficient Funds**: `****************`

### Test Webhooks

Use Stripe CLI to test webhooks locally:

```bash
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```

## 🚨 Error Handling

### Common Errors

- **Insufficient Funds**: Wallet balance too low
- **Card Declined**: Invalid or declined card
- **Provider Not Onboarded**: Complete Stripe setup first
- **Invalid Amount**: Amount outside allowed range

### Error Responses

```json
{
  "success": false,
  "error": "Insufficient wallet balance",
  "code": "INSUFFICIENT_FUNDS"
}
```

## 📈 Monitoring

### Key Metrics

- Payment success rate
- Average transaction amount
- Wallet usage vs. card payments
- Provider onboarding completion rate
- Platform fee revenue

### Webhook Monitoring

Monitor webhook delivery in Stripe dashboard:
- Success rate
- Response times
- Failed deliveries

## 🔄 Deployment

### Environment Setup

1. **Development**: Use Stripe test keys
2. **Production**: Use Stripe live keys
3. **Webhooks**: Update endpoint URLs for each environment

### Database Migration

Ensure these collections exist in Firestore:
- `transactions`
- `invoices`
- `payouts`
- `disputes`

## 📞 Support

### Stripe Support
- Dashboard: [dashboard.stripe.com](https://dashboard.stripe.com)
- Documentation: [stripe.com/docs](https://stripe.com/docs)

### Firebase Support
- Console: [console.firebase.google.com](https://console.firebase.google.com)
- Documentation: [firebase.google.com/docs](https://firebase.google.com/docs)

## 🎉 Success!

Your Fetchly payment system is now ready to:

1. ✅ Accept payments from pet owners
2. ✅ Manage Fetchly wallet balances
3. ✅ Onboard service providers
4. ✅ Process automatic payouts
5. ✅ Handle invoicing and billing
6. ✅ Track all transactions

**Happy coding! 🐾💳**
