# 🚀 Fetchly PostgreSQL Setup Instructions

## Current Status
✅ PostgreSQL 17.5 is installed and running  
✅ PostgreSQL is configured on port **3005**  
✅ All backend code is ready  
❌ Need to find/set the PostgreSQL password  

## 🔧 Quick Setup (Choose One Method)

### Method 1: Use pgAdmin (Easiest)

1. **Open pgAdmin** (installed with PostgreSQL)
   - Look for "pgAdmin 4" in your Start menu
   - Or go to: http://localhost:5050

2. **Connect to PostgreSQL**
   - Server: localhost
   - Port: 3005
   - Username: postgres
   - Password: (try the password you set during installation)

3. **Once connected, run this SQL:**
   ```sql
   -- Create fetchly user
   CREATE USER fetchly_user WITH PASSWORD 'fetchly_password';
   ALTER USER fetchly_user CREATEDB;
   
   -- Create fetchly database
   CREATE DATABASE fetchly_db OWNER fetchly_user;
   GRANT ALL PRIVILEGES ON DATABASE fetchly_db TO fetchly_user;
   ```

4. **Update .env.local** with your postgres password:
   ```env
   POSTGRES_PASSWORD=your_actual_password_here
   ```

5. **Run the setup:**
   ```bash
   npm run db:setup
   npm run dev:full
   ```

### Method 2: Command Line Setup

1. **Find your PostgreSQL password** (try these):
   - The password you set during PostgreSQL installation
   - Check if you saved it in a password manager
   - Look for installation notes

2. **Test connection:**
   ```bash
   node setup-postgres-helper.js
   ```
   - Enter your password when prompted
   - Let it set up the database automatically

3. **Start the application:**
   ```bash
   npm run dev:full
   ```

### Method 3: Reset PostgreSQL Password (If Forgotten)

1. **Stop PostgreSQL service:**
   ```cmd
   net stop postgresql-x64-17
   ```

2. **Edit pg_hba.conf** (as Administrator):
   - File: `C:\Program Files\PostgreSQL\17\data\pg_hba.conf`
   - Change the line with `host all all 127.0.0.1/32 scram-sha-256`
   - To: `host all all 127.0.0.1/32 trust`

3. **Start PostgreSQL:**
   ```cmd
   net start postgresql-x64-17
   ```

4. **Connect and reset password:**
   ```cmd
   "C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -p 3005
   ```
   ```sql
   ALTER USER postgres PASSWORD 'newpassword';
   ```

5. **Restore pg_hba.conf** (change `trust` back to `scram-sha-256`)

6. **Restart PostgreSQL:**
   ```cmd
   net stop postgresql-x64-17
   net start postgresql-x64-17
   ```

## 🎯 After Database Setup

Once PostgreSQL is working, the setup script will:

1. ✅ Create all database tables (users, pets, bookings, etc.)
2. ✅ Set up proper relationships and indexes
3. ✅ Insert sample reward items
4. ✅ Configure triggers for automatic timestamps

## 🚀 Starting the Application

```bash
# Start both Next.js and Socket.IO servers
npm run dev:full
```

This starts:
- **Frontend**: http://localhost:3000
- **API**: http://localhost:3000/api  
- **Socket.IO**: http://localhost:3002

## 🧪 Testing the Application

### Create Test Accounts

1. **Go to**: http://localhost:3000
2. **Register new accounts** with these roles:

```javascript
// Pet Owner Account
{
  name: "Sarah Johnson",
  email: "<EMAIL>", 
  password: "TestPass123!",
  role: "pet_owner"
}

// Service Provider Account  
{
  name: "Dr. Michael Chen",
  email: "<EMAIL>",
  password: "TestPass123!", 
  role: "provider"
}
```

### Test Features

1. **Authentication**: Login/logout with JWT tokens
2. **Pet Management**: Add pets with medical records
3. **Booking System**: Create and manage bookings
4. **Real-time Chat**: Message between users
5. **Rewards System**: Earn and redeem points
6. **Virtual Wallet**: Manage Fetchly Balance

## 🔍 Troubleshooting

### PostgreSQL Issues

1. **Service not running:**
   ```cmd
   net start postgresql-x64-17
   ```

2. **Wrong port (should be 3005):**
   - Check: `C:\Program Files\PostgreSQL\17\data\postgresql.conf`
   - Look for: `port = 3005`

3. **Connection refused:**
   - Verify service is running
   - Check Windows Firewall
   - Try connecting with pgAdmin first

### Application Issues

1. **Database connection errors:**
   - Verify `.env.local` has correct password
   - Check PostgreSQL is running on port 3005

2. **API errors:**
   - Check console for detailed error messages
   - Verify database tables were created

3. **Socket.IO not working:**
   - Make sure port 3002 is available
   - Check firewall settings

## 📁 Project Structure

```
fetchly/
├── src/
│   ├── app/api/          # API endpoints
│   ├── lib/              # Database services & utilities
│   ├── contexts/         # React contexts (updated for PostgreSQL)
│   └── components/       # UI components (unchanged)
├── database/
│   ├── migrations/       # Database schema
│   └── setup.js         # Database setup script
├── .env.local           # Environment configuration
└── server.js            # Custom server with Socket.IO
```

## 🔐 Security Features

- ✅ JWT authentication with refresh tokens
- ✅ bcrypt password hashing  
- ✅ Rate limiting on all endpoints
- ✅ Input validation and sanitization
- ✅ SQL injection protection
- ✅ XSS prevention
- ✅ CORS configuration
- ✅ File upload security

## 📚 API Documentation

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

### Users  
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update profile

### Pets
- `GET /api/pets` - Get user's pets
- `POST /api/pets` - Create new pet

### Bookings
- `GET /api/bookings` - Get user's bookings  
- `POST /api/bookings` - Create booking

## 🎉 Success!

Once everything is set up, you'll have:

- **Full PostgreSQL backend** with comprehensive schema
- **Real-time chat system** with Socket.IO
- **Secure authentication** with JWT tokens
- **Complete API** with all CRUD operations
- **Production-ready security** measures
- **Local development** without Firebase costs

The application will be fully functional and ready for development!
