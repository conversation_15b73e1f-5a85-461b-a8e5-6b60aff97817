'use client';

import Link from 'next/link';
import { Scissors, Stethoscope, Home, Clock, GraduationCap, Heart } from 'lucide-react';

const services = [
  {
    id: 'grooming',
    title: 'Pet Grooming',
    description: 'Professional grooming services to keep your pet looking and feeling their best',
    icon: Scissors,
    color: 'primary',
    href: '/search?service=grooming',
    features: ['Bathing & Brushing', 'Nail Trimming', 'Hair Styling', 'Teeth Cleaning']
  },
  {
    id: 'veterinary',
    title: 'Veterinary Care',
    description: 'Comprehensive health services from routine check-ups to emergency care',
    icon: Stethoscope,
    color: 'secondary',
    href: '/search?service=veterinary',
    features: ['Health Check-ups', 'Vaccinations', 'Surgery', 'Emergency Care']
  },
  {
    id: 'hotel',
    title: 'Pet Hotels',
    description: 'Safe and comfortable accommodations for your pet while you\'re away',
    icon: Home,
    color: 'accent',
    href: '/search?service=hotel',
    features: ['Overnight Stays', 'Play Areas', 'Feeding Service', 'Daily Updates']
  },
  {
    id: 'daycare',
    title: 'Pet Daycare',
    description: 'Daily care and socialization for your pet in a supervised environment',
    icon: Clock,
    color: 'warm',
    href: '/search?service=daycare',
    features: ['Daily Care', 'Socialization', 'Exercise', 'Feeding']
  },
  {
    id: 'training',
    title: 'Pet Training',
    description: 'Professional training services to help your pet learn and grow',
    icon: GraduationCap,
    color: 'primary',
    href: '/search?service=training',
    features: ['Basic Training', 'Behavior Correction', 'Agility Training', 'Puppy Classes']
  },
  {
    id: 'wellness',
    title: 'Pet Wellness',
    description: 'Holistic wellness services including massage, therapy, and nutrition',
    icon: Heart,
    color: 'warm',
    href: '/search?service=wellness',
    features: ['Massage Therapy', 'Nutrition Plans', 'Mental Health', 'Senior Care']
  }
];

const colorClasses = {
  primary: {
    icon: 'text-primary-500',
    iconBg: 'bg-gradient-to-br from-primary-100 to-primary-50',
    hover: 'hover:shadow-lg hover:shadow-primary-500/20'
  },
  secondary: {
    icon: 'text-secondary-500',
    iconBg: 'bg-gradient-to-br from-secondary-100 to-secondary-50',
    hover: 'hover:shadow-lg hover:shadow-secondary-500/20'
  },
  accent: {
    icon: 'text-accent-500',
    iconBg: 'bg-gradient-to-br from-accent-100 to-accent-50',
    hover: 'hover:shadow-lg hover:shadow-accent-500/20'
  },
  warm: {
    icon: 'text-secondary-500', // Using secondary for warm since we removed warm colors
    iconBg: 'bg-gradient-to-br from-secondary-100 to-secondary-50',
    hover: 'hover:shadow-lg hover:shadow-secondary-500/20'
  }
};

export function ServiceTypes() {
  return (
    <section className="py-20 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/30 to-transparent"></div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-text-primary">
            Complete Pet Care Services
          </h2>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto">
            From routine grooming to emergency veterinary care, find trusted professionals
            for every aspect of your pet's wellbeing
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => {
            const Icon = service.icon;
            const colors = colorClasses[service.color as keyof typeof colorClasses];

            return (
              <Link
                key={service.id}
                href={service.href}
                className="group"
              >
                <div className={`
                  glass-card rounded-2xl p-8 h-full ${colors.hover}
                  transition-all duration-300 group-hover:scale-105 group-hover:shadow-xl
                `}>
                  {/* Icon */}
                  <div className={`
                    w-16 h-16 ${colors.iconBg} rounded-2xl flex items-center justify-center mb-6
                    group-hover:scale-110 transition-transform duration-300
                  `}>
                    <Icon className={`w-8 h-8 ${colors.icon}`} />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold mb-3 text-text-primary group-hover:text-text-primary">
                    {service.title}
                  </h3>

                  <p className="text-text-secondary mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features */}
                  <ul className="space-y-2">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-text-secondary">
                        <div className={`w-2 h-2 ${colors.iconBg} rounded-full mr-3`}></div>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* CTA */}
                  <div className="mt-6 pt-6 border-t border-white/30">
                    <span className={`
                      text-sm font-medium ${colors.icon} group-hover:underline
                      transition-all duration-300
                    `}>
                      Find {service.title} →
                    </span>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="glass-card rounded-2xl p-8 max-w-2xl mx-auto relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-secondary-500/5 to-accent-500/5"></div>
            <div className="relative z-10">
              <h3 className="text-2xl font-bold mb-4 text-cool-800">Can't find what you're looking for?</h3>
              <p className="text-cool-600 mb-6">
                We're constantly adding new services and providers. Let us know what you need!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="btn-secondary"
                >
                  Request a Service
                </Link>
                <Link
                  href="/providers/register"
                  className="btn-primary"
                >
                  Become a Provider
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
