'use client';

import Link from 'next/link';

const services = [
  {
    id: 'grooming',
    title: 'Pet Grooming',
    description: 'Professional grooming services to keep your pet looking and feeling their best',
    image: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=300&fit=crop&crop=center',
    color: 'primary',
    href: '/search?service=grooming',
    features: ['Bathing & Brushing', 'Nail Trimming', 'Hair Styling', 'Teeth Cleaning']
  },
  {
    id: 'veterinary',
    title: 'Veterinary Care',
    description: 'Comprehensive health services from routine check-ups to emergency care',
    image: 'https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=400&h=300&fit=crop&crop=center',
    color: 'secondary',
    href: '/search?service=veterinary',
    features: ['Health Check-ups', 'Vaccinations', 'Surgery', 'Emergency Care']
  },
  {
    id: 'hotel',
    title: 'Pet Hotels',
    description: 'Safe and comfortable accommodations for your pet while you\'re away',
    image: 'https://images.unsplash.com/photo-**********-8cc77767d783?w=400&h=300&fit=crop&crop=center',
    color: 'accent',
    href: '/search?service=hotel',
    features: ['Overnight Stays', 'Play Areas', 'Feeding Service', 'Daily Updates']
  },
  {
    id: 'daycare',
    title: 'Pet Daycare',
    description: 'Daily care and socialization for your pet in a supervised environment',
    image: 'https://images.unsplash.com/photo-**********-03cce0bbc87b?w=400&h=300&fit=crop&crop=center',
    color: 'warm',
    href: '/search?service=daycare',
    features: ['Daily Care', 'Socialization', 'Exercise', 'Feeding']
  },
  {
    id: 'training',
    title: 'Pet Training',
    description: 'Professional training services to help your pet learn and grow',
    image: 'https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop&crop=center',
    color: 'primary',
    href: '/search?service=training',
    features: ['Basic Training', 'Behavior Correction', 'Agility Training', 'Puppy Classes']
  },
  {
    id: 'wellness',
    title: 'Pet Wellness',
    description: 'Holistic wellness services including massage, therapy, and nutrition',
    image: 'https://images.unsplash.com/photo-1587300003388-59208cc962cb?w=400&h=300&fit=crop&crop=center',
    color: 'warm',
    href: '/search?service=wellness',
    features: ['Massage Therapy', 'Nutrition Plans', 'Mental Health', 'Senior Care']
  }
];

const colorClasses = {
  primary: {
    icon: 'text-primary-500',
    iconBg: 'bg-gradient-to-br from-primary-100 to-primary-50',
    hover: 'hover:shadow-lg hover:shadow-primary-500/20'
  },
  secondary: {
    icon: 'text-secondary-500',
    iconBg: 'bg-gradient-to-br from-secondary-100 to-secondary-50',
    hover: 'hover:shadow-lg hover:shadow-secondary-500/20'
  },
  accent: {
    icon: 'text-accent-500',
    iconBg: 'bg-gradient-to-br from-accent-100 to-accent-50',
    hover: 'hover:shadow-lg hover:shadow-accent-500/20'
  },
  warm: {
    icon: 'text-secondary-500', // Using secondary for warm since we removed warm colors
    iconBg: 'bg-gradient-to-br from-secondary-100 to-secondary-50',
    hover: 'hover:shadow-lg hover:shadow-secondary-500/20'
  }
};

export function ServiceTypes() {
  return (
    <section className="py-20 relative bg-white">
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">
            Complete Pet Care Services
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From routine grooming to emergency veterinary care, find trusted professionals
            for every aspect of your pet's wellbeing
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => {
            const colors = colorClasses[service.color as keyof typeof colorClasses];

            return (
              <Link
                key={service.id}
                href={service.href}
                className="group"
              >
                <div className={`
                  bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden h-full ${colors.hover}
                  transition-all duration-300 group-hover:scale-105 group-hover:shadow-xl
                `}>
                  {/* Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={service.image}
                      alt={service.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3 text-gray-800">
                      {service.title}
                    </h3>

                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {service.description}
                    </p>

                    {/* Features */}
                    <ul className="space-y-2 mb-6">
                      {service.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm text-gray-600">
                          <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mr-3"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {/* CTA */}
                    <div className="pt-4 border-t border-gray-100">
                      <span className="text-sm font-medium text-green-600 group-hover:text-blue-600 group-hover:underline transition-all duration-300">
                        Find {service.title} →
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-8 max-w-2xl mx-auto text-white">
            <h3 className="text-2xl font-bold mb-4">Can't find what you're looking for?</h3>
            <p className="text-green-100 mb-6">
              We're constantly adding new services and providers. Let us know what you need!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:bg-white/30 transition-all duration-300 font-semibold border border-white/30"
              >
                Request a Service
              </Link>
              <Link
                href="/providers/register"
                className="bg-white text-green-600 px-6 py-3 rounded-xl hover:bg-gray-100 transition-all duration-300 font-semibold"
              >
                Become a Provider
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
