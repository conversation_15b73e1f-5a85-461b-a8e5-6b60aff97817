'use client';

import { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Plus, 
  ArrowUpRight, 
  ArrowDownLeft, 
  DollarSign,
  Smartphone,
  History,
  TrendingUp,
  Wallet
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Transaction, TransactionType } from '@/types/user';
import { TransactionService } from '@/lib/firestore';
import { useAuth } from '@/contexts/AuthContext';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface FetchlyBalanceProps {
  balance: number;
  onBalanceUpdate: (newBalance: number) => void;
}

export default function FetchlyBalance({ balance, onBalanceUpdate }: FetchlyBalanceProps) {
  const { user } = useAuth();
  const [showTopUp, setShowTopUp] = useState(false);
  const [showTransactions, setShowTransactions] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [topUpAmount, setTopUpAmount] = useState('');

  useEffect(() => {
    if (user && showTransactions) {
      loadTransactions();
    }
  }, [user, showTransactions]);

  const loadTransactions = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const userTransactions = await TransactionService.getUserTransactions(user.id, 20);
      setTransactions(userTransactions);
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTopUp = async (amount: number, paymentMethod: 'card' | 'apple_pay' | 'google_pay') => {
    if (!user) return;

    try {
      setLoading(true);
      
      if (paymentMethod === 'card') {
        // Handle Stripe payment
        const stripe = await stripePromise;
        if (!stripe) throw new Error('Stripe not loaded');

        // Create payment intent on your backend
        const response = await fetch('/api/create-payment-intent', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            amount: amount * 100, // Convert to cents
            currency: 'usd',
            userId: user.id,
          }),
        });

        const { clientSecret } = await response.json();

        // Confirm payment
        const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret);

        if (error) {
          throw new Error(error.message);
        }

        if (paymentIntent?.status === 'succeeded') {
          // Add funds to user account
          await TransactionService.addFunds(
            user.id,
            amount,
            'credit_card',
            paymentIntent.id
          );
          
          onBalanceUpdate(balance + amount);
          setShowTopUp(false);
          setTopUpAmount('');
        }
      } else {
        // Handle Apple Pay / Google Pay (simplified for demo)
        await TransactionService.addFunds(user.id, amount, paymentMethod);
        onBalanceUpdate(balance + amount);
        setShowTopUp(false);
        setTopUpAmount('');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      alert('Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getTransactionIcon = (type: TransactionType) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="w-4 h-4 text-green-600" />;
      case 'payment':
        return <ArrowUpRight className="w-4 h-4 text-red-600" />;
      case 'refund':
        return <ArrowDownLeft className="w-4 h-4 text-blue-600" />;
      case 'reward_redemption':
        return <TrendingUp className="w-4 h-4 text-purple-600" />;
      default:
        return <DollarSign className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type: TransactionType) => {
    switch (type) {
      case 'deposit':
      case 'refund':
        return 'text-green-600';
      case 'payment':
        return 'text-red-600';
      case 'reward_redemption':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Wallet className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Fetchly Balance</h3>
              <p className="text-blue-100 text-sm">Your digital wallet</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{formatCurrency(balance)}</div>
            <p className="text-blue-100 text-sm">Available balance</p>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="p-6">
        <div className="grid grid-cols-2 gap-4 mb-6">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowTopUp(true)}
            className="flex items-center justify-center space-x-2 bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-3 px-4 rounded-xl transition-colors"
          >
            <Plus className="w-5 h-5" />
            <span>Top Up</span>
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowTransactions(!showTransactions)}
            className="flex items-center justify-center space-x-2 bg-gray-50 hover:bg-gray-100 text-gray-700 font-medium py-3 px-4 rounded-xl transition-colors"
          >
            <History className="w-5 h-5" />
            <span>History</span>
          </motion.button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="text-green-600 font-semibold text-lg">$127</div>
            <div className="text-green-700 text-xs">This Month</div>
          </div>
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="text-blue-600 font-semibold text-lg">$89</div>
            <div className="text-blue-700 text-xs">Last Month</div>
          </div>
          <div className="p-3 bg-purple-50 rounded-lg">
            <div className="text-purple-600 font-semibold text-lg">$543</div>
            <div className="text-purple-700 text-xs">Total Saved</div>
          </div>
        </div>
      </div>

      {/* Top Up Modal */}
      <AnimatePresence>
        {showTopUp && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowTopUp(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-semibold mb-4">Add Funds</h3>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Amount
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="number"
                    value={topUpAmount}
                    onChange={(e) => setTopUpAmount(e.target.value)}
                    placeholder="0.00"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="space-y-3 mb-6">
                <div className="grid grid-cols-3 gap-2">
                  {[25, 50, 100].map((amount) => (
                    <button
                      key={amount}
                      onClick={() => setTopUpAmount(amount.toString())}
                      className="py-2 px-3 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                      ${amount}
                    </button>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => handleTopUp(parseFloat(topUpAmount), 'card')}
                  disabled={!topUpAmount || loading}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  <CreditCard className="w-5 h-5" />
                  <span>Credit/Debit Card</span>
                </button>
                
                <button
                  onClick={() => handleTopUp(parseFloat(topUpAmount), 'apple_pay')}
                  disabled={!topUpAmount || loading}
                  className="w-full flex items-center justify-center space-x-2 bg-black hover:bg-gray-800 disabled:bg-gray-300 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  <Smartphone className="w-5 h-5" />
                  <span>Apple Pay</span>
                </button>
                
                <button
                  onClick={() => handleTopUp(parseFloat(topUpAmount), 'google_pay')}
                  disabled={!topUpAmount || loading}
                  className="w-full flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  <Smartphone className="w-5 h-5" />
                  <span>Google Pay</span>
                </button>
              </div>

              <button
                onClick={() => setShowTopUp(false)}
                className="w-full mt-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
              >
                Cancel
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Transaction History */}
      <AnimatePresence>
        {showTransactions && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-t border-gray-100"
          >
            <div className="p-6">
              <h4 className="font-semibold text-gray-900 mb-4">Recent Transactions</h4>
              
              {loading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-12 bg-gray-200 rounded-lg"></div>
                    </div>
                  ))}
                </div>
              ) : transactions.length > 0 ? (
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transaction.type)}
                        <div>
                          <div className="font-medium text-gray-900 text-sm">
                            {transaction.description}
                          </div>
                          <div className="text-xs text-gray-500">
                            {transaction.createdAt.toDate().toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                        {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <History className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>No transactions yet</p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
