'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import {
  User, Wallet, Gift, Calendar, Settings, PawPrint, 
  Home, Users, MessageCircle, TrendingUp
} from 'lucide-react';

interface QuickActionsProps {
  currentPage?: string;
}

export default function QuickActions({ currentPage }: QuickActionsProps) {
  const router = useRouter();
  const { user } = useAuth();
  const { stats, pets } = useData();

  const quickActions = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      color: 'text-blue-600',
      path: '/dashboard',
      description: 'Overview & Feed'
    },
    {
      id: 'profile',
      label: 'My Profile',
      icon: User,
      color: 'text-purple-600',
      path: '/profile',
      description: 'Edit Profile & Pets'
    },
    {
      id: 'wallet',
      label: 'Wallet',
      icon: Wallet,
      color: 'text-green-600',
      path: '/wallet',
      description: `$${stats.walletBalance.toFixed(2)} available`
    },
    {
      id: 'rewards',
      label: 'Rewards',
      icon: Gift,
      color: 'text-yellow-600',
      path: '/rewards',
      description: `${stats.rewardPoints.toLocaleString()} points`
    },
    {
      id: 'community',
      label: 'Community',
      icon: Users,
      color: 'text-pink-600',
      path: '/community',
      description: 'Public Posts & Feed'
    },
    {
      id: 'appointments',
      label: 'Appointments',
      icon: Calendar,
      color: 'text-indigo-600',
      path: '/appointments',
      description: `${stats.upcomingAppointments} upcoming`
    }
  ];

  const isCurrentPage = (path: string) => {
    if (currentPage) {
      return currentPage === path.replace('/', '');
    }
    return false;
  };

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="space-y-2">
          {quickActions.map((action) => {
            const Icon = action.icon;
            const isCurrent = isCurrentPage(action.path);
            
            return (
              <button
                key={action.id}
                onClick={() => router.push(action.path)}
                disabled={isCurrent}
                className={`w-full text-left p-3 rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                  isCurrent
                    ? 'bg-blue-50 border-2 border-blue-200 cursor-default'
                    : 'hover:bg-gray-50 border-2 border-transparent hover:border-gray-200'
                }`}
              >
                <Icon className={`w-5 h-5 ${isCurrent ? 'text-blue-600' : action.color}`} />
                <div className="flex-1">
                  <div className={`font-medium ${isCurrent ? 'text-blue-900' : 'text-gray-900'}`}>
                    {action.label}
                  </div>
                  <div className={`text-sm ${isCurrent ? 'text-blue-600' : 'text-gray-500'}`}>
                    {action.description}
                  </div>
                </div>
                {isCurrent && (
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* My Pets Quick View */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">My Pets</h3>
          <button
            onClick={() => router.push('/profile')}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            Manage
          </button>
        </div>
        
        <div className="space-y-3">
          {pets.slice(0, 3).map((pet) => (
            <div key={pet.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                {pet.profileImage ? (
                  <img src={pet.profileImage} alt={pet.name} className="w-full h-full object-cover" />
                ) : (
                  <PawPrint className="w-5 h-5 text-gray-400" />
                )}
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">{pet.name}</p>
                <p className="text-sm text-gray-500 capitalize">{pet.type} • {pet.breed}</p>
              </div>
            </div>
          ))}
          
          {pets.length > 3 && (
            <button 
              onClick={() => router.push('/profile')}
              className="w-full text-center py-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              View all {pets.length} pets
            </button>
          )}
          
          {pets.length === 0 && (
            <div className="text-center py-4">
              <PawPrint className="w-8 h-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No pets added yet</p>
              <button 
                onClick={() => router.push('/profile')}
                className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Add your first pet
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MessageCircle className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-gray-600">Posts</span>
            </div>
            <span className="font-medium text-gray-900">{stats.totalPosts}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-red-600" />
              <span className="text-sm text-gray-600">Total Likes</span>
            </div>
            <span className="font-medium text-gray-900">{stats.totalLikes}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <PawPrint className="w-4 h-4 text-purple-600" />
              <span className="text-sm text-gray-600">Pets</span>
            </div>
            <span className="font-medium text-gray-900">{stats.totalPets}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-green-600" />
              <span className="text-sm text-gray-600">Appointments</span>
            </div>
            <span className="font-medium text-gray-900">{stats.upcomingAppointments}</span>
          </div>
        </div>
      </div>

      {/* Account Actions */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Account</h3>
        <div className="space-y-2">
          <button
            onClick={() => router.push('/settings')}
            className="w-full text-left p-3 rounded-lg hover:bg-gray-50 flex items-center space-x-3"
          >
            <Settings className="w-5 h-5 text-gray-600" />
            <span className="text-gray-700">Settings</span>
          </button>

          <button
            onClick={() => router.push('/profile')}
            className="w-full text-left p-3 rounded-lg hover:bg-gray-50 flex items-center space-x-3"
          >
            <User className="w-5 h-5 text-gray-600" />
            <span className="text-gray-700">Account Info</span>
          </button>
        </div>
      </div>
    </div>
  );
}
