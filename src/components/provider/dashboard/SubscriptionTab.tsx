"use client";

import { Lock } from "lucide-react";
import { useProvider } from "@/contexts/ProviderContext";

export default function SubscriptionTab() {
  const { provider } = useProvider();
  // Example: provider.membershipTier = 'free' | 'pro' | 'trial' | 'expired'
  let status = "No subscription information available.";
  let color = "bg-gray-100 text-gray-800";
  let label = "Unknown";
  if (provider) {
    switch (provider.membershipTier as 'free' | 'pro' | 'trial' | 'expired') {
      case "pro":
        status = "You are a Pro Member. Enjoy all premium features!";
        color = "bg-purple-100 text-purple-800";
        label = "Pro";
        break;
      case "trial":
        status = "You are on a Free Trial. Upgrade to Pro for more benefits.";
        color = "bg-blue-100 text-blue-800";
        label = "Trial";
        break;
      case "expired":
        status = "Your subscription has expired. Please renew to regain access to premium features.";
        color = "bg-red-100 text-red-800";
        label = "Expired";
        break;
      default:
        status = "You are on the Free plan. Upgrade for more features!";
        color = "bg-gray-100 text-gray-800";
        label = "Free";
    }
  }
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-blue-100">
            <Lock className="w-7 h-7 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Subscription Plans</h2>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-6">
          {/* Free Plan */}
          <div className="border rounded-2xl p-6 bg-gray-50">
            <h3 className="text-xl font-bold text-gray-800 mb-2">Free</h3>
            <p className="text-gray-600 mb-4">Start with the basics</p>
            <ul className="space-y-2 text-gray-700 text-sm mb-4">
              <li>✓ Unlimited bookings</li>
              <li>✓ Basic analytics</li>
              <li>✓ Email support</li>
              <li>✓ Standard listing</li>
              <li>✓ Wallet payouts</li>
            </ul>
            <span className="inline-block px-4 py-2 rounded bg-gray-200 text-gray-800 font-semibold">$0/mo</span>
          </div>
          {/* Pro Plan */}
          <div className="border-2 border-blue-500 rounded-2xl p-6 bg-white shadow-md">
            <h3 className="text-xl font-bold text-blue-800 mb-2">Pro</h3>
            <p className="text-gray-600 mb-4">Unlock all premium features</p>
            <ul className="space-y-2 text-gray-700 text-sm mb-4">
              <li>✓ Everything in Free</li>
              <li>✓ Advanced analytics & reports</li>
              <li>✓ Priority support</li>
              <li>✓ Featured listing</li>
              <li>✓ Integrations (coming soon)</li>
              <li>✓ SMS notifications</li>
              <li>✓ Custom branding</li>
            </ul>
            <span className="inline-block px-4 py-2 rounded bg-blue-600 text-white font-semibold">$20/mo</span>
          </div>
        </div>
      </div>
    </div>
  );
}
