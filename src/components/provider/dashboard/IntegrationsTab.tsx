'use client';

import { useState } from 'react';
import { 
  ExternalLink, 
  Settings, 
  Check, 
  Plus,
  Calendar,
  CreditCard,
  MessageSquare,
  Mail,
  Camera,
  MapPin
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: any;
  category: string;
  connected: boolean;
  isPro: boolean;
}

export default function IntegrationsTab() {
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  const integrations: Integration[] = [
    {
      id: 'google-calendar',
      name: 'Google Calendar',
      description: 'Sync your bookings with Google Calendar',
      icon: Calendar,
      category: 'scheduling',
      connected: false,
      isPro: false
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Process payments securely',
      icon: CreditCard,
      category: 'payments',
      connected: true,
      isPro: false
    },
    {
      id: 'mailchimp',
      name: 'Mailchimp',
      description: 'Email marketing automation',
      icon: Mail,
      category: 'marketing',
      connected: false,
      isPro: true
    },
    {
      id: 'zoom',
      name: 'Zoom',
      description: 'Virtual consultations and meetings',
      icon: MessageSquare,
      category: 'communication',
      connected: false,
      isPro: true
    },
    {
      id: 'instagram',
      name: 'Instagram',
      description: 'Share your work on Instagram',
      icon: Camera,
      category: 'social',
      connected: false,
      isPro: true
    },
    {
      id: 'google-maps',
      name: 'Google Maps',
      description: 'Location services and directions',
      icon: MapPin,
      category: 'location',
      connected: true,
      isPro: false
    }
  ];

  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'scheduling', name: 'Scheduling' },
    { id: 'payments', name: 'Payments' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'communication', name: 'Communication' },
    { id: 'social', name: 'Social Media' },
    { id: 'location', name: 'Location' }
  ];

  const filteredIntegrations = integrations.filter(integration => 
    categoryFilter === 'all' || integration.category === categoryFilter
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Integrations</h2>
            <p className="text-gray-600">Connect your favorite tools and services</p>
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setCategoryFilter(category.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                categoryFilter === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Integration Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Connected</p>
              <p className="text-2xl font-bold text-gray-800">
                {integrations.filter(i => i.connected).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Check className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Available</p>
              <p className="text-2xl font-bold text-gray-800">{integrations.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <ExternalLink className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pro Features</p>
              <p className="text-2xl font-bold text-gray-800">
                {integrations.filter(i => i.isPro).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Plus className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Integrations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredIntegrations.map((integration) => (
          <div key={integration.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                  <integration.icon className="w-6 h-6 text-gray-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">{integration.name}</h3>
                  {integration.isPro && (
                    <span className="inline-block px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                      Pro
                    </span>
                  )}
                </div>
              </div>
              
              {integration.connected ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <Check className="w-4 h-4" />
                  <span className="text-sm font-medium">Connected</span>
                </div>
              ) : (
                <button 
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                    integration.isPro 
                      ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                  disabled={integration.isPro}
                >
                  {integration.isPro ? 'Pro Only' : 'Connect'}
                </button>
              )}
            </div>
            
            <p className="text-gray-600 text-sm mb-4">{integration.description}</p>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500 capitalize">{integration.category}</span>
              {integration.connected && (
                <button className="text-gray-400 hover:text-gray-600">
                  <Settings className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Pro Upgrade Banner */}
      <div className="bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl p-6 text-white">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h3 className="text-xl font-bold mb-2">Unlock Pro Integrations</h3>
            <p className="text-purple-100">
              Get access to advanced integrations like Mailchimp, Zoom, and social media tools.
            </p>
          </div>
          <button className="bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            Upgrade to Pro
          </button>
        </div>
      </div>
    </div>
  );
}
