'use client';

import { useState, useMemo } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Users, 
  DollarSign,
  Star,
  Clock,
  Target,
  Activity,
  Filter,
  Download
} from 'lucide-react';
import { Timestamp } from 'firebase/firestore';

export default function AnalyticsTab() {
  const { bookings, earnings, services, isLoading } = useProvider();
  const [timeRange, setTimeRange] = useState<string>('month');

  // Calculate analytics data
  const analyticsData = useMemo(() => {
    if (!bookings || !earnings || (bookings.length === 0 && earnings.length === 0)) return {
      totalBookings: 0,
      completedBookings: 0,
      cancelledBookings: 0,
      totalRevenue: 0,
      averageBookingValue: 0,
      completionRate: 0,
      cancellationRate: 0,
      uniqueCustomers: 0,
      servicePerformance: [],
      dailyData: []
    };

    const now = new Date();
    let startDate: Date;

    switch (timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(0);
    }

    // Filter data by time range
    const filteredBookings = bookings.filter(booking => {
      const bookingDate = booking.date instanceof Timestamp ? booking.date.toDate() : new Date(booking.date);
      return bookingDate >= startDate;
    });

    const filteredEarnings = earnings.filter(earning => {
      const earningDate = earning.date instanceof Timestamp ? earning.date.toDate() : new Date(earning.date);
      return earningDate >= startDate;
    });

    // Calculate metrics
    const totalBookings = filteredBookings.length;
    const completedBookings = filteredBookings.filter(b => b.status === 'completed').length;
    const cancelledBookings = filteredBookings.filter(b => b.status === 'cancelled').length;
    const totalRevenue = filteredEarnings.reduce((sum, earning) => sum + earning.amount, 0);
    const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;
    const completionRate = totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0;
    const cancellationRate = totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0;

    // Unique customers
    const uniqueCustomers = new Set(filteredBookings.map(b => b.customerEmail)).size;

    // Service performance
    const servicePerformance = services?.map(service => {
      const serviceBookings = filteredBookings.filter(b => b.serviceId === service.id);
      const serviceRevenue = serviceBookings.reduce((sum, booking) => sum + booking.totalAmount, 0);
      
      return {
        name: service.name,
        bookings: serviceBookings.length,
        revenue: serviceRevenue,
        averageRating: service.rating || 0
      };
    }).sort((a, b) => b.revenue - a.revenue) || [];

    // Daily data for charts (last 30 days)
    const dailyData = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dayBookings = bookings.filter(booking => {
        const bookingDate = booking.date instanceof Timestamp ? booking.date.toDate() : new Date(booking.date);
        return bookingDate.toDateString() === date.toDateString();
      });
      
      const dayEarnings = earnings.filter(earning => {
        const earningDate = earning.date instanceof Timestamp ? earning.date.toDate() : new Date(earning.date);
        return earningDate.toDateString() === date.toDateString();
      });

      dailyData.push({
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        bookings: dayBookings.length,
        revenue: dayEarnings.reduce((sum, earning) => sum + earning.amount, 0)
      });
    }

    return {
      totalBookings,
      completedBookings,
      cancelledBookings,
      totalRevenue,
      averageBookingValue,
      completionRate,
      cancellationRate,
      uniqueCustomers,
      servicePerformance,
      dailyData
    };
  }, [bookings, earnings, services, timeRange]);

  const getChangeIndicator = (current: number, previous: number) => {
    if (previous === 0) return { value: 0, isPositive: true };
    const change = ((current - previous) / previous) * 100;
    return { value: Math.abs(change), isPositive: change >= 0 };
  };

  if (isLoading || !analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Analytics Dashboard</h2>
            <p className="text-gray-600">Track your business performance and insights</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
            
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Bookings</p>
              <p className="text-2xl font-bold text-gray-800">{analyticsData.totalBookings}</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                <span className="text-sm text-green-600">+12% vs last period</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-800">${analyticsData.totalRevenue.toFixed(0)}</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                <span className="text-sm text-green-600">+8% vs last period</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completion Rate</p>
              <p className="text-2xl font-bold text-gray-800">{analyticsData.completionRate.toFixed(1)}%</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                <span className="text-sm text-green-600">+2% vs last period</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Target className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Unique Customers</p>
              <p className="text-2xl font-bold text-gray-800">{analyticsData.uniqueCustomers}</p>
              <div className="flex items-center mt-1">
                <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                <span className="text-sm text-green-600">+15% vs last period</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <Users className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Revenue Trend</h3>
          <div className="h-64 flex items-end justify-between space-x-2">
            {analyticsData.dailyData.slice(-7).map((day, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div 
                  className="bg-blue-500 rounded-t w-full min-h-[4px] transition-all duration-300 hover:bg-blue-600"
                  style={{ 
                    height: `${Math.max((day.revenue / Math.max(...analyticsData.dailyData.map(d => d.revenue))) * 200, 4)}px` 
                  }}
                ></div>
                <div className="mt-2 text-xs text-gray-600 text-center">
                  <div>{day.date}</div>
                  <div className="font-medium">${day.revenue.toFixed(0)}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bookings Chart */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Booking Trend</h3>
          <div className="h-64 flex items-end justify-between space-x-2">
            {analyticsData.dailyData.slice(-7).map((day, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div 
                  className="bg-green-500 rounded-t w-full min-h-[4px] transition-all duration-300 hover:bg-green-600"
                  style={{ 
                    height: `${Math.max((day.bookings / Math.max(...analyticsData.dailyData.map(d => d.bookings))) * 200, 4)}px` 
                  }}
                ></div>
                <div className="mt-2 text-xs text-gray-600 text-center">
                  <div>{day.date}</div>
                  <div className="font-medium">{day.bookings}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Service Performance */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Service Performance</h3>
          <div className="space-y-4">
            {analyticsData.servicePerformance.slice(0, 5).map((service, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-gray-800">{service.name}</p>
                  <p className="text-sm text-gray-600">{service.bookings} bookings</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-800">${service.revenue.toFixed(0)}</p>
                  <div className="flex items-center">
                    <Star className="w-4 h-4 text-yellow-400 mr-1" />
                    <span className="text-sm text-gray-600">{service.averageRating.toFixed(1)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Key Insights */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Key Insights</h3>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-blue-800">Revenue Growth</p>
                  <p className="text-sm text-blue-700">Your revenue increased by 8% compared to the previous period.</p>
                </div>
              </div>
            </div>

            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Target className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-green-800">High Completion Rate</p>
                  <p className="text-sm text-green-700">You maintain a {analyticsData.completionRate.toFixed(1)}% completion rate, which is excellent!</p>
                </div>
              </div>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-purple-800">Customer Growth</p>
                  <p className="text-sm text-purple-700">You've gained {analyticsData.uniqueCustomers} unique customers this period.</p>
                </div>
              </div>
            </div>

            {analyticsData.cancellationRate > 10 && (
              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Activity className="w-4 h-4 text-yellow-600" />
                  </div>
                  <div>
                    <p className="font-medium text-yellow-800">Cancellation Rate</p>
                    <p className="text-sm text-yellow-700">Your cancellation rate is {analyticsData.cancellationRate.toFixed(1)}%. Consider reviewing your booking policies.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Additional Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <DollarSign className="w-8 h-8 text-blue-600" />
            </div>
            <p className="text-2xl font-bold text-gray-800">{analyticsData.averageBookingValue > 0 ? `$${analyticsData.averageBookingValue.toFixed(0)}` : '--'}</p>
            <p className="text-sm text-gray-600">Average Booking Value</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Clock className="w-8 h-8 text-green-600" />
            </div>
            <p className="text-2xl font-bold text-gray-800">{analyticsData.completedBookings > 0 ? analyticsData.completedBookings : '--'}</p>
            <p className="text-sm text-gray-600">Completed Bookings</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Activity className="w-8 h-8 text-red-600" />
            </div>
            <p className="text-2xl font-bold text-gray-800">{analyticsData.cancelledBookings > 0 ? analyticsData.cancelledBookings : '--'}</p>
            <p className="text-sm text-gray-600">Cancelled Bookings</p>
          </div>
        </div>
      </div>
    </div>
  );
}
