'use client';

import { useState, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  Plus,
  Edit,
  Trash2,
  Clock,
  DollarSign,
  Package,
  Star,
  Eye,
  MoreVertical,
  Search,
  Filter,
  X,
  Upload
} from 'lucide-react';
import { Service } from '@/lib/firebase/providers';
import toast from 'react-hot-toast';

export default function ServicesTab() {
  const { services, isLoading, provider, refreshServices, createService, updateService, deleteService } = useProvider();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [showAddService, setShowAddService] = useState(false);
  const [showEditService, setShowEditService] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<Service | null>(null);

  // Listen for custom event to open add service modal
  useEffect(() => {
    const handleOpenAddServiceModal = () => {
      setShowAddService(true);
    };

    window.addEventListener('openAddServiceModal', handleOpenAddServiceModal);
    return () => {
      window.removeEventListener('openAddServiceModal', handleOpenAddServiceModal);
    };
  }, []);

  // Form state for new service
  const [newService, setNewService] = useState({
    name: '',
    category: '',
    description: '',
    price: 0,
    duration: 60,
    petTypes: [] as string[],
    requirements: [] as string[],
    addOns: [] as { name: string; price: number }[],
    images: [] as string[]
  });

  // Form state for editing service
  const [editService, setEditService] = useState({
    name: '',
    category: '',
    description: '',
    price: 0,
    duration: 60,
    petTypes: [] as string[],
    requirements: [] as string[],
    addOns: [] as { name: string; price: number }[],
    images: [] as string[]
  });

  // Filter services
  const filteredServices = services?.filter(service => {
    const searchMatch = searchTerm === '' || 
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const categoryMatch = categoryFilter === 'all' || service.category === categoryFilter;
    
    return searchMatch && categoryMatch;
  }) || [];

  // Get unique categories
  const categories = Array.from(new Set(services?.map(service => service.category) || []));

  // Predefined service categories for pet services
  const serviceCategories = [
    'Grooming', 'Veterinary', 'Pet Sitting', 'Dog Walking', 'Training',
    'Boarding', 'Pet Taxi', 'Photography', 'Daycare', 'Emergency Care'
  ];

  // Predefined pet types
  const petTypes = ['Dog', 'Cat', 'Bird', 'Fish', 'Rabbit', 'Hamster', 'Guinea Pig', 'Reptile', 'Other'];

  // Handle service creation
  const handleCreateService = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('Creating service - Debug info:', {
      user: user ? { id: user.id, role: user.role, name: user.name } : null,
      provider: provider ? { id: provider.id, businessName: provider.businessName } : null,
      isLoading
    });

    if (!user?.id) {
      toast.error('You must be logged in to create services');
      return;
    }

    if (!provider?.id) {
      console.error('Provider profile not found. User role:', user.role);
      toast.error('Provider profile not found. Please complete your profile setup first.');
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('Attempting to create service with data:', {
        name: newService.name,
        category: newService.category,
        providerId: provider.id
      });

      await createService({
        name: newService.name,
        category: newService.category,
        description: newService.description,
        price: newService.price,
        duration: newService.duration,
        active: true,
        petTypes: newService.petTypes,
        requirements: newService.requirements,
        addOns: newService.addOns,
        images: []
      });

      // Reset form and close modal
      setNewService({
        name: '',
        category: '',
        description: '',
        price: 0,
        duration: 60,
        petTypes: [],
        requirements: [],
        addOns: [],
        images: []
      });
      setShowAddService(false);
    } catch (error) {
      console.error('Error creating service:', error);
      const toast = (await import('react-hot-toast')).default;
      toast.error('Failed to create service');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle editing a service
  const handleEditService = (service: Service) => {
    setEditingService(service);
    setEditService({
      name: service.name,
      category: service.category,
      description: service.description,
      price: service.price,
      duration: service.duration,
      petTypes: service.petTypes || [],
      requirements: service.requirements || [],
      addOns: service.addOns || [],
      images: service.images || []
    });
    setShowEditService(true);
  };

  // Handle updating a service
  const handleUpdateService = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingService) return;

    setIsSubmitting(true);
    try {
      await updateService(editingService.id!, {
        name: editService.name,
        category: editService.category,
        description: editService.description,
        price: editService.price,
        duration: editService.duration,
        petTypes: editService.petTypes,
        requirements: editService.requirements,
        addOns: editService.addOns,
        images: editService.images
      });

      setShowEditService(false);
      setEditingService(null);
    } catch (error) {
      console.error('Error updating service:', error);
      toast.error('Failed to update service');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting a service
  const handleDeleteService = (service: Service) => {
    setServiceToDelete(service);
    setShowDeleteConfirm(true);
  };

  // Confirm delete service
  const confirmDeleteService = async () => {
    if (!serviceToDelete) return;

    try {
      await deleteService(serviceToDelete.id!);
      setShowDeleteConfirm(false);
      setServiceToDelete(null);
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  // Handle image upload for new service
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`${file.name} is too large. Maximum size is 5MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setNewService(prev => ({
          ...prev,
          images: [...prev.images, result]
        }));
      };
      reader.readAsDataURL(file);
    });
  };

  // Handle image upload for edit service
  const handleEditImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`${file.name} is too large. Maximum size is 5MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setEditService(prev => ({
          ...prev,
          images: [...prev.images, result]
        }));
      };
      reader.readAsDataURL(file);
    });
  };

  // Handle adding requirements
  const addRequirement = () => {
    setNewService(prev => ({
      ...prev,
      requirements: [...prev.requirements, '']
    }));
  };

  // Handle removing requirements
  const removeRequirement = (index: number) => {
    setNewService(prev => ({
      ...prev,
      requirements: prev.requirements.filter((_, i) => i !== index)
    }));
  };

  // Handle adding add-ons
  const addAddOn = () => {
    setNewService(prev => ({
      ...prev,
      addOns: [...prev.addOns, { name: '', price: 0 }]
    }));
  };

  // Handle removing add-ons
  const removeAddOn = (index: number) => {
    setNewService(prev => ({
      ...prev,
      addOns: prev.addOns.filter((_, i) => i !== index)
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Services Management</h2>
            <p className="text-gray-600">Manage your service offerings and pricing</p>
          </div>
          
          <button 
            onClick={() => setShowAddService(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Service</span>
          </button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredServices.length === 0 ? (
          <div className="col-span-full bg-white rounded-2xl p-12 shadow-sm border border-gray-100 text-center">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No services found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || categoryFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'Start by adding your first service offering.'}
            </p>
            <button 
              onClick={() => setShowAddService(true)}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Your First Service
            </button>
          </div>
        ) : (
          filteredServices.map((service) => (
            <div key={service.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-800 mb-1">{service.name}</h3>
                  <p className="text-sm text-gray-600">{service.category}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(service.status)}`}>
                    {service.status}
                  </span>
                  <button className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors">
                    <MoreVertical className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <p className="text-gray-700 text-sm mb-4 line-clamp-3">{service.description}</p>
              
              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <DollarSign className="w-4 h-4" />
                    <span>Price</span>
                  </div>
                  <span className="font-semibold text-gray-800">${service.basePrice}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>Duration</span>
                  </div>
                  <span className="font-semibold text-gray-800">{service.duration} min</span>
                </div>
                
                {service.rating && (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Star className="w-4 h-4" />
                      <span>Rating</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="font-semibold text-gray-800">{service.rating}</span>
                      <span className="text-gray-500">({service.reviewCount})</span>
                    </div>
                  </div>
                )}
              </div>
              
              {service.features && service.features.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Features:</p>
                  <div className="flex flex-wrap gap-1">
                    {service.features.slice(0, 3).map((feature, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">
                        {feature}
                      </span>
                    ))}
                    {service.features.length > 3 && (
                      <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded-full">
                        +{service.features.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}
              
              <div className="flex items-center space-x-2 pt-4 border-t border-gray-100">
                <button
                  onClick={() => setSelectedService(service)}
                  className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
                >
                  <Eye className="w-4 h-4" />
                  <span>View</span>
                </button>
                <button
                  onClick={() => handleEditService(service)}
                  className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
                >
                  <Edit className="w-4 h-4" />
                  <span>Edit</span>
                </button>
                <button
                  onClick={() => handleDeleteService(service)}
                  className="bg-red-600 text-white px-3 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Delete</span>
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Service Details Modal */}
      {selectedService && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800">Service Details</h3>
              <button
                onClick={() => setSelectedService(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">{selectedService.name}</h4>
                <p className="text-gray-600">{selectedService.description}</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 className="font-medium text-gray-800 mb-3">Service Information</h5>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Category:</span> {selectedService.category}</p>
                    <p><span className="font-medium">Base Price:</span> ${selectedService.basePrice}</p>
                    <p><span className="font-medium">Duration:</span> {selectedService.duration} minutes</p>
                    <p><span className="font-medium">Status:</span> 
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedService.status)}`}>
                        {selectedService.status}
                      </span>
                    </p>
                  </div>
                </div>
                
                <div>
                  <h5 className="font-medium text-gray-800 mb-3">Performance</h5>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Rating:</span> {selectedService.rating || 'No ratings yet'}</p>
                    <p><span className="font-medium">Reviews:</span> {selectedService.reviewCount || 0}</p>
                    <p><span className="font-medium">Bookings:</span> {selectedService.bookingCount || 0}</p>
                  </div>
                </div>
              </div>
              
              {selectedService.features && selectedService.features.length > 0 && (
                <div>
                  <h5 className="font-medium text-gray-800 mb-3">Features</h5>
                  <div className="flex flex-wrap gap-2">
                    {selectedService.features.map((feature, index) => (
                      <span key={index} className="px-3 py-1 bg-blue-50 text-blue-700 text-sm rounded-full">
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              {selectedService.requirements && selectedService.requirements.length > 0 && (
                <div>
                  <h5 className="font-medium text-gray-800 mb-3">Requirements</h5>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                    {selectedService.requirements.map((requirement, index) => (
                      <li key={index}>{requirement}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setSelectedService(null)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Close
                </button>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Edit Service
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Service Modal */}
      {showAddService && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800">Add New Service</h3>
              <button
                onClick={() => setShowAddService(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <form onSubmit={handleCreateService} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Service Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={newService.name}
                    onChange={(e) => setNewService(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Full Service Grooming"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    required
                    value={newService.category}
                    onChange={(e) => setNewService(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a category</option>
                    {serviceCategories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  required
                  rows={3}
                  value={newService.description}
                  onChange={(e) => setNewService(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe your service in detail..."
                />
              </div>

              {/* Price and Duration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price ($) *
                  </label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="0.01"
                    value={newService.price}
                    onChange={(e) => setNewService(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes) *
                  </label>
                  <input
                    type="number"
                    required
                    min="15"
                    step="15"
                    value={newService.duration}
                    onChange={(e) => setNewService(prev => ({ ...prev, duration: parseInt(e.target.value) || 60 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="60"
                  />
                </div>
              </div>

              {/* Pet Types */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pet Types
                </label>
                <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                  {petTypes.map(petType => (
                    <label key={petType} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={newService.petTypes.includes(petType)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewService(prev => ({
                              ...prev,
                              petTypes: [...prev.petTypes, petType]
                            }));
                          } else {
                            setNewService(prev => ({
                              ...prev,
                              petTypes: prev.petTypes.filter(type => type !== petType)
                            }));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{petType}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Service Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Images
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">Upload service images</p>
                  <p className="text-xs text-gray-500">PNG, JPG up to 5MB each</p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    id="service-images"
                    onChange={handleImageUpload}
                  />
                  <label
                    htmlFor="service-images"
                    className="mt-2 inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 cursor-pointer"
                  >
                    Choose Images
                  </label>
                </div>
                {newService.images.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {newService.images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Service ${index + 1}`}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const newImages = newService.images.filter((_, i) => i !== index);
                            setNewService(prev => ({ ...prev, images: newImages }));
                          }}
                          className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Requirements */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Requirements
                  </label>
                  <button
                    type="button"
                    onClick={addRequirement}
                    className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Requirement</span>
                  </button>
                </div>
                <div className="space-y-2">
                  {newService.requirements.map((requirement, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={requirement}
                        onChange={(e) => {
                          const newRequirements = [...newService.requirements];
                          newRequirements[index] = e.target.value;
                          setNewService(prev => ({ ...prev, requirements: newRequirements }));
                        }}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., Pet must be up to date on vaccinations"
                      />
                      <button
                        type="button"
                        onClick={() => removeRequirement(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Add-ons */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Add-ons (Optional)
                  </label>
                  <button
                    type="button"
                    onClick={addAddOn}
                    className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Add-on</span>
                  </button>
                </div>
                <div className="space-y-2">
                  {newService.addOns.map((addOn, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={addOn.name}
                        onChange={(e) => {
                          const newAddOns = [...newService.addOns];
                          newAddOns[index].name = e.target.value;
                          setNewService(prev => ({ ...prev, addOns: newAddOns }));
                        }}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Add-on name"
                      />
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={addOn.price}
                        onChange={(e) => {
                          const newAddOns = [...newService.addOns];
                          newAddOns[index].price = parseFloat(e.target.value) || 0;
                          setNewService(prev => ({ ...prev, addOns: newAddOns }));
                        }}
                        className="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                      <button
                        type="button"
                        onClick={() => removeAddOn(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowAddService(false)}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Creating...</span>
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4" />
                      <span>Create Service</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Service Modal */}
      {showEditService && editingService && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800">Edit Service</h3>
              <button
                onClick={() => setShowEditService(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form onSubmit={handleUpdateService} className="space-y-6">
              {/* Service Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Name *
                </label>
                <input
                  type="text"
                  required
                  value={editService.name}
                  onChange={(e) => setEditService(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Dog Walking, Pet Grooming"
                />
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  required
                  value={editService.category}
                  onChange={(e) => setEditService(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a category</option>
                  <option value="Pet Sitting">Pet Sitting</option>
                  <option value="Dog Walking">Dog Walking</option>
                  <option value="Pet Grooming">Pet Grooming</option>
                  <option value="Veterinary">Veterinary</option>
                  <option value="Pet Training">Pet Training</option>
                  <option value="Pet Boarding">Pet Boarding</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  required
                  rows={4}
                  value={editService.description}
                  onChange={(e) => setEditService(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe your service in detail..."
                />
              </div>

              {/* Price and Duration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base Price ($) *
                  </label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="0.01"
                    value={editService.price}
                    onChange={(e) => setEditService(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes) *
                  </label>
                  <input
                    type="number"
                    required
                    min="15"
                    step="15"
                    value={editService.duration}
                    onChange={(e) => setEditService(prev => ({ ...prev, duration: parseInt(e.target.value) || 60 }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="60"
                  />
                </div>
              </div>

              {/* Service Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service Images
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">Upload service images</p>
                  <p className="text-xs text-gray-500">PNG, JPG up to 5MB each</p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    id="edit-service-images"
                    onChange={handleEditImageUpload}
                  />
                  <label
                    htmlFor="edit-service-images"
                    className="mt-2 inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 cursor-pointer"
                  >
                    Choose Images
                  </label>
                </div>
                {editService.images.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {editService.images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Service ${index + 1}`}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const newImages = editService.images.filter((_, i) => i !== index);
                            setEditService(prev => ({ ...prev, images: newImages }));
                          }}
                          className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowEditService(false)}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Updating...</span>
                    </>
                  ) : (
                    <>
                      <Edit className="w-4 h-4" />
                      <span>Update Service</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && serviceToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <Trash2 className="w-6 h-6 text-red-600" />
              </div>
            </div>

            <h3 className="text-lg font-bold text-gray-900 text-center mb-2">
              Delete Service
            </h3>

            <p className="text-gray-600 text-center mb-6">
              Are you sure you want to delete "{serviceToDelete.name}"? This action cannot be undone.
            </p>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteService}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
