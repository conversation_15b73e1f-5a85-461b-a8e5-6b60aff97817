'use client';

import { useState, useMemo } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { useData } from '@/contexts/DataContext';
import {
  Star,
  ThumbsUp,
  MessageCircle,
  Filter,
  Search,
  TrendingUp,
  Award,
  Users,
  Calendar,
  Share2
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Review {
  id: string;
  customerName: string;
  customerEmail: string;
  rating: number;
  comment: string;
  date: Date;
  serviceId: string;
  serviceName: string;
  bookingId: string;
  helpful: number;
  response?: string;
  responseDate?: Date;
}

export default function ReviewsTab() {
  const { isLoading } = useProvider();
  const { createPost } = useData();
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('newest');
  const [sharingReview, setSharingReview] = useState<string | null>(null);

  // Real reviews data - will come from Firebase when clients leave reviews
  const reviews: Review[] = [];

  // Filter and sort reviews
  const filteredReviews = useMemo(() => {
    let filtered = reviews.filter(review => {
      const searchMatch = searchTerm === '' || 
        review.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.comment.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.serviceName.toLowerCase().includes(searchTerm.toLowerCase());

      const ratingMatch = ratingFilter === 'all' || review.rating.toString() === ratingFilter;

      return searchMatch && ratingMatch;
    });

    // Sort reviews
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return b.date.getTime() - a.date.getTime();
        case 'oldest':
          return a.date.getTime() - b.date.getTime();
        case 'highest':
          return b.rating - a.rating;
        case 'lowest':
          return a.rating - b.rating;
        case 'helpful':
          return b.helpful - a.helpful;
        default:
          return 0;
      }
    });

    return filtered;
  }, [reviews, searchTerm, ratingFilter, sortBy]);

  // Calculate review stats
  const reviewStats = useMemo(() => {
    const totalReviews = reviews.length;
    const averageRating = totalReviews > 0 
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews 
      : 0;
    
    const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
      rating,
      count: reviews.filter(review => review.rating === rating).length,
      percentage: totalReviews > 0 
        ? (reviews.filter(review => review.rating === rating).length / totalReviews) * 100 
        : 0
    }));

    const responseRate = totalReviews > 0 
      ? (reviews.filter(review => review.response).length / totalReviews) * 100 
      : 0;

    return {
      totalReviews,
      averageRating,
      ratingDistribution,
      responseRate
    };
  }, [reviews]);

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6'
    };

    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle sharing good reviews to community feed
  const handleShareToCommunity = async (review: Review) => {
    if (review.rating < 4) {
      toast.error('Only 4-5 star reviews can be shared to the community');
      return;
    }

    setSharingReview(review.id);
    try {
      const postContent = `🌟 Amazing review from ${review.customerName}!\n\n"${review.comment}"\n\n⭐ ${review.rating}/5 stars for ${review.serviceName}\n\n#CustomerReview #PetCare #Fetchly`;

      await createPost(postContent, '', true, false); // public post, not a story
      toast.success('Review shared to community feed!');
    } catch (error) {
      console.error('Error sharing review:', error);
      toast.error('Failed to share review to community');
    } finally {
      setSharingReview(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Reviews & Ratings</h2>
            <p className="text-gray-600">Manage customer feedback and build your reputation</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search reviews by customer, service, or comment..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={ratingFilter}
            onChange={(e) => setRatingFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="highest">Highest Rating</option>
            <option value="lowest">Lowest Rating</option>
            <option value="helpful">Most Helpful</option>
          </select>
        </div>
      </div>

      {/* Review Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Reviews</p>
              <p className="text-2xl font-bold text-gray-800">{reviewStats.totalReviews}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <MessageCircle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Rating</p>
              <div className="flex items-center space-x-2">
                <p className="text-2xl font-bold text-gray-800">{reviewStats.averageRating.toFixed(1)}</p>
                {renderStars(Math.round(reviewStats.averageRating), 'sm')}
              </div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Response Rate</p>
              <p className="text-2xl font-bold text-gray-800">{reviewStats.responseRate.toFixed(0)}%</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <ThumbsUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">5-Star Reviews</p>
              <p className="text-2xl font-bold text-gray-800">
                {reviewStats.ratingDistribution.find(r => r.rating === 5)?.count || 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Award className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Rating Distribution */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Rating Distribution</h3>
        <div className="space-y-3">
          {reviewStats.ratingDistribution.map((item) => (
            <div key={item.rating} className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 w-20">
                <span className="text-sm font-medium text-gray-700">{item.rating}</span>
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${item.percentage}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-600 w-12">{item.count}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
        {filteredReviews.length === 0 ? (
          <div className="text-center py-12">
            <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No reviews found</h3>
            <p className="text-gray-600">
              {searchTerm || ratingFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'Your customer reviews will appear here once you start receiving feedback.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredReviews.map((review) => (
              <div key={review.id} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                      <Users className="w-6 h-6 text-gray-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800">{review.customerName}</h4>
                      <p className="text-sm text-gray-600">{review.serviceName}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        {renderStars(review.rating, 'sm')}
                        <span className="text-sm text-gray-500">•</span>
                        <span className="text-sm text-gray-500">{formatDate(review.date)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <ThumbsUp className="w-4 h-4" />
                      <span>{review.helpful}</span>
                    </div>

                    {/* Share to Community button for good reviews */}
                    {review.rating >= 4 && (
                      <button
                        onClick={() => handleShareToCommunity(review)}
                        disabled={sharingReview === review.id}
                        className="flex items-center space-x-1 px-2 py-1 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                      >
                        <Share2 className="w-4 h-4" />
                        <span>{sharingReview === review.id ? 'Sharing...' : 'Share'}</span>
                      </button>
                    )}
                  </div>
                </div>
                
                <div className="ml-16">
                  <p className="text-gray-700 mb-4">{review.comment}</p>
                  
                  {review.response ? (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm font-medium text-blue-800">Your Response</span>
                        <span className="text-xs text-blue-600">
                          {review.responseDate && formatDate(review.responseDate)}
                        </span>
                      </div>
                      <p className="text-sm text-blue-700">{review.response}</p>
                    </div>
                  ) : (
                    <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                      Respond to Review
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
