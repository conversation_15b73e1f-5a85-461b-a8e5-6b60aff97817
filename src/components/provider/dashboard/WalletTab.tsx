'use client';

import { useState, useMemo, useEffect } from 'react';
import { useProvider } from '@/contexts/ProviderContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  CreditCard,
  Wallet,
  ArrowUpRight,
  ArrowDownLeft,
  Filter,
  Search,
  Eye,
  ExternalLink,
  Plus,
  Settings
} from 'lucide-react';
import { Earning, Payout } from '@/lib/firebase/providers';
import { Timestamp } from 'firebase/firestore';
import PlaidLink, { BankAccountDisplay, PayoutForm } from '@/components/plaid/PlaidLink';
import { getBankAccounts, initiatePayout, BankAccount } from '@/lib/plaid';
import toast from 'react-hot-toast';

export default function WalletTab() {
  const { earnings, payouts, isLoading } = useProvider();
  const { user } = useAuth();
  const [timeFilter, setTimeFilter] = useState<string>('month');
  const [transactionFilter, setTransactionFilter] = useState<string>('all');
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [showPayoutForm, setShowPayoutForm] = useState(false);
  const [isLoadingBankAccounts, setIsLoadingBankAccounts] = useState(false);
  const [isProcessingPayout, setIsProcessingPayout] = useState(false);

  // Load bank accounts on component mount
  useEffect(() => {
    const loadBankAccounts = async () => {
      if (!user?.uid) return;

      try {
        setIsLoadingBankAccounts(true);
        const accounts = await getBankAccounts(user.uid);
        setBankAccounts(accounts);
      } catch (error) {
        console.error('Error loading bank accounts:', error);
      } finally {
        setIsLoadingBankAccounts(false);
      }
    };

    loadBankAccounts();
  }, [user?.uid]);

  // Handle successful bank account connection
  const handleBankAccountSuccess = (accounts: BankAccount[]) => {
    setBankAccounts(prev => [...prev, ...accounts]);
    toast.success('Bank account connected successfully!');
  };

  // Handle payout request
  const handlePayout = async (accountId: string, amount: number) => {
    if (!user?.uid) return;

    try {
      setIsProcessingPayout(true);
      const result = await initiatePayout(user.uid, accountId, amount, 'Provider earnings payout');

      if (result.success) {
        toast.success('Payout initiated successfully!');
        setShowPayoutForm(false);
        // Refresh payouts data here if needed
      } else {
        toast.error(result.error || 'Failed to initiate payout');
      }
    } catch (error) {
      console.error('Error initiating payout:', error);
      toast.error('Failed to initiate payout');
    } finally {
      setIsProcessingPayout(false);
    }
  };

  // Calculate wallet stats
  const walletStats = useMemo(() => {
    const totalEarnings = earnings?.reduce((sum, earning) => sum + earning.netAmount, 0) || 0;
    const totalPayouts = payouts?.reduce((sum, payout) => sum + payout.totalAmount, 0) || 0;
    const pendingEarnings = earnings?.filter(e => e.status === 'pending').reduce((sum, earning) => sum + earning.netAmount, 0) || 0;
    const availableBalance = totalEarnings - totalPayouts - pendingEarnings;

    // Calculate this month's earnings
    const now = new Date();
    const thisMonth = earnings?.filter(earning => {
      const earningDate = earning.date instanceof Timestamp ? earning.date.toDate() : new Date(earning.date);
      return earningDate.getMonth() === now.getMonth() && earningDate.getFullYear() === now.getFullYear();
    }).reduce((sum, earning) => sum + earning.amount, 0) || 0;

    // Calculate last month's earnings for comparison
    const lastMonth = earnings?.filter(earning => {
      const earningDate = earning.date instanceof Timestamp ? earning.date.toDate() : new Date(earning.date);
      const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      return earningDate.getMonth() === lastMonthDate.getMonth() && earningDate.getFullYear() === lastMonthDate.getFullYear();
    }).reduce((sum, earning) => sum + earning.amount, 0) || 0;

    const monthlyGrowth = lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0;

    return {
      totalEarnings,
      totalPayouts,
      pendingEarnings,
      availableBalance,
      thisMonth,
      monthlyGrowth
    };
  }, [earnings, payouts]);

  // Filter transactions
  const filteredTransactions = useMemo(() => {
    const allTransactions = [
      ...(earnings?.map(earning => ({ ...earning, type: 'earning' })) || []),
      ...(payouts?.map(payout => ({ ...payout, type: 'payout' })) || [])
    ];

    return allTransactions
      .filter(transaction => {
        if (transactionFilter === 'all') return true;
        return transaction.type === transactionFilter;
      })
      .sort((a, b) => {
        const dateA = a.date instanceof Timestamp ? a.date.toDate() : new Date(a.date);
        const dateB = b.date instanceof Timestamp ? b.date.toDate() : new Date(b.date);
        return dateB.getTime() - dateA.getTime();
      });
  }, [earnings, payouts, transactionFilter]);

  const formatDate = (date: any) => {
    if (!date) return 'No date';
    const dateObj = date instanceof Timestamp ? date.toDate() : new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTransactionIcon = (type: string, status: string) => {
    if (type === 'earning') {
      return <ArrowUpRight className="w-4 h-4 text-green-600" />;
    } else {
      return <ArrowDownLeft className="w-4 h-4 text-blue-600" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Wallet Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Available Balance</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.availableBalance.toFixed(2)}</p>
              <p className="text-sm text-green-600 mt-1">Ready to withdraw</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <Wallet className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Earnings</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.totalEarnings.toFixed(2)}</p>
              <p className="text-sm text-blue-600 mt-1">All time</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.thisMonth.toFixed(2)}</p>
              <div className="flex items-center mt-1">
                {walletStats.monthlyGrowth >= 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm ${walletStats.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(walletStats.monthlyGrowth).toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-800">${walletStats.pendingEarnings.toFixed(2)}</p>
              <p className="text-sm text-yellow-600 mt-1">Processing</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <CreditCard className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => setShowPayoutForm(true)}
            className="bg-blue-600 text-white p-4 rounded-xl hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
          >
            <Download className="w-5 h-5" />
            <span>Withdraw</span>
          </button>
          <button className="bg-gray-100 text-gray-700 p-4 rounded-xl hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2">
            <Download className="w-5 h-5" />
            <span>Download Statement</span>
          </button>
          <button className="bg-gray-100 text-gray-700 p-4 rounded-xl hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2">
            <ExternalLink className="w-5 h-5" />
            <span>Tax Documents</span>
          </button>
        </div>
      </div>

      {/* Transaction History */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800">Transaction History</h3>
            <p className="text-gray-600">Track your earnings and payouts</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={transactionFilter}
              onChange={(e) => setTransactionFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Transactions</option>
              <option value="earning">Earnings</option>
              <option value="payout">Payouts</option>
            </select>
            
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>

        {filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <Wallet className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-gray-800 mb-2">No transactions found</h4>
            <p className="text-gray-600">
              {transactionFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'Your transaction history will appear here once you start earning.'}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    {getTransactionIcon(transaction.type, transaction.status)}
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-800">
                      {transaction.type === 'earning' ? 'Service Payment' : 'Payout'}
                    </p>
                    <p className="text-sm text-gray-600">
                      {transaction.type === 'earning' 
                        ? `From ${(transaction as any).customerName || 'Customer'}`
                        : `To ${(transaction as any).bankAccount || 'Bank Account'}`
                      }
                    </p>
                    <p className="text-xs text-gray-500">{formatDate(transaction.date)}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className={`font-semibold ${
                    transaction.type === 'earning' ? 'text-green-600' : 'text-blue-600'
                  }`}>
                    {transaction.type === 'earning' ? '+' : '-'}${transaction.amount.toFixed(2)}
                  </p>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(transaction.status)}`}>
                    {transaction.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Payout Settings */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Payout Settings</h3>
          <div className="flex items-center space-x-3">
            {bankAccounts.length > 0 && (
              <button
                onClick={() => setShowPayoutForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <ArrowUpRight className="w-4 h-4" />
                <span>Withdraw</span>
              </button>
            )}
            {user?.uid && (
              <PlaidLink
                userId={user.uid}
                onSuccess={handleBankAccountSuccess}
                className="flex items-center space-x-2 px-4 py-2"
              >
                <Plus className="w-4 h-4" />
                <span>Add Bank Account</span>
              </PlaidLink>
            )}
          </div>
        </div>

        {/* Bank Accounts */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-800">Connected Bank Accounts</h4>

          {isLoadingBankAccounts ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : bankAccounts.length > 0 ? (
            <div className="space-y-3">
              {bankAccounts.map((account) => (
                <BankAccountDisplay
                  key={account.id}
                  account={account}
                  isPrimary={account.is_primary}
                  onSetPrimary={(accountId) => {
                    // Handle setting primary account
                    setBankAccounts(prev =>
                      prev.map(acc => ({
                        ...acc,
                        is_primary: acc.id === accountId
                      }))
                    );
                  }}
                  onRemove={(accountId) => {
                    // Handle removing account
                    setBankAccounts(prev => prev.filter(acc => acc.id !== accountId));
                  }}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <CreditCard className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-600 mb-4">No bank accounts connected</p>
              <p className="text-sm text-gray-500 mb-4">
                Connect a bank account to receive payouts from your earnings
              </p>
              {user?.uid && (
                <PlaidLink
                  userId={user.uid}
                  onSuccess={handleBankAccountSuccess}
                  className="mx-auto"
                />
              )}
            </div>
          )}
        </div>

        {/* Payout Schedule */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="font-medium text-gray-800 mb-3">Payout Schedule</h4>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-800">Weekly Payouts</p>
                <p className="text-sm text-gray-600">Every Friday at 12:00 PM EST</p>
                <p className="text-xs text-gray-500 mt-1">
                  Minimum payout amount: $25.00
                </p>
              </div>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1">
                <Settings className="w-4 h-4" />
                <span>Change</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Payout Form Modal */}
      {showPayoutForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-800">Withdraw Funds</h3>
              <button
                onClick={() => setShowPayoutForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <div className="mb-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                Available Balance: <span className="font-semibold">${walletStats.availableBalance.toFixed(2)}</span>
              </p>
            </div>

            <PayoutForm
              accounts={bankAccounts}
              availableBalance={walletStats.availableBalance}
              onPayout={handlePayout}
              isLoading={isProcessingPayout}
            />
          </div>
        </div>
      )}
    </div>
  );
}
