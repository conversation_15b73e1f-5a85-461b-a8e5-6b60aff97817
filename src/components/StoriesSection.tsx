'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Plus, Play, X, Camera, Type, Upload, Pause, ChevronLeft, ChevronRight } from 'lucide-react';
import NoSSR from './NoSSR';
import toast from 'react-hot-toast';

interface Story {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  image?: string;
  timestamp: Date;
  expiresAt: Date;
  isStory: boolean;
}

interface UserStories {
  userId: string;
  stories: Story[];
  latestStory: Story;
  totalStories: number;
}

interface StoriesSectionProps {
  onCreateStory?: () => void;
}

export default function StoriesSection({ onCreateStory }: StoriesSectionProps) {
  const { user } = useAuth();
  const { createPost } = useData();
  const [userStories, setUserStories] = useState<UserStories[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUserStories, setSelectedUserStories] = useState<UserStories | null>(null);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [countdown, setCountdown] = useState(10);
  const [isPaused, setIsPaused] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showCreateStory, setShowCreateStory] = useState(false);
  const [storyContent, setStoryContent] = useState('');
  const [storyImage, setStoryImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
const [showEnlargedImage, setShowEnlargedImage] = useState(false);

  // Load active stories (not expired)
  useEffect(() => {
    const now = new Date();
    console.log('🔍 Loading stories, current time:', now);

    // First, let's load all stories to debug
    const storiesQuery = query(
      collection(db, 'posts'),
      where('isStory', '==', true),
      orderBy('timestamp', 'desc')
    );

    const unsubscribe = onSnapshot(storiesQuery, (snapshot) => {
      console.log('📱 Stories snapshot received, docs count:', snapshot.docs.length);

      const storiesData = snapshot.docs.map(doc => {
        const data = doc.data();
        console.log('📱 Story data:', { id: doc.id, ...data });
        return {
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date()
        };
      }) as Story[];

      console.log('📱 Processed stories:', storiesData);

      // Filter out expired stories
      const now = new Date();
      const activeStories = storiesData.filter(story => {
        const isActive = story.expiresAt > now;
        console.log(`📱 Story ${story.id} expires at ${story.expiresAt}, active: ${isActive}`);
        return isActive;
      });

      // Group stories by user (keep all stories per user, sorted by timestamp)
      const userStoriesMap = new Map();
      activeStories.forEach(story => {
        if (!userStoriesMap.has(story.userId)) {
          userStoriesMap.set(story.userId, []);
        }
        userStoriesMap.get(story.userId).push(story);
      });

      // Sort stories within each user group by timestamp (oldest first)
      userStoriesMap.forEach((stories, userId) => {
        stories.sort((a: Story, b: Story) => a.timestamp.getTime() - b.timestamp.getTime());
      });

      // Create user story objects with latest story for display
      const finalStories = Array.from(userStoriesMap.entries()).map(([userId, stories]) => ({
        userId,
        stories: stories,
        latestStory: stories[stories.length - 1], // Most recent for display
        totalStories: stories.length
      }));
      console.log('📱 Final user stories to display:', finalStories);

      setUserStories(finalStories);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleStoryClick = (userStory: UserStories) => {
    setSelectedUserStories(userStory);
    setCurrentStoryIndex(0);
    setCountdown(10);
    setIsPaused(false);
    setShowModal(true);
  };

  // Handle image upload for story
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setStoryImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Create story
  const handleCreateStory = async () => {
    if (!user || (!storyContent.trim() && !storyImage)) {
      toast.error('Please add some content or an image to your story');
      return;
    }

    setIsSubmitting(true);
    try {
      await createPost(
        storyContent.trim() || 'Story', // Default content if only image
        storyImage || undefined,
        true, // isPublic
        true  // isStory
      );

      toast.success('Story created successfully!');
      setShowCreateStory(false);
      setStoryContent('');
      setStoryImage(null);
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to create story');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Carousel and countdown logic
  useEffect(() => {
    if (!showModal || !selectedUserStories || isPaused) return;

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          goToNextStory();
          return 10;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [showModal, selectedUserStories, isPaused]);

  const closeModal = () => {
    setShowModal(false);
    setSelectedUserStories(null);
    setCurrentStoryIndex(0);
    setCountdown(10);
    setIsPaused(false);
  };

  const goToNextStory = () => {
    if (!selectedUserStories) return;

    if (currentStoryIndex < selectedUserStories.stories.length - 1) {
      setCurrentStoryIndex(prev => prev + 1);
      setCountdown(10);
    } else {
      // Only close if truly at the last story
      closeModal();
    }
  };

  const goToPrevStory = () => {
    if (currentStoryIndex > 0) {
      setCurrentStoryIndex(prev => prev - 1);
      setCountdown(10);
    }
  };

  const togglePause = () => {
    setIsPaused(prev => !prev);
  };

  const getTimeRemaining = (expiresAt: Date) => {
    const now = new Date();
    const diff = expiresAt.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else {
      return 'Expiring';
    }
  };

  if (loading) {
    return (
      <div className="glass-card rounded-xl p-4 mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="glass-card rounded-xl p-4 mb-6">
      <div className="flex items-center space-x-4 overflow-x-auto pb-2">
        {/* Add Story Button */}
        {user && (
          <div className="flex-shrink-0 text-center">
            <button
              onClick={() => setShowCreateStory(true)}
              className="relative w-16 h-16 rounded-full border-2 border-dashed border-blue-400 hover:border-blue-600 transition-colors flex items-center justify-center group"
            >
              <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Plus className="w-6 h-6 text-white" />
              </div>
            </button>
            <p className="text-xs text-gray-600 mt-1 font-medium">Your Story</p>
          </div>
        )}

        {/* Stories */}
        {userStories.map((userStory) => (
          <div key={userStory.userId} className="flex-shrink-0 text-center">
            <button
              className="relative group"
              onClick={() => handleStoryClick(userStory)}
            >
              {/* Story Ring */}
              <div className="w-16 h-16 rounded-full bg-gradient-to-tr from-pink-500 via-red-500 to-yellow-500 p-0.5">
                <div className="w-full h-full rounded-full bg-white p-0.5">
                  <img
                    src={userStory.latestStory.userAvatar || '/favicon.png'}
                    alt={userStory.latestStory.userName}
                    className="w-full h-full rounded-full object-cover"
                  />
                </div>
              </div>

              {/* Multiple Stories Indicator */}
              {userStory.totalStories > 1 && (
                <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {userStory.totalStories}
                </div>
              )}

              {/* Play Icon Overlay */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="w-6 h-6 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                  <Play className="w-3 h-3 text-white fill-current" />
                </div>
              </div>

              {/* Time Remaining */}
              <div className="absolute -bottom-1 -right-1 bg-blue-600 text-white text-xs px-1 py-0.5 rounded-full">
                <NoSSR fallback="24h">
                  {getTimeRemaining(userStory.latestStory.expiresAt)}
                </NoSSR>
              </div>
            </button>

            <p className="text-xs text-gray-600 mt-1 font-medium truncate w-16">
              {userStory.userId === user?.id ? 'You' : userStory.latestStory.userName}
            </p>
          </div>
        ))}

        {/* No Stories Message */}
        {userStories.length === 0 && !user && (
          <div className="flex-1 text-center py-8">
            <div className="text-gray-500">
              <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-3 flex items-center justify-center">
                <Play className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-sm font-medium">No active stories</p>
              <p className="text-xs text-gray-400 mt-1">Stories appear here for 24 hours</p>
            </div>
          </div>
        )}

        {(!userStories.find(us => us.userId === user?.id) || userStories.find(us => us.userId === user?.id)?.stories.length === 0) && user && (
          <div className="flex-1 text-center py-4">
            <p className="text-sm text-gray-500">Be the first to share a story!</p>
          </div>
        )}
      </div>

      {/* Story Carousel Modal */}
      {showModal && selectedUserStories && selectedUserStories.stories[currentStoryIndex] && (
        <div className="fixed inset-0 z-50">
          {/* Overlay */}
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40" />
          {/* Modal Content */}
          <div className="fixed inset-0 flex items-center justify-center">
            <div className="relative max-w-md w-full mx-auto">
              {/* Progress Bars */}
              <div className="absolute top-4 left-4 right-4 z-20 flex space-x-1">
                {selectedUserStories.stories.map((_, index) => (
                  <div key={index} className="flex-1 h-1 bg-white bg-opacity-30 rounded-full overflow-hidden">
                    <div
                      className={`h-full bg-white ${index < currentStoryIndex ? 'w-full' : index === currentStoryIndex ? 'progress-bar-anim' : 'w-0'}`}
                      style={index === currentStoryIndex ? {
                        width: '100%',
                        transition: `width ${countdown === 10 ? 10000 : (countdown * 1000)}ms linear`,
                      } : index < currentStoryIndex ? { width: '100%' } : { width: 0 }}
                    />
                  </div>
                ))}
              </div>

              {/* Story Controls */}
              <div className="absolute top-4 right-4 z-20 flex space-x-2">
                {/* Pause/Play Button */}
                <button
                  onClick={togglePause}
                  className="w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white hover:bg-opacity-75 transition-colors"
                >
                  {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                </button>

                {/* Close Button */}
                <button
                  onClick={closeModal}
                  className="w-8 h-8 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white hover:bg-opacity-75 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {/* Navigation Areas */}
              <button
                onClick={goToPrevStory}
                className="absolute left-0 top-0 bottom-0 w-1/3 z-10 flex items-center justify-start pl-4 text-white opacity-0 hover:opacity-100 transition-opacity"
                disabled={currentStoryIndex === 0}
              >
                {currentStoryIndex > 0 && <ChevronLeft className="w-8 h-8" />}
              </button>

              <button
                onClick={goToNextStory}
                className="absolute right-0 top-0 bottom-0 w-1/3 z-10 flex items-center justify-end pr-4 text-white opacity-0 hover:opacity-100 transition-opacity"
              >
                <ChevronRight className="w-8 h-8" />
              </button>

              {/* Current Story Content */}
              <div className="bg-gradient-to-br from-[var(--color-primary)] to-[var(--color-cool)] rounded-2xl overflow-hidden border-2 border-[var(--color-primary)] shadow-xl relative max-w-md w-full mx-auto">
                {/* User Info */}
                <div className="flex items-center p-4 text-white">
                  <img
                    src={selectedUserStories.stories[currentStoryIndex].userAvatar || '/favicon.png'}
                    alt={selectedUserStories.stories[currentStoryIndex].userName}
                    className="w-10 h-10 rounded-full object-cover mr-3"
                  />
                  <div className="flex-1">
                    <p className="font-semibold">{selectedUserStories.stories[currentStoryIndex].userName}</p>
                    <p className="text-sm text-gray-300">
                      <NoSSR fallback="24h remaining">
                        {getTimeRemaining(selectedUserStories.stories[currentStoryIndex].expiresAt)} remaining
                      </NoSSR>
                    </p>
                  </div>

                  {/* Story Counter */}
                  <div className="text-sm text-gray-300">
                    {currentStoryIndex + 1} / {selectedUserStories.totalStories}
                  </div>
                </div>

                {/* Story Image */}
                {selectedUserStories.stories[currentStoryIndex].image && (
                  <div className="relative">
                    <img
                      src={selectedUserStories.stories[currentStoryIndex].image}
                      alt={`${selectedUserStories.stories[currentStoryIndex].userName}'s story`}
                      className="w-full max-h-96 object-contain cursor-pointer transition-transform duration-200 hover:scale-105"
                      onClick={() => setShowEnlargedImage(true)}
                    />
                    {showEnlargedImage && (
                      <div 
                        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm" 
                        onClick={() => setShowEnlargedImage(false)}
                      >
                        <img
                          src={selectedUserStories.stories[currentStoryIndex].image}
                          alt={`${selectedUserStories.stories[currentStoryIndex].userName}'s story enlarged`}
                          className="max-w-3xl max-h-[80vh] rounded-2xl shadow-2xl border-4 border-[var(--color-primary)]"
                        />
                        <button
                          className="absolute top-8 right-8 text-white bg-black bg-opacity-60 rounded-full p-2 hover:bg-opacity-90 z-50"
                          onClick={e => { e.stopPropagation(); setShowEnlargedImage(false); }}
                        >
                          <X className="w-6 h-6" />
                        </button>
                      </div>
                    )}
                  </div>
                )}

                {/* Story Text */}
                {selectedUserStories.stories[currentStoryIndex].content && (
                  <div className="p-4 text-white">
                    <p className="text-center">{selectedUserStories.stories[currentStoryIndex].content}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Story Modal - Full Screen Overlay */}
      {showCreateStory && (
        <div
          className="fixed top-0 left-0 w-full h-full bg-black/90 backdrop-blur-xl z-[99999] flex items-center justify-center p-6"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 99999
          }}
        >
          <div
            className="bg-gradient-to-br from-white/20 via-white/10 to-white/5 backdrop-blur-3xl rounded-3xl p-8 w-full max-w-md mx-auto shadow-2xl border border-white/20 relative overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.1) 100%)',
              boxShadow: '0 32px 64px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3)',
              backdropFilter: 'blur(40px)',
              WebkitBackdropFilter: 'blur(40px)'
            }}
          >
            {/* Animated Background Elements */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/3 to-pink-500/5 pointer-events-none rounded-3xl" />
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-tr from-transparent via-white/3 to-transparent pointer-events-none rounded-3xl" />

            {/* Floating Orbs */}
            <div className="absolute -top-2 -right-2 w-24 h-24 bg-gradient-to-br from-blue-400/15 to-purple-500/15 rounded-full blur-2xl animate-pulse" />
            <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-gradient-to-br from-pink-400/15 to-orange-500/15 rounded-full blur-xl animate-pulse delay-1000" />
            <div className="absolute top-1/2 right-0 w-16 h-16 bg-gradient-to-br from-cyan-400/10 to-blue-500/10 rounded-full blur-lg animate-pulse delay-500" />

            {/* Content */}
            <div className="relative z-30">
              <div className="flex items-start justify-between mb-8">
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
                    Create Your Story
                  </h3>
                  <p className="text-white/70 text-sm font-medium">Share a moment that lasts 24 hours ✨</p>
                </div>
                <button
                  onClick={() => {
                    setShowCreateStory(false);
                    setStoryContent('');
                    setStoryImage(null);
                  }}
                  className="text-white/70 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl backdrop-blur-sm border border-white/10 hover:border-white/30 hover:scale-110 ml-4"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Story Image Upload */}
                <div>
                  <label className="block text-sm font-semibold text-white/90 mb-3 flex items-center space-x-2">
                    <span className="text-lg">📸</span>
                    <span>Add Photo (Optional)</span>
                  </label>
                  <div className="border-2 border-dashed border-white/30 rounded-2xl p-6 text-center bg-white/5 backdrop-blur-sm hover:border-white/50 hover:bg-white/10 transition-all duration-300 group">
                    {storyImage ? (
                      <div className="relative">
                        <img
                          src={storyImage}
                          alt="Story preview"
                          className="w-full h-40 object-cover rounded-xl shadow-lg border border-white/20"
                        />
                        <button
                          onClick={() => setStoryImage(null)}
                          className="absolute top-2 right-2 bg-red-500/90 backdrop-blur-sm text-white rounded-full w-8 h-8 flex items-center justify-center text-sm hover:bg-red-600 transition-all duration-200 shadow-lg border border-white/20"
                        >
                          ×
                        </button>
                      </div>
                    ) : (
                      <>
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500/70 to-purple-600/70 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg backdrop-blur-sm border border-white/20 group-hover:scale-105 transition-all duration-300">
                          <Camera className="w-8 h-8 text-white" />
                        </div>
                        <p className="text-white/90 mb-4 font-medium">Add a photo to your story</p>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                          id="story-image-upload"
                        />
                        <label
                          htmlFor="story-image-upload"
                          className="inline-block bg-gradient-to-r from-blue-500/80 to-purple-600/80 backdrop-blur-sm text-white px-6 py-3 rounded-xl hover:from-blue-600/90 hover:to-purple-700/90 cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-semibold border border-white/20 hover:border-white/40"
                        >
                          Choose Photo
                        </label>
                      </>
                    )}
                  </div>
                </div>

                {/* Story Text */}
                <div>
                  <label className="block text-sm font-semibold text-white/90 mb-3 flex items-center space-x-2">
                    <span className="text-lg">✍️</span>
                    <span>Add Text (Optional)</span>
                  </label>
                  <textarea
                    value={storyContent}
                    onChange={(e) => setStoryContent(e.target.value)}
                    placeholder="What's happening with your pets today? 🐕🐱"
                    className="w-full px-4 py-3 border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-white/70 resize-none bg-white/10 backdrop-blur-sm transition-all duration-300 placeholder-white/60 text-white font-medium shadow-lg hover:bg-white/15 focus:bg-white/15"
                    rows={3}
                    maxLength={200}
                  />
                  <div className="flex justify-between items-center mt-2">
                    <p className="text-xs text-white/70">
                      {storyContent.length}/200 characters
                    </p>
                    <div className="flex items-center space-x-1 text-xs text-white/70">
                      <span>✨</span>
                      <span>Express yourself</span>
                    </div>
                  </div>
                </div>

                {/* Story Duration Info */}
                <div className="bg-white/10 backdrop-blur-sm p-4 rounded-2xl border border-white/20 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse"></div>
                    <p className="text-white/90 font-semibold text-sm">
                      ⏰ Your story will be visible for 24 hours
                    </p>
                  </div>
                  <p className="text-white/70 mt-1 ml-6 text-xs">
                    Share moments that matter with your pet community ✨
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-6">
                  <button
                    onClick={() => {
                      setShowCreateStory(false);
                      setStoryContent('');
                      setStoryImage(null);
                    }}
                    className="flex-1 px-6 py-3 border border-white/30 text-white/90 rounded-xl hover:bg-white/10 hover:border-white/50 transition-all duration-300 font-semibold backdrop-blur-sm shadow-lg hover:scale-105"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateStory}
                    disabled={isSubmitting || (!storyContent.trim() && !storyImage)}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-500/80 to-purple-600/80 backdrop-blur-sm text-white rounded-xl hover:from-blue-600/90 hover:to-purple-700/90 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none border border-white/20 hover:border-white/40"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Sharing...</span>
                      </>
                    ) : (
                      <>
                        <Upload className="w-5 h-5" />
                        <span>Share Story</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
