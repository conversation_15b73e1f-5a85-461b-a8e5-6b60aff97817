'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Camera, ChevronLeft, ChevronRight, Play, Pause, X, Upload, Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';

// Define the User type that matches the auth context
type User = {
  uid: string;
  displayName: string | null;
  photoURL: string | null;
  email?: string | null;
  emailVerified?: boolean;
  isAnonymous?: boolean;
  metadata?: any;
  providerData?: any[];
  refreshToken?: string;
  tenantId?: string | null;
  delete?: () => Promise<void>;
  getIdToken?: (forceRefresh?: boolean) => Promise<string>;
  getIdTokenResult?: (forceRefresh?: boolean) => Promise<any>;
  reload?: () => Promise<void>;
  toJSON?: () => object;
  // Some auth providers might use 'id' instead of 'uid'
  id?: string;
};
import { db } from '@/lib/firebase';
import { collection, query, where, onSnapshot, orderBy, addDoc, serverTimestamp } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import NoSSR from './NoSSR';

interface Story {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  text?: string;
  image?: string;
  mediaType?: 'image' | 'video';
  mediaUrl?: string;
  timestamp: Date;
  expiresAt: Date;
  isStory: boolean;
  textColor?: string;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  textAlign?: 'left' | 'center' | 'right' | 'justify' | 'start' | 'end';
  seen?: boolean;
}

interface UserStories {
  userId: string;
  userName: string;
  userAvatar: string;
  stories: Story[];
  latestStory?: Story;
  totalStories: number;
  lastUpdated: Date;
}

interface StoriesSectionProps {
  onCreateStory?: () => void;
}

const STORY_DURATION = 10; // seconds

declare global {
  interface Window {
    onYouTubeIframeAPIReady?: () => void;
    YT: any;
  }
}

export default function StoriesSection({ onCreateStory }: StoriesSectionProps) {
  const { user } = useAuth() as { user: User | null };
  const { createPost } = useData();
  
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // User stories state
  const [userStories, setUserStories] = useState<UserStories[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Modal state
  const [showModal, setShowModal] = useState<boolean>(false);
  const [selectedUserStories, setSelectedUserStories] = useState<UserStories | null>(null);
  const [currentUserIndex, setCurrentUserIndex] = useState<number>(0);
  const [currentStoryIndex, setCurrentStoryIndex] = useState<number>(0);
  const [progress, setProgress] = useState<number>(0);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  
  // Story creation states
  const [showCreateStory, setShowCreateStory] = useState<boolean>(false);
  const [storyContent, setStoryContent] = useState<string>('');
  const [storyImage, setStoryImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  // Touch handling states
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [autoProgressEnabled, setAutoProgressEnabled] = useState(true);
  const [isLongPress, setIsLongPress] = useState(false);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [progressTimer, setProgressTimer] = useState<NodeJS.Timeout | null>(null);
  const [showEnlargedImage, setShowEnlargedImage] = useState<boolean>(false);
  
  // Close modal and cleanup
  const closeModal = useCallback(() => {
    if (progressTimer) clearInterval(progressTimer);
    setShowModal(false);
    setSelectedUserStories(null);
    setCurrentStoryIndex(0);
    setCurrentUserIndex(0);
    setProgress(0);
    setIsPaused(false);
  }, [progressTimer]);

  // Navigation functions
  const goToNextStory = useCallback(() => {
    if (!selectedUserStories) return;

    if (currentStoryIndex < selectedUserStories.stories.length - 1) {
      setCurrentStoryIndex(prev => prev + 1);
      setProgress(0);
    } else if (currentUserIndex < userStories.length - 1) {
      // Move to next user's stories
      setCurrentUserIndex(prev => prev + 1);
      setSelectedUserStories(userStories[currentUserIndex + 1]);
      setCurrentStoryIndex(0);
      setProgress(0);
    } else {
      closeModal();
    }
  }, [currentStoryIndex, currentUserIndex, selectedUserStories, userStories, closeModal]);

  const goToPrevStory = useCallback(() => {
    if (!selectedUserStories) return;

    if (currentStoryIndex > 0) {
      setCurrentStoryIndex(prev => prev - 1);
      setProgress(0);
    } else if (currentUserIndex > 0) {
      setCurrentUserIndex(prev => prev - 1);
      setSelectedUserStories(userStories[currentUserIndex - 1]);
      setCurrentStoryIndex(userStories[currentUserIndex - 1]?.stories?.length - 1 || 0);
      setProgress(0);
    }
  }, [currentStoryIndex, currentUserIndex, selectedUserStories, userStories]);

  const goToNextUser = useCallback(() => {
    if (currentUserIndex < userStories.length - 1) {
      setCurrentUserIndex(prev => prev + 1);
      setSelectedUserStories(userStories[currentUserIndex + 1]);
      setCurrentStoryIndex(0);
      setProgress(0);
    } else {
      closeModal();
    }
  }, [currentUserIndex, userStories, closeModal]);

  const goToPrevUser = useCallback(() => {
    if (currentUserIndex > 0) {
      setCurrentUserIndex(prev => prev - 1);
      setSelectedUserStories(userStories[currentUserIndex - 1]);
      setCurrentStoryIndex(0);
      setProgress(0);
    }
  }, [currentUserIndex, userStories]);

  const togglePause = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);

  // Handle video play/pause when story is paused/resumed
  const handleVideoEnd = useCallback(() => {
    if (!isPaused) {
      goToNextStory();
    }
  }, [isPaused, goToNextStory]);
  
  // Clear all timers
  const clearTimers = useCallback(() => {
    if (progressTimer) {
      clearInterval(progressTimer);
      setProgressTimer(null);
    }
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  }, [progressTimer, longPressTimer]);
  
  // Start story timer with fixed 10s duration
  const startStoryTimer = useCallback(() => {
    clearTimers();
    setProgress(0);

    const durationMs = STORY_DURATION * 1000; // Convert to milliseconds
    const interval = 50; // Update every 50ms for smooth progress
    const increment = (interval / durationMs) * 100;

    const timer = setInterval(() => {
      if (!isPaused) {
        setProgress(prev => {
          const newProgress = Math.min(prev + increment, 100);
          if (newProgress >= 100) {
            goToNextStory();
            return 0;
          }
          return newProgress;
        });
      }
    }, interval);

    setProgressTimer(timer);
    return () => clearInterval(timer);
  }, [isPaused, goToNextStory, clearTimers]);
  
  // Handle video playback
  useEffect(() => {
    if (!videoRef.current) return;
    
    if (isPaused) {
      videoRef.current.pause();
    } else {
      videoRef.current.play().catch(e => console.error('Error playing video:', e));
    }
  }, [isPaused]);

  // Handle image upload
  const handleImageUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setStoryImage(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle story creation
  const handleCreateStory = useCallback(async () => {
    if (!user) return;
    
    if (!storyContent.trim() && !storyImage) {
      toast.error('Please add some content or an image to your story');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const storyRef = await addDoc(collection(db, 'stories'), {
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userAvatar: user.photoURL || '',
        content: storyContent,
        imageUrl: storyImage,
        mediaType: storyImage ? 'image' : undefined,
        mediaUrl: storyImage || undefined,
        timestamp: serverTimestamp(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        seen: false,
        isStory: true
      });
      
      const newStory: Story = {
        id: storyRef.id,
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userAvatar: user.photoURL || '',
        content: storyContent || '',
        image: storyImage || '',
        mediaType: storyImage ? 'image' : undefined,
        mediaUrl: storyImage || undefined,
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        isStory: true
      };
      
      setUserStories(prevStories => {
        const userStoryIndex = prevStories.findIndex(s => s.userId === user.uid);
        
        if (userStoryIndex >= 0) {
          const updatedStories = [...prevStories];
          updatedStories[userStoryIndex] = {
            ...updatedStories[userStoryIndex],
            stories: [newStory, ...updatedStories[userStoryIndex].stories],
            latestStory: newStory,
            totalStories: (updatedStories[userStoryIndex].totalStories || 0) + 1,
            lastUpdated: new Date()
          };
          return updatedStories;
        } else {
          const newUserStory: UserStories = {
            userId: user.uid,
            userName: user.displayName || 'Anonymous',
            userAvatar: user.photoURL || '',
            stories: [newStory],
            latestStory: newStory,
            totalStories: 1,
            lastUpdated: new Date()
          };
          return [newUserStory, ...prevStories];
        }
      });
      
      toast.success('Story created successfully!');
      setShowCreateStory(false);
      setStoryContent('');
      setStoryImage(null);
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to create story. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [user, storyContent, storyImage]);

  // Handle story click with null check for latestStory
  const handleStoryClick = useCallback((userStory: UserStories) => {
    const userIndex = userStories.findIndex(us => us.userId === userStory.userId);
    if (userIndex !== -1 && userStory.latestStory) {
      setSelectedUserStories(userStories[userIndex]);
      setCurrentUserIndex(userIndex);
      setCurrentStoryIndex(0);
      setProgress(0);
      setShowModal(true);
      setIsPaused(false);
    } else if (onCreateStory) {
      // If no latest story but onCreateStory is provided, call it
      onCreateStory();
    }
  }, [userStories, onCreateStory]);

  // Handle story tap
  const handleStoryTap = (side: 'left' | 'right') => {
    if (side === 'left') {
      goToPrevStory();
    } else {
      goToNextStory();
    }
  };

  // Enhanced Touch/Swipe handlers with long-press
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
    setIsLongPress(false);

    // Start long-press timer
    const timer = setTimeout(() => {
      setIsLongPress(true);
      setIsPaused(true);
      // Haptic feedback if available
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, 500); // 500ms for long press

    setLongPressTimer(timer);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);

    // Cancel long press if user moves finger
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  const handleTouchEnd = () => {
    // Clear long press timer
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    // If it was a long press, resume playback
    if (isLongPress) {
      setIsPaused(false);
      setIsLongPress(false);
      return;
    }

    // Handle swipe gestures
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      goToNextStory();
    } else if (isRightSwipe) {
      goToPrevStory();
    }
  };

  // Mouse handlers for desktop long-press (hover)
  const handleMouseEnter = () => {
    const timer = setTimeout(() => {
      setIsPaused(true);
    }, 300); // Shorter delay for hover

    setLongPressTimer(timer);
  };

  const handleMouseLeave = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
    setIsPaused(false);
  };

  // Auto-advance stories effect
  useEffect(() => {
    if (showModal && selectedUserStories && !isPaused) {
      startStoryTimer();
    }
    return () => {
      clearTimers();
    };
  }, [showModal, selectedUserStories, isPaused, startStoryTimer, clearTimers]);

  // Handle keyboard navigation
  const handleKeyPress = useCallback((e: KeyboardEvent) => {
    if (!showModal) return;
    
    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        goToPrevStory();
        break;
      case 'ArrowRight':
        e.preventDefault();
        goToNextStory();
        break;
      case ' ':
      case 'Spacebar':
        e.preventDefault();
        togglePause();
        break;
      case 'Escape':
        e.preventDefault();
        closeModal();
        break;
      case 'ArrowUp':
        e.preventDefault();
        goToPrevUser();
        break;
      case 'ArrowDown':
        e.preventDefault();
        goToNextUser();
        break;
      default:
        break;
    }
  }, [showModal, goToPrevStory, goToNextStory, togglePause, closeModal, goToPrevUser, goToNextUser]);

  // Helper function to get time remaining for a story
  const getTimeRemaining = (expiresAt: Date) => {
    const now = new Date();
    const diff = expiresAt.getTime() - now.getTime();
    
    if (diff <= 0) return { hours: 0, minutes: 0, seconds: 0 };
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    return { hours, minutes, seconds };
  };

  if (loading) {
    return (
      <div className="glass-card rounded-xl p-4 mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Stories</h3>
        <p className="text-sm text-gray-500">24h updates</p>
      </div>

      {/* Stories Container */}
      <div className="flex items-center space-x-4 overflow-x-auto pb-2 scrollbar-hide">
        {/* Add Story Button */}
        {user && (
          <div className="flex-shrink-0 text-center">
            <button
              onClick={() => setShowCreateStory(true)}
              className="relative w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-dashed border-blue-300 hover:border-blue-500 hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 flex items-center justify-center group"
            >
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Plus className="w-6 h-6 text-white" />
              </div>
            </button>
            <p className="text-xs text-gray-600 mt-2 font-medium">Add Story</p>
          </div>
        )}

        {/* Stories */}
        {userStories.map((userStory) => {
                  const latestStory = userStory.latestStory;
                  const hasUnseenStory = latestStory && !latestStory.seen;
                  const avatarUrl = latestStory?.userAvatar || '/favicon.png';
                  const userName = latestStory?.userName || userStory.userName;
                  
                  return (
                    <div
                      key={userStory.userId}
                      className="w-20 flex-shrink-0 flex flex-col items-center space-y-1 cursor-pointer relative"
                      onClick={() => handleStoryClick(userStory)}
                    >
                      <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 p-0.5 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                        <div className="w-full h-full rounded-2xl bg-white p-0.5">
                          <img
                            src={avatarUrl}
                            alt={userName}
                            className="w-full h-full rounded-xl object-cover"
                          />
                        </div>
                      </div>
                      <p className="text-xs font-medium text-gray-700 truncate w-20 text-center">
                        {userStory.userName}
                      </p>
                      {hasUnseenStory && (
                        <div className="absolute top-0 right-0 w-3 h-3 bg-red-500 rounded-full border-2 border-white"></div>
                      )}
                    </div>
                  );
                })}
                </div>
              </div>

              {/* Time Remaining */}
              <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs px-2 py-1 rounded-full shadow-lg border-2 border-white font-medium">
                <NoSSR>
                  {(() => {
                    if (!userStory.latestStory) return '24h';
                    const { hours, minutes } = getTimeRemaining(userStory.latestStory.expiresAt);
                    return `${hours}h ${minutes}m`;
                  })()}
                </NoSSR>
              </div>
            </button>

            <p className="text-xs text-gray-700 mt-2 font-medium truncate w-20">
              {userStory.userId === user?.id ? 'You' : userStory.latestStory.userName}
            </p>
          </div>
        ))}

        {/* No Stories Message */}
        {userStories.length === 0 && !user && (
          <div className="flex-1 text-center py-12">
            <div className="text-gray-500">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-lg font-semibold text-gray-700 mb-2">No Stories Yet</p>
              <p className="text-sm text-gray-500">Stories from your community will appear here</p>
            </div>
          </div>
        )}

        {(!userStories.find(us => us.userId === user?.id) || userStories.find(us => us.userId === user?.id)?.stories.length === 0) && user && userStories.length === 0 && (
          <div className="flex-1 text-center py-8">
            <div className="text-gray-500">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl mx-auto mb-4 flex items-center justify-center border-2 border-dashed border-blue-200">
                <Plus className="w-8 h-8 text-blue-400" />
              </div>
              <p className="text-lg font-semibold text-gray-700 mb-2">Share Your First Story</p>
              <p className="text-sm text-gray-500">Let your community know what's happening with your pets!</p>
            </div>
          </div>
        )}
      </div>

      {/* Story Viewing Modal */}
      {showModal && selectedUserStories && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90" onClick={closeModal}>
          <div className="relative w-full h-full max-w-2xl max-h-[90vh] aspect-[9/16] bg-black rounded-xl overflow-hidden" onClick={e => e.stopPropagation()}>
            {/* Progress bars */}
            <div className="absolute top-0 left-0 right-0 z-20 flex w-full p-2 space-x-1">
              {selectedUserStories.stories.map((_, index) => (
                <div key={index} className="flex-1 h-1 bg-white/30 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-white transition-all duration-100"
                    style={{
                      width: index < currentStoryIndex ? '100%' : 
                             index === currentStoryIndex ? `${progress}%` : '0%',
                      backgroundColor: index === currentStoryIndex ? '#ffffff' : 'rgba(255,255,255,0.7)'
                    }}
                  />
                </div>
              ))}
            </div>

            {/* Story Controls */}
            <div className="absolute top-4 right-4 z-30 flex space-x-2">
              {/* Pause/Play Button */}
              <button
                onClick={togglePause}
                className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                {isPaused ? <Play className="w-5 h-5 ml-0.5" /> : <Pause className="w-5 h-5" />}
              </button>

              {/* Close Button */}
              <button
                onClick={closeModal}
                className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Navigation Arrows */}
            <div 
              className="absolute left-0 top-1/2 transform -translate-y-1/2 pl-4 z-20"
              onClick={(e) => {
                e.stopPropagation();
                if (currentStoryIndex > 0 || currentUserIndex > 0) {
                  goToPrevStory();
                }
              }}
            >
              {(currentStoryIndex > 0 || currentUserIndex > 0) && (
                <div className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-opacity-80 hover:scale-110 transition-all duration-200">
                  <ChevronLeft className="w-6 h-6 text-white" />
                </div>
              )}
            </div>

            <div 
              className="absolute right-0 top-1/2 transform -translate-y-1/2 pr-4 z-20"
              onClick={(e) => {
                e.stopPropagation();
                goToNextStory();
              }}
            >
              <div className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-opacity-80 hover:scale-110 transition-all duration-200">
                <ChevronRight className="w-6 h-6 text-white" />
              </div>
            </div>
              {/* Tap indicator */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-30 transition-opacity">
                <div className="text-white text-sm font-medium bg-black bg-opacity-50 px-3 py-1 rounded-full">
                  {currentStoryIndex < selectedUserStories.stories.length - 1 ? 'Next' :
                   currentUserIndex < userStories.length - 1 ? 'Next User' : 'Finish'}
                </div>
              </div>
            </div>

            {/* Center tap area for pause/play with long-press indicator */}
            <button
              onClick={togglePause}
              className="absolute left-1/3 right-1/3 top-0 bottom-0 z-10 flex items-center justify-center opacity-0 hover:opacity-100 transition-all duration-200 group"
            >
              <div className="w-14 h-14 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-opacity-80 group-hover:scale-110 transition-all duration-200">
                {isPaused ? <Play className="w-7 h-7 ml-1" /> : <Pause className="w-7 h-7" />}
              </div>
              {/* Long-press indicator */}
              <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="text-white text-xs font-medium bg-black bg-opacity-70 px-3 py-1 rounded-full whitespace-nowrap">
                  {isPaused ? 'Tap to resume' : 'Hold to pause'}
                </div>
              </div>
            </button>

            {/* Story Content */}
            <div
              className="relative w-full h-full flex items-center justify-center overflow-hidden bg-black"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              {/* Story Media */}
              {selectedUserStories.stories[currentStoryIndex].mediaType === 'image' ? (
                <img
                  src={selectedUserStories.stories[currentStoryIndex].mediaUrl}
                  alt={`Story by ${selectedUserStories.stories[currentStoryIndex].userName}`}
                  className="w-full h-full object-contain"
                  style={{ maxHeight: '100vh' }}
                />
              ) : (
                <video
                  src={selectedUserStories.stories[currentStoryIndex].mediaUrl}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-full object-contain"
                  style={{ maxHeight: '100vh' }}
                  onEnded={handleVideoEnd}
                  ref={videoRef}
                />
              )}

              {/* Story Text Overlay */}
              {selectedUserStories.stories[currentStoryIndex].text && (
                <div 
                  className="absolute inset-0 flex items-center justify-center p-4 text-center pointer-events-none"
                  style={{
                    textShadow: '0 1px 3px rgba(0,0,0,0.8)',
                    color: selectedUserStories.stories[currentStoryIndex].textColor || '#ffffff',
                    fontFamily: selectedUserStories.stories[currentStoryIndex].fontFamily || 'sans-serif',
                    fontSize: selectedUserStories.stories[currentStoryIndex].fontSize || '1.25rem',
                    fontWeight: selectedUserStories.stories[currentStoryIndex].fontWeight || '600',
                    textAlign: selectedUserStories.stories[currentStoryIndex].textAlign || 'center',
                    wordBreak: 'break-word',
                    maxWidth: '90%',
                    margin: '0 auto',
                    lineHeight: '1.4',
                  }}
                  dangerouslySetInnerHTML={{
                    __html: selectedUserStories.stories[currentStoryIndex].text.replace(/\n/g, '<br>'),
                  }}
                />
              )}

              {/* Pause Overlay */}
              {isPaused && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                  <div className="text-white text-center p-6 bg-black bg-opacity-60 rounded-full">
                    <Pause className="w-12 h-12 mx-auto mb-2" />
                    <p className="text-sm opacity-90">Tap to resume</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create Story Modal */}
      {showCreateStory && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={() => {
              setShowCreateStory(false);
              setStoryContent('');
              setStoryImage(null);
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-3xl p-8 w-full max-w-md mx-auto shadow-2xl border border-gray-200">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold text-gray-800 mb-1">Create Story</h3>
                <p className="text-gray-500 text-sm">Share a moment with your community</p>
              </div>
              <button
                onClick={() => {
                  setShowCreateStory(false);
                  setStoryContent('');
                  setStoryImage(null);
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-xl"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Image Upload */}
              <div className="mb-4">
                <div 
                  className="w-full aspect-[9/16] bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 flex flex-col items-center justify-center cursor-pointer hover:border-blue-500 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                >
                  {storyImage ? (
                    <img 
                      src={storyImage} 
                      alt="Story preview" 
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <>
                      <Upload className="w-10 h-10 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500">Click to upload an image</p>
                      <p className="text-xs text-gray-400 mt-1">or drag and drop</p>
                    </>
                  )}
                  <input 
                    type="file" 
                    ref={fileInputRef}
                    className="hidden" 
                    accept="image/*"
                    onChange={handleImageUpload}
                  />
                </div>
                {storyImage && (
                  <button
                    onClick={() => setStoryImage(null)}
                    className="mt-2 text-sm text-red-500 hover:text-red-600"
                  >
                    Remove image
                  </button>
                )}
              </div>

              {/* Text Input */}
              <div className="mb-4">
                <label htmlFor="storyText" className="block text-sm font-medium text-gray-700 mb-1">
                  Add text (optional)
                </label>
                <textarea
                  id="storyText"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="What's on your mind?"
                  value={storyContent}
                  onChange={(e) => setStoryContent(e.target.value)}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateStory(false);
                    setStoryContent('');
                    setStoryImage(null);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleCreateStory}
                  disabled={isSubmitting || (!storyContent && !storyImage)}
                  className={`px-4 py-2 text-sm font-medium text-white rounded-lg ${
                    !storyContent && !storyImage
                      ? 'bg-blue-300 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {isSubmitting ? 'Posting...' : 'Post Story'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
