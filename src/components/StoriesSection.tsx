'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Plus, Play, X, Camera, Upload, Pause, ChevronLeft, ChevronRight } from 'lucide-react';
import NoSSR from './NoSSR';
import toast from 'react-hot-toast';

interface Story {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  image?: string;
  timestamp: Date;
  expiresAt: Date;
  isStory: boolean;
}

interface UserStories {
  userId: string;
  stories: Story[];
  latestStory: Story;
  totalStories: number;
}

interface StoriesSectionProps {
  onCreateStory?: () => void;
}

export default function StoriesSection({ onCreateStory }: StoriesSectionProps) {
  const { user } = useAuth();
  const { createPost } = useData();
  const [userStories, setUserStories] = useState<UserStories[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUserStories, setSelectedUserStories] = useState<UserStories | null>(null);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [storyDuration, setStoryDuration] = useState(5); // Default 5 seconds per story
  const [autoPlayTimer, setAutoPlayTimer] = useState<NodeJS.Timeout | null>(null);
  const [progressTimer, setProgressTimer] = useState<NodeJS.Timeout | null>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showCreateStory, setShowCreateStory] = useState(false);
  const [storyContent, setStoryContent] = useState('');
  const [storyImage, setStoryImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
const [showEnlargedImage, setShowEnlargedImage] = useState(false);

  // Load active stories (not expired)
  useEffect(() => {
    const now = new Date();
    console.log('🔍 Loading stories, current time:', now);

    // First, let's load all stories to debug
    const storiesQuery = query(
      collection(db, 'posts'),
      where('isStory', '==', true),
      orderBy('timestamp', 'desc')
    );

    const unsubscribe = onSnapshot(storiesQuery, (snapshot) => {
      console.log('📱 Stories snapshot received, docs count:', snapshot.docs.length);

      const storiesData = snapshot.docs.map(doc => {
        const data = doc.data();
        console.log('📱 Story data:', { id: doc.id, ...data });
        return {
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date()
        };
      }) as Story[];

      console.log('📱 Processed stories:', storiesData);

      // Filter out expired stories
      const now = new Date();
      const activeStories = storiesData.filter(story => {
        const isActive = story.expiresAt > now;
        console.log(`📱 Story ${story.id} expires at ${story.expiresAt}, active: ${isActive}`);
        return isActive;
      });

      // Group stories by user (keep all stories per user, sorted by timestamp)
      const userStoriesMap = new Map();
      activeStories.forEach(story => {
        if (!userStoriesMap.has(story.userId)) {
          userStoriesMap.set(story.userId, []);
        }
        userStoriesMap.get(story.userId).push(story);
      });

      // Sort stories within each user group by timestamp (oldest first)
      userStoriesMap.forEach((stories, userId) => {
        stories.sort((a: Story, b: Story) => a.timestamp.getTime() - b.timestamp.getTime());
      });

      // Create user story objects with latest story for display
      const finalStories = Array.from(userStoriesMap.entries()).map(([userId, stories]) => ({
        userId,
        stories: stories,
        latestStory: stories[stories.length - 1], // Most recent for display
        totalStories: stories.length
      }));
      console.log('📱 Final user stories to display:', finalStories);

      setUserStories(finalStories);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleStoryClick = (userStory: UserStories) => {
    const userIndex = userStories.findIndex(us => us.userId === userStory.userId);
    setCurrentUserIndex(userIndex);
    setSelectedUserStories(userStory);
    setCurrentStoryIndex(0);
    setProgress(0);
    setIsPaused(false);
    setShowModal(true);
    startStoryTimer();
  };

  // Handle image upload for story
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setStoryImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Create story
  const handleCreateStory = async () => {
    if (!user || (!storyContent.trim() && !storyImage)) {
      toast.error('Please add some content or an image to your story');
      return;
    }

    setIsSubmitting(true);
    try {
      await createPost(
        storyContent.trim() || 'Story', // Default content if only image
        storyImage || undefined,
        true, // isPublic
        true  // isStory
      );

      toast.success('Story created successfully!');
      setShowCreateStory(false);
      setStoryContent('');
      setStoryImage(null);
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to create story');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Story Timer Functions
  const startStoryTimer = () => {
    clearTimers();
    setProgress(0);

    const duration = getCurrentStoryDuration();
    const interval = 50; // Update every 50ms for smooth progress
    const increment = (interval / (duration * 1000)) * 100;

    const progressInterval = setInterval(() => {
      if (!isPaused) {
        setProgress(prev => {
          const newProgress = prev + increment;
          if (newProgress >= 100) {
            goToNextStory();
            return 0;
          }
          return newProgress;
        });
      }
    }, interval);

    setProgressTimer(progressInterval);
  };

  const clearTimers = () => {
    if (autoPlayTimer) {
      clearTimeout(autoPlayTimer);
      setAutoPlayTimer(null);
    }
    if (progressTimer) {
      clearInterval(progressTimer);
      setProgressTimer(null);
    }
  };

  const getCurrentStoryDuration = () => {
    if (!selectedUserStories || !selectedUserStories.stories[currentStoryIndex]) {
      return storyDuration;
    }

    const currentStory = selectedUserStories.stories[currentStoryIndex];
    // If story has an image, show for longer (8 seconds), text only for shorter (5 seconds)
    return currentStory.image ? 8 : 5;
  };

  const goToNextStory = () => {
    if (!selectedUserStories) return;

    if (currentStoryIndex < selectedUserStories.stories.length - 1) {
      // Next story in current user's stories
      setCurrentStoryIndex(prev => prev + 1);
      setProgress(0);
      startStoryTimer();
    } else {
      // Move to next user's stories
      goToNextUser();
    }
  };

  const goToPrevStory = () => {
    if (currentStoryIndex > 0) {
      // Previous story in current user's stories
      setCurrentStoryIndex(prev => prev - 1);
      setProgress(0);
      startStoryTimer();
    } else {
      // Move to previous user's stories
      goToPrevUser();
    }
  };

  const goToNextUser = () => {
    if (currentUserIndex < userStories.length - 1) {
      const nextUserIndex = currentUserIndex + 1;
      const nextUserStories = userStories[nextUserIndex];

      setCurrentUserIndex(nextUserIndex);
      setSelectedUserStories(nextUserStories);
      setCurrentStoryIndex(0);
      setProgress(0);
      startStoryTimer();
    } else {
      // All stories watched, close viewer
      closeModal();
    }
  };

  const goToPrevUser = () => {
    if (currentUserIndex > 0) {
      const prevUserIndex = currentUserIndex - 1;
      const prevUserStories = userStories[prevUserIndex];

      setCurrentUserIndex(prevUserIndex);
      setSelectedUserStories(prevUserStories);
      setCurrentStoryIndex(prevUserStories.stories.length - 1); // Go to last story of previous user
      setProgress(0);
      startStoryTimer();
    }
  };

  const togglePause = () => {
    setIsPaused(prev => !prev);
  };

  const closeModal = () => {
    clearTimers();
    setShowModal(false);
    setSelectedUserStories(null);
    setCurrentStoryIndex(0);
    setCurrentUserIndex(0);
    setProgress(0);
    setIsPaused(false);
  };

  const handleStoryTap = (side: 'left' | 'right') => {
    if (side === 'left') {
      goToPrevStory();
    } else {
      goToNextStory();
    }
  };

  // Touch/Swipe handlers for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      goToNextStory();
    } else if (isRightSwipe) {
      goToPrevStory();
    }
  };

  // Auto-advance stories effect
  useEffect(() => {
    if (showModal && selectedUserStories && !isPaused) {
      startStoryTimer();
    } else {
      clearTimers();
    }

    return () => clearTimers();
  }, [showModal, selectedUserStories, isPaused, currentStoryIndex]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!showModal) return;

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          goToPrevStory();
          break;
        case 'ArrowRight':
        case ' ': // Spacebar
          e.preventDefault();
          goToNextStory();
          break;
        case 'Escape':
          e.preventDefault();
          closeModal();
          break;
        case 'p':
        case 'P':
          e.preventDefault();
          togglePause();
          break;
      }
    };

    if (showModal) {
      document.addEventListener('keydown', handleKeyPress);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [showModal, currentStoryIndex, currentUserIndex]);

  // Cleanup on unmount
  useEffect(() => {
    return () => clearTimers();
  }, []);

  const getTimeRemaining = (expiresAt: Date) => {
    const now = new Date();
    const diff = expiresAt.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h`;
    } else if (minutes > 0) {
      return `${minutes}m`;
    } else {
      return 'Expiring';
    }
  };

  if (loading) {
    return (
      <div className="glass-card rounded-xl p-4 mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Stories</h3>
        <p className="text-sm text-gray-500">24h updates</p>
      </div>

      {/* Stories Container */}
      <div className="flex items-center space-x-4 overflow-x-auto pb-2 scrollbar-hide">
        {/* Add Story Button */}
        {user && (
          <div className="flex-shrink-0 text-center">
            <button
              onClick={() => setShowCreateStory(true)}
              className="relative w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-dashed border-blue-300 hover:border-blue-500 hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 flex items-center justify-center group"
            >
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Plus className="w-6 h-6 text-white" />
              </div>
            </button>
            <p className="text-xs text-gray-600 mt-2 font-medium">Add Story</p>
          </div>
        )}

        {/* Stories */}
        {userStories.map((userStory) => (
          <div key={userStory.userId} className="flex-shrink-0 text-center">
            <button
              className="relative group"
              onClick={() => handleStoryClick(userStory)}
            >
              {/* Story Container */}
              <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500 p-0.5 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="w-full h-full rounded-2xl bg-white p-0.5">
                  <img
                    src={userStory.latestStory.userAvatar || '/favicon.png'}
                    alt={userStory.latestStory.userName}
                    className="w-full h-full rounded-xl object-cover"
                  />
                </div>
              </div>

              {/* Multiple Stories Indicator */}
              {userStory.totalStories > 1 && (
                <div className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold shadow-lg border-2 border-white">
                  {userStory.totalStories}
                </div>
              )}

              {/* Play Icon Overlay */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-2xl bg-black bg-opacity-20">
                <div className="w-8 h-8 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg">
                  <Play className="w-4 h-4 text-gray-800 fill-current ml-0.5" />
                </div>
              </div>

              {/* Time Remaining */}
              <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs px-2 py-1 rounded-full shadow-lg border-2 border-white font-medium">
                <NoSSR fallback="24h">
                  {getTimeRemaining(userStory.latestStory.expiresAt)}
                </NoSSR>
              </div>
            </button>

            <p className="text-xs text-gray-700 mt-2 font-medium truncate w-20">
              {userStory.userId === user?.id ? 'You' : userStory.latestStory.userName}
            </p>
          </div>
        ))}

        {/* No Stories Message */}
        {userStories.length === 0 && !user && (
          <div className="flex-1 text-center py-12">
            <div className="text-gray-500">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <Play className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-lg font-semibold text-gray-700 mb-2">No Stories Yet</p>
              <p className="text-sm text-gray-500">Stories from your community will appear here</p>
            </div>
          </div>
        )}

        {(!userStories.find(us => us.userId === user?.id) || userStories.find(us => us.userId === user?.id)?.stories.length === 0) && user && userStories.length === 0 && (
          <div className="flex-1 text-center py-8">
            <div className="text-gray-500">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl mx-auto mb-4 flex items-center justify-center border-2 border-dashed border-blue-200">
                <Plus className="w-8 h-8 text-blue-400" />
              </div>
              <p className="text-lg font-semibold text-gray-700 mb-2">Share Your First Story</p>
              <p className="text-sm text-gray-500">Let your community know what's happening with your pets!</p>
            </div>
          </div>
        )}
      </div>

      {/* Story Viewing Modal */}
      {showModal && selectedUserStories && selectedUserStories.stories[currentStoryIndex] && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm" onClick={closeModal} />

          {/* Modal Container */}
          <div className="relative w-full max-w-sm mx-auto">
            {/* Progress Bars */}
            <div className="absolute top-6 left-6 right-6 z-30 flex space-x-2">
              {selectedUserStories.stories.map((_, index) => (
                <div key={index} className="flex-1 h-1 bg-white bg-opacity-30 rounded-full overflow-hidden">
                  <div
                    className={`h-full bg-white transition-all duration-300 ${
                      index < currentStoryIndex ? 'w-full' :
                      index === currentStoryIndex ? 'progress-bar-anim' : 'w-0'
                    }`}
                    style={index === currentStoryIndex ? {
                      width: `${progress}%`,
                      transition: isPaused ? 'none' : 'width 50ms linear',
                    } : index < currentStoryIndex ? { width: '100%' } : { width: '0%' }}
                  />
                </div>
              ))}
            </div>

            {/* Story Controls */}
            <div className="absolute top-6 right-6 z-30 flex space-x-3">
              {/* Pause/Play Button */}
              <button
                onClick={togglePause}
                className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                {isPaused ? <Play className="w-5 h-5 ml-0.5" /> : <Pause className="w-5 h-5" />}
              </button>

              {/* Close Button */}
              <button
                onClick={closeModal}
                className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-opacity-80 transition-all duration-200 border border-white border-opacity-20"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Navigation Areas - Tap to navigate */}
            <button
              onClick={() => handleStoryTap('left')}
              className="absolute left-0 top-0 bottom-0 w-1/3 z-20 flex items-center justify-start pl-6 text-white opacity-0 hover:opacity-100 transition-opacity"
              disabled={currentStoryIndex === 0 && currentUserIndex === 0}
            >
              {(currentStoryIndex > 0 || currentUserIndex > 0) && (
                <div className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <ChevronLeft className="w-6 h-6" />
                </div>
              )}
            </button>

            <button
              onClick={() => handleStoryTap('right')}
              className="absolute right-0 top-0 bottom-0 w-1/3 z-20 flex items-center justify-end pr-6 text-white opacity-0 hover:opacity-100 transition-opacity"
            >
              <div className="w-10 h-10 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center">
                <ChevronRight className="w-6 h-6" />
              </div>
            </button>

            {/* Center tap area for pause/play */}
            <button
              onClick={togglePause}
              className="absolute left-1/3 right-1/3 top-0 bottom-0 z-10 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
            >
              <div className="w-12 h-12 bg-black bg-opacity-60 backdrop-blur-sm rounded-full flex items-center justify-center">
                {isPaused ? <Play className="w-6 h-6 ml-1" /> : <Pause className="w-6 h-6" />}
              </div>
            </button>

            {/* Story Content */}
            <div
              className={`bg-white rounded-3xl overflow-hidden shadow-2xl border border-gray-200 relative transition-all duration-300 ${
                isPaused ? 'ring-4 ring-blue-500 ring-opacity-50' : ''
              }`}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              {/* User Header */}
              <div className="flex items-center p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
                <img
                  src={selectedUserStories.stories[currentStoryIndex].userAvatar || '/favicon.png'}
                  alt={selectedUserStories.stories[currentStoryIndex].userName}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                />
                <div className="flex-1 ml-4">
                  <p className="font-semibold text-gray-800">{selectedUserStories.stories[currentStoryIndex].userName}</p>
                  <p className="text-sm text-gray-500">
                    <NoSSR fallback="24h remaining">
                      {getTimeRemaining(selectedUserStories.stories[currentStoryIndex].expiresAt)} remaining
                    </NoSSR>
                  </p>
                </div>
                <div className="flex flex-col items-end space-y-1">
                  <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border border-gray-200">
                    {currentStoryIndex + 1} / {selectedUserStories.totalStories}
                  </div>
                  <div className="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-full border border-gray-100">
                    User {currentUserIndex + 1} of {userStories.length}
                  </div>
                </div>
              </div>

              {/* Story Image */}
              {selectedUserStories.stories[currentStoryIndex].image && (
                <div className="relative bg-gray-50">
                  <img
                    src={selectedUserStories.stories[currentStoryIndex].image}
                    alt={`${selectedUserStories.stories[currentStoryIndex].userName}'s story`}
                    className="w-full max-h-96 object-contain cursor-pointer transition-transform duration-200 hover:scale-105"
                    onClick={() => setShowEnlargedImage(true)}
                  />
                  {showEnlargedImage && (
                    <div
                      className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-90 backdrop-blur-sm p-4"
                      onClick={() => setShowEnlargedImage(false)}
                    >
                      <img
                        src={selectedUserStories.stories[currentStoryIndex].image}
                        alt={`${selectedUserStories.stories[currentStoryIndex].userName}'s story enlarged`}
                        className="max-w-full max-h-full rounded-2xl shadow-2xl"
                      />
                      <button
                        className="absolute top-8 right-8 text-white bg-black bg-opacity-60 backdrop-blur-sm rounded-full p-3 hover:bg-opacity-80 transition-all duration-200"
                        onClick={e => { e.stopPropagation(); setShowEnlargedImage(false); }}
                      >
                        <X className="w-6 h-6" />
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Story Text */}
              {selectedUserStories.stories[currentStoryIndex].content && (
                <div className="p-6">
                  <p className="text-gray-800 text-center leading-relaxed">{selectedUserStories.stories[currentStoryIndex].content}</p>
                </div>
              )}

              {/* Pause Overlay */}
              {isPaused && (
                <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center rounded-3xl">
                  <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-2xl p-4 flex items-center space-x-3 shadow-lg">
                    <Pause className="w-6 h-6 text-gray-700" />
                    <span className="text-gray-700 font-medium">Paused</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create Story Modal */}
      {showCreateStory && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={() => {
              setShowCreateStory(false);
              setStoryContent('');
              setStoryImage(null);
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-3xl p-8 w-full max-w-md mx-auto shadow-2xl border border-gray-200">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold text-gray-800 mb-1">Create Story</h3>
                <p className="text-gray-500 text-sm">Share a moment with your community</p>
              </div>
              <button
                onClick={() => {
                  setShowCreateStory(false);
                  setStoryContent('');
                  setStoryImage(null);
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-xl"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Form Content */}
            <div className="space-y-6">
              {/* Image Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                  <Camera className="w-4 h-4 text-blue-600" />
                  <span>Add Photo (Optional)</span>
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-2xl p-6 text-center bg-gray-50 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 group">
                  {storyImage ? (
                    <div className="relative">
                      <img
                        src={storyImage}
                        alt="Story preview"
                        className="w-full h-48 object-cover rounded-xl shadow-sm border border-gray-200"
                      />
                      <button
                        onClick={() => setStoryImage(null)}
                        className="absolute top-3 right-3 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm hover:bg-red-600 transition-colors shadow-lg"
                      >
                        ×
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300">
                        <Camera className="w-8 h-8 text-white" />
                      </div>
                      <p className="text-gray-700 mb-4 font-medium">Add a photo to your story</p>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="story-image-upload"
                      />
                      <label
                        htmlFor="story-image-upload"
                        className="inline-block bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-indigo-700 cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 font-semibold"
                      >
                        Choose Photo
                      </label>
                    </>
                  )}
                </div>
              </div>

              {/* Text Input */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center space-x-2">
                  <span className="text-lg">✍️</span>
                  <span>Add Text (Optional)</span>
                </label>
                <textarea
                  value={storyContent}
                  onChange={(e) => setStoryContent(e.target.value)}
                  placeholder="What's happening with your pets today? 🐕🐱"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-white transition-all duration-300 placeholder-gray-500 text-gray-800 shadow-sm hover:border-gray-400"
                  rows={4}
                  maxLength={200}
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-gray-500">
                    {storyContent.length}/200 characters
                  </p>
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <span>✨</span>
                    <span>Express yourself</span>
                  </div>
                </div>
              </div>

              {/* Info Box */}
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-2xl">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full animate-pulse"></div>
                  <p className="text-blue-800 font-semibold text-sm">
                    Your story will be visible for 24 hours
                  </p>
                </div>
                <p className="text-blue-700 mt-1 ml-6 text-xs">
                  Share moments that matter with your pet community
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-2">
                <button
                  onClick={() => {
                    setShowCreateStory(false);
                    setStoryContent('');
                    setStoryImage(null);
                  }}
                  className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateStory}
                  disabled={isSubmitting || (!storyContent.trim() && !storyImage)}
                  className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Sharing...</span>
                    </>
                  ) : (
                    <>
                      <Upload className="w-5 h-5" />
                      <span>Share Story</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
