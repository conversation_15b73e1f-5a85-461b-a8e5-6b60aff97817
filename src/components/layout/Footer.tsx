'use client';

import Link from 'next/link';
import { Heart, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react';

export function Footer() {
  return (
    <footer className="bg-charcoal-50 border-t border-primary-200/30">
      <div className="container mx-auto px-4 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <img
                src="/fetchlylogo.png"
                alt="Fetchly"
                className="h-8 w-auto"
              />
            </Link>
            <p className="text-charcoal-700 mb-6 leading-relaxed">
              Connecting pet parents with trusted, verified pet care professionals.
              Your pet's happiness and wellbeing is our mission.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="p-2 bg-white/80 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <Facebook className="w-5 h-5 text-charcoal-600 hover:text-primary-500" />
              </a>
              <a href="#" className="p-2 bg-white/80 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <Twitter className="w-5 h-5 text-charcoal-600 hover:text-primary-500" />
              </a>
              <a href="#" className="p-2 bg-white/80 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <Instagram className="w-5 h-5 text-charcoal-600 hover:text-primary-500" />
              </a>
              <a href="#" className="p-2 bg-white/80 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                <Youtube className="w-5 h-5 text-charcoal-600 hover:text-primary-500" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-bold text-charcoal-900 mb-6">Services</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/search?service=grooming" className="text-charcoal-700 hover:text-primary-500 transition-colors duration-200">
                  Pet Grooming
                </Link>
              </li>
              <li>
                <Link href="/search?service=veterinary" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Veterinary Care
                </Link>
              </li>
              <li>
                <Link href="/search?service=hotel" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Pet Hotels
                </Link>
              </li>
              <li>
                <Link href="/search?service=daycare" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Pet Daycare
                </Link>
              </li>
              <li>
                <Link href="/search?service=training" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Pet Training
                </Link>
              </li>
              <li>
                <Link href="/emergency" className="text-gray-600 hover:text-warm-500 transition-colors duration-200">
                  Emergency Care
                </Link>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-bold text-gray-800 mb-6">Company</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/providers" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  For Providers
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Pet Care Blog
                </Link>
              </li>
              <li>
                <Link href="/community" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Community
                </Link>
              </li>
              <li>
                <Link href="/press" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Press
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-bold text-gray-800 mb-6">Support</h3>
            <ul className="space-y-3 mb-6">
              <li>
                <Link href="/help" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/safety" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Safety Guidelines
                </Link>
              </li>
              <li>
                <Link href="/insurance" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                  Insurance
                </Link>
              </li>
            </ul>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <Phone className="w-4 h-4" />
                <span>1-800-FETCHLY</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>Available Nationwide</span>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="glass-card rounded-2xl p-8 mb-12">
          <div className="text-center md:text-left md:flex md:items-center md:justify-between">
            <div className="mb-6 md:mb-0">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Stay Updated</h3>
              <p className="text-gray-600">Get pet care tips, provider spotlights, and exclusive offers.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 md:max-w-md">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <button className="btn-primary whitespace-nowrap">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-200 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-600 text-sm">
              © 2024 Fetchly. All rights reserved.
            </div>
            
            <div className="flex flex-wrap justify-center gap-6 text-sm">
              <Link href="/privacy" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                Terms of Service
              </Link>
              <Link href="/cookies" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                Cookie Policy
              </Link>
              <Link href="/accessibility" className="text-gray-600 hover:text-primary-600 transition-colors duration-200">
                Accessibility
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
