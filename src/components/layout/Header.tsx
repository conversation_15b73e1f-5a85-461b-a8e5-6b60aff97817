'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Heart, Menu, X, User, Bell, LogOut, Settings, ChevronDown, LayoutDashboard } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, signOut, isAuthenticated } = useAuth();

  return (
    <header className="sticky top-0 z-50 glass-card border-b border-white/20">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center group">
            <img
              src="/fetchlylogo.png"
              alt="Fetchly"
              className="h-12 w-auto group-hover:scale-105 transition-transform duration-300"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/search"
              className="text-cool-700 hover:text-primary-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Find Services
            </Link>
            <Link
              href="/provider"
              className="text-cool-700 hover:text-secondary-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Providers
            </Link>
            <Link
              href="/blog"
              className="text-cool-700 hover:text-green-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Blog
            </Link>
            <Link
              href="/emergency"
              className="text-cool-700 hover:text-warm-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Emergency
            </Link>
            <Link
              href="/community"
              className="text-cool-700 hover:text-accent-500 font-medium transition-all duration-300 hover:scale-105"
            >
              Community
            </Link>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated && (
              <button className="relative p-2 text-cool-600 hover:text-primary-500 transition-all duration-300 hover:scale-110">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-warm-500 to-secondary-500 rounded-full animate-pulse"></span>
              </button>
            )}

            {isAuthenticated ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center gap-2 p-2 rounded-xl hover:bg-white/50 transition-all duration-300"
                >
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-cool-700 font-medium">{user?.name}</span>
                  <ChevronDown className="w-4 h-4 text-cool-600" />
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-64 glass-card rounded-2xl p-4 shadow-xl z-50">
                    <div className="border-b border-white/20 pb-3 mb-3">
                      <p className="font-medium text-cool-800">{user?.name}</p>
                      <p className="text-sm text-cool-600">{user?.email}</p>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                        user?.role === 'admin' ? 'bg-warm-100 text-warm-700' :
                        user?.role === 'provider' ? 'bg-secondary-100 text-secondary-700' :
                        'bg-primary-100 text-primary-700'
                      }`}>
                        {user?.role === 'pet_owner' ? 'Pet Owner' :
                         user?.role === 'provider' ? 'Service Provider' : 'Admin'}
                      </span>
                    </div>

                    <div className="space-y-2">
                      {user?.role === 'admin' && (
                        <Link
                          href="/admin"
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/50 transition-colors duration-200"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Settings className="w-4 h-4 text-warm-500" />
                          <span className="text-cool-700">Admin Portal</span>
                        </Link>
                      )}

                      {user?.role === 'provider' && (
                        <Link
                          href="/provider/dashboard"
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/50 transition-colors duration-200"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Settings className="w-4 h-4 text-secondary-500" />
                          <span className="text-cool-700">Provider Dashboard</span>
                        </Link>
                      )}

                      {user?.role === 'pet_owner' && (
                        <Link
                          href="/dashboard"
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/50 transition-colors duration-200"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <LayoutDashboard className="w-4 h-4 text-primary-500" />
                          <span className="text-cool-700">Dashboard</span>
                        </Link>
                      )}

                      <Link
                        href={user?.role === 'provider' ? '/provider/profile' : '/profile'}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/50 transition-colors duration-200"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="w-4 h-4 text-primary-500" />
                        <span className="text-cool-700">Profile</span>
                      </Link>

                      <button
                        onClick={() => {
                          signOut();
                          setIsUserMenuOpen(false);
                        }}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/50 transition-colors duration-200 w-full text-left"
                      >
                        <LogOut className="w-4 h-4 text-cool-500" />
                        <span className="text-cool-700">Sign Out</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <>
                <Link
                  href="/auth/signin"
                  className="text-cool-700 hover:text-primary-500 font-medium transition-all duration-300 hover:scale-105"
                >
                  Sign In
                </Link>

                <Link
                  href="/auth/signup"
                  className="btn-primary"
                >
                  Get Started
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-charcoal-700 hover:text-primary-500 transition-colors duration-200"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 pb-4 border-t border-primary-200/30">
            <nav className="flex flex-col space-y-4 mt-4">
              <Link
                href="/search"
                className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Find Services
              </Link>
              <Link
                href="/provider"
                className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Providers
              </Link>
              <Link
                href="/blog"
                className="text-gray-700 hover:text-green-600 font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Blog
              </Link>
              <Link
                href="/emergency"
                className="text-gray-700 hover:text-warm-500 font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Emergency
              </Link>
              <Link
                href="/community"
                className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Community
              </Link>
              
              <div className="flex flex-col space-y-3 pt-4 border-t border-primary-200/30">
                {isAuthenticated ? (
                  <>
                    {user?.role === 'pet_owner' && (
                      <Link
                        href="/dashboard"
                        className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Dashboard
                      </Link>
                    )}
                    {user?.role === 'provider' && (
                      <Link
                        href="/provider/dashboard"
                        className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Provider Dashboard
                      </Link>
                    )}
                    <Link
                      href={user?.role === 'provider' ? '/provider/profile' : '/profile'}
                      className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Profile
                    </Link>
                    <button
                      onClick={() => {
                        signOut();
                        setIsMenuOpen(false);
                      }}
                      className="text-left text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      href="/auth/signin"
                      className="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="btn-primary text-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Get Started
                    </Link>
                  </>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
