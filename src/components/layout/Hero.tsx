'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Calendar } from 'lucide-react';
import Image from 'next/image';

export function Hero() {
  const router = useRouter();
  const [searchData, setSearchData] = useState({
    location: '',
    service: '',
    date: '',
    petType: ''
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Build search query parameters
    const searchParams = new URLSearchParams();
    if (searchData.location) searchParams.set('location', searchData.location);
    if (searchData.service) searchParams.set('service', searchData.service);
    if (searchData.date) searchParams.set('date', searchData.date);
    if (searchData.petType) searchParams.set('petType', searchData.petType);

    // Redirect to search page with parameters
    router.push(`/search?${searchParams.toString()}`);
  };

  return (
    <section className="relative py-20 md:py-32 overflow-hidden bg-gradient-to-r from-green-600 to-blue-600">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 via-blue-500/10 to-green-500/20"></div>
      <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-green-400/30 to-blue-400/30 rounded-full blur-2xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-blue-400/30 to-green-400/30 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
      <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-gradient-to-r from-green-300/20 to-blue-300/20 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
      <div className="absolute bottom-1/3 left-1/4 w-28 h-28 bg-gradient-to-r from-blue-300/20 to-green-300/20 rounded-full blur-xl animate-float" style={{ animationDelay: '3s' }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight text-white">
            Find the Perfect{' '}
            <span className="text-yellow-300 animate-pulse">Care</span>{' '}
            for Your Pet
          </h1>
          <p className="text-xl md:text-2xl text-green-100 mb-8 leading-relaxed">
            Book trusted pet services including grooming, veterinary care,
            pet hotels, and daycare - all in one place
          </p>
          
          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center items-center gap-6 mb-12 text-sm text-white">
            <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full border border-white/30">
              <Image
                src="/fetchlylogo.png"
                alt="Fetchly Logo"
                width={16}
                height={16}
                className="w-4 h-4"
              />
              <span>10,000+ Happy Pets</span>
            </div>
            <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full border border-white/30">
              <div className="w-4 h-4 bg-gradient-to-r from-yellow-400 to-green-400 rounded-full"></div>
              <span>Verified Providers</span>
            </div>
            <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full border border-white/30">
              <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-green-400 rounded-full animate-pulse"></div>
              <span>24/7 Support</span>
            </div>
          </div>
        </div>

        {/* Search Form */}
        <div className="max-w-5xl mx-auto">
          <form onSubmit={handleSearch} className="bg-white/95 backdrop-blur-sm rounded-3xl p-6 md:p-8 shadow-2xl border border-white/20">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 md:gap-6">
              {/* Location */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-600" />
                  <input
                    type="text"
                    placeholder="Where are you?"
                    value={searchData.location}
                    onChange={(e) => setSearchData({ ...searchData, location: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-gray-200 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 hover:border-gray-300 text-gray-800 placeholder-gray-500"
                  />
                </div>
              </div>

              {/* Service Type */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Service
                </label>
                <select
                  value={searchData.service}
                  onChange={(e) => setSearchData({ ...searchData, service: e.target.value })}
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 appearance-none bg-white text-gray-800"
                >
                  <option value="">All Services</option>
                  <option value="grooming">Grooming</option>
                  <option value="veterinary">Veterinary</option>
                  <option value="hotel">Pet Hotel</option>
                  <option value="daycare">Daycare</option>
                  <option value="training">Training</option>
                </select>
              </div>

              {/* Date */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="date"
                    value={searchData.date}
                    onChange={(e) => setSearchData({ ...searchData, date: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
              </div>

              {/* Pet Type */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pet Type
                </label>
                <select
                  value={searchData.petType}
                  onChange={(e) => setSearchData({ ...searchData, petType: e.target.value })}
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 appearance-none bg-white"
                >
                  <option value="">All Pets</option>
                  <option value="dog">Dogs</option>
                  <option value="cat">Cats</option>
                  <option value="bird">Birds</option>
                  <option value="rabbit">Rabbits</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            {/* Search Button */}
            <div className="mt-6 flex justify-center">
              <button
                type="submit"
                className="btn-primary flex items-center gap-3 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <Search className="w-5 h-5" />
                Find Pet Services
              </button>
            </div>
          </form>
        </div>

        {/* Popular Searches */}
        <div className="max-w-3xl mx-auto mt-8 text-center">
          <p className="text-gray-500 mb-4">Popular searches:</p>
          <div className="flex flex-wrap justify-center gap-3">
            {['Dog Grooming', 'Cat Vet', 'Pet Hotel', 'Dog Training', 'Emergency Vet'].map((search) => (
              <button
                key={search}
                className="px-4 py-2 bg-white/60 hover:bg-white/80 border border-primary-200/50 rounded-full text-sm text-gray-700 hover:text-primary-600 transition-all duration-200"
              >
                {search}
              </button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
