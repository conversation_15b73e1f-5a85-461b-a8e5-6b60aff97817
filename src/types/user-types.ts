/**
 * Comprehensive User Type Definitions for Fetchly Platform
 * Supports both Pet Owners and Providers with distinct features
 */

// Base user interface (shared by both user types)
export interface BaseUser {
  id: string;
  email: string;
  name: string;
  role: 'pet_owner' | 'provider' | 'admin';
  verified: boolean;
  joinedDate: string;
  profilePicture?: string;
  bannerImage?: string;
  bio?: string;
  location?: string;
  phone?: string;
  
  // Privacy settings
  isProfilePrivate: boolean;
  showEmail: boolean;
  showPhone: boolean;
  allowMessages: boolean;
  
  // Notification preferences
  emailNotifications: boolean;
  pushNotifications: boolean;
  
  // Timestamps
  createdAt?: string;
  updatedAt?: string;
}

// Pet Owner specific user interface
export interface PetOwnerUser extends BaseUser {
  role: 'pet_owner';
  fetchlyBalance: number;
  rewardPoints: number;
}

// Provider specific user interface
export interface ProviderUser extends BaseUser {
  role: 'provider';
  fetchlyBalance: number; // For receiving payments
  rewardPoints: number; // Minimal for providers
}

// Admin user interface
export interface AdminUser extends BaseUser {
  role: 'admin';
  permissions: string[];
  lastLogin?: string;
}

// Union type for all user types
export type User = PetOwnerUser | ProviderUser | AdminUser;

// Pet interface (only for pet owners)
export interface Pet {
  id: string;
  userId: string; // Pet owner's ID
  name: string;
  type: string; // Dog, Cat, Bird, etc.
  breed?: string;
  age?: number;
  weight?: number;
  color?: string;
  gender?: 'male' | 'female';
  isActive: boolean;
  medicalNotes?: string;
  profilePicture?: string;
  createdAt: string;
  updatedAt?: string;
}

// Provider Profile interface (extended business information)
export interface ProviderProfile {
  id: string; // Same as user ID
  userId: string;
  businessName?: string;
  profession: string;
  specialties: string[];
  yearsExperience?: number;
  rating: number;
  totalReviews: number;
  isActive: boolean;
  isVerified: boolean;
  
  // Location details
  location: {
    address?: string;
    city: string;
    state: string;
    zipCode?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  
  // Contact information
  contact: {
    phone?: string;
    email?: string;
    website?: string;
  };
  
  // Business hours
  businessHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}

// Service interface (provided by providers)
export interface Service {
  id: string;
  providerId: string;
  name: string;
  description: string;
  category: string;
  price: number;
  duration: number; // in minutes
  isActive: boolean;
  requirements?: string[];
  createdAt: string;
  updatedAt?: string;
}

// Provider Subscription interface
export interface ProviderSubscription {
  id: string; // Same as provider ID
  providerId: string;
  tier: 'free' | 'pro';
  status: 'active' | 'inactive' | 'cancelled';
  startDate: string;
  endDate?: string;
  nextBillingDate?: string;
  
  // Features based on tier
  features: {
    aiAssistant: boolean;
    calendarSync: boolean;
    customBranding: boolean;
    bulkMessaging: boolean;
    advancedAnalytics: boolean;
    reducedCommission: boolean;
  };
  
  // Pricing
  monthlyPrice: number;
  commissionRate: number; // 0.05 for free, 0.03 for pro
  
  // Timestamps
  createdAt: string;
  updatedAt?: string;
}

// Booking interface (shared between pet owners and providers)
export interface Booking {
  id: string;
  petOwnerId: string;
  providerId: string;
  serviceId: string;
  petId?: string;
  
  // Booking details
  bookingDate: string;
  startTime: string;
  endTime: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  
  // Pricing
  totalAmount: number;
  commissionAmount: number;
  providerEarnings: number;
  
  // Additional information
  notes?: string;
  specialRequests?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt?: string;
}

// Review interface (pet owners review providers)
export interface Review {
  id: string;
  petOwnerId: string;
  providerId: string;
  bookingId?: string;
  
  // Review content
  rating: number; // 1-5 stars
  title?: string;
  comment: string;
  
  // Provider response
  providerResponse?: string;
  providerResponseDate?: string;
  
  // Visibility
  isPublic: boolean;
  isVerified: boolean;
  
  // Timestamps
  createdAt: string;
  updatedAt?: string;
}

// Transaction interfaces
export interface PetOwnerTransaction {
  id: string;
  userId: string; // Pet owner ID
  type: 'wallet_charge' | 'booking_payment' | 'refund';
  amount: number;
  description: string;
  status: 'pending' | 'completed' | 'failed';
  
  // Payment details
  paymentMethod?: string;
  stripePaymentIntentId?: string;
  
  // Related entities
  bookingId?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt?: string;
}

export interface ProviderEarning {
  id: string;
  providerId: string;
  bookingId?: string;
  
  // Earning details
  grossAmount: number;
  commissionAmount: number;
  netAmount: number;
  
  // Status
  status: 'pending' | 'available' | 'withdrawn';
  
  // Withdrawal details
  withdrawalDate?: string;
  withdrawalMethod?: string;
  
  // Timestamps
  date: string;
  createdAt: string;
  updatedAt?: string;
}

// Reward system interfaces
export interface RewardTransaction {
  id: string;
  userId: string; // Pet owner ID
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  
  // Related entities
  bookingId?: string;
  rewardItemId?: string;
  
  // Timestamps
  createdAt: string;
}

export interface RewardItem {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  category: string;
  isActive: boolean;
  
  // Availability
  stockQuantity?: number;
  isUnlimited: boolean;
  
  // Timestamps
  createdAt: string;
  updatedAt?: string;
}

// Type guards for user roles
export function isPetOwner(user: User): user is PetOwnerUser {
  return user.role === 'pet_owner';
}

export function isProvider(user: User): user is ProviderUser {
  return user.role === 'provider';
}

export function isAdmin(user: User): user is AdminUser {
  return user.role === 'admin';
}

// Provider tier type guards
export function isProProvider(subscription: ProviderSubscription): boolean {
  return subscription.tier === 'pro' && subscription.status === 'active';
}

export function isFreeProvider(subscription: ProviderSubscription): boolean {
  return subscription.tier === 'free' && subscription.status === 'active';
}
