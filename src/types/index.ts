import { User as FirebaseUser } from 'firebase/auth';

export interface User {
  id: string;
  uid?: string;
  name: string;
  email: string;
  role: 'pet_owner' | 'provider' | 'admin';
  avatar?: string;
  banner?: string;
  city?: string;
  phone?: string;
  location?: string;
  verified?: boolean;
  joinedDate?: string;
  fetchlyBalance?: number;
  rewardPoints?: number;
}

export interface Post {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  image?: string;
  likes: string[];
  comments: Array<{
    id: string;
    userId: string;
    userName: string;
    content: string;
    timestamp: Date;
  }>;
  timestamp: Date;
  isStory?: boolean;
  expiresAt?: Date;
}

export interface FriendRequest {
  id: string;
  fromUserId: string;
  toUserId: string;
  status: 'pending' | 'accepted' | 'rejected';
  timestamp: Date;
  fromUser?: Pick<User, 'id' | 'name' | 'email' | 'avatar'>;
  toUser?: Pick<User, 'id' | 'name' | 'email' | 'avatar'>;
}

export interface Provider {
  id: string;
  name: string;
  email: string;
  role: 'provider';
  avatar?: string;
  banner?: string;
  city?: string;
  phone?: string;
  location?: string;
  verified?: boolean;
  joinedDate?: string;
  services?: string[];
  rating?: number;
  reviewCount?: number;
}
