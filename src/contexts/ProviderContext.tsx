'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { 
  Provider, 
  Service, 
  Booking, 
  getProviderByUserId, 
  updateProvider,
  getProviderServices,
  getProviderBookings,
  getProviderEarnings
} from '@/lib/firebase/providers';
import toast from 'react-hot-toast';

interface ProviderContextType {
  provider: Provider | null;
  services: Service[];
  bookings: Booking[];
  earnings: any[];
  payouts: any[];
  isLoading: boolean;
  error: string | null;
  
  // Provider management
  updateProviderProfile: (updates: Partial<Provider>) => Promise<void>;
  refreshProviderData: () => Promise<void>;
  
  // Services management
  refreshServices: () => Promise<void>;
  createService: (serviceData: any) => Promise<void>;
  updateService: (serviceId: string, updates: any) => Promise<void>;
  deleteService: (serviceId: string) => Promise<void>;
  
  // Bookings management
  refreshBookings: () => Promise<void>;
  updateBookingStatus: (bookingId: string, status: string) => Promise<void>;
  
  // Earnings management
  refreshEarnings: () => Promise<void>;
  
  // Dashboard stats
  getDashboardStats: () => {
    totalBookings: number;
    totalRevenue: number;
    rating: number;
    reviewCount: number;
    responseRate: number;
    completionRate: number;
  };
}

const ProviderContext = createContext<ProviderContextType | undefined>(undefined);

export function ProviderProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [provider, setProvider] = useState<Provider | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [earnings, setEarnings] = useState<any[]>([]);
  const [payouts, setPayouts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Real-time provider data when user changes
  useEffect(() => {
    if (!user || user.role !== 'provider') {
      setProvider(null);
      setServices([]);
      setBookings([]);
      setEarnings([]);
      setPayouts([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    // Firestore imports
    let providerUnsub: (() => void) | null = null;
    let bookingsUnsub: (() => void) | null = null;
    let earningsUnsub: (() => void) | null = null;
    let servicesUnsub: (() => void) | null = null;

    // Dynamically import Firestore and db/config
    (async () => {
      const { onSnapshot, collection, query, where, limit } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/config');

      // Listen to provider profile by userId
      const providerQuery = query(
        collection(db, 'providers'),
        where('userId', '==', user.id),
        limit(1)
      );
      providerUnsub = onSnapshot(providerQuery, (snapshot) => {
        if (!snapshot.empty) {
          const doc = snapshot.docs[0];
          setProvider({ id: doc.id, ...doc.data() });
        } else {
          setProvider(null);
        }
        setIsLoading(false);
      }, (err) => {
        setError('Failed to load provider data');
        setIsLoading(false);
      });

      // Listen to bookings, earnings, services after provider is set
      // Use a separate effect for these, since provider id may not be known immediately
    })();

    // Cleanup
    return () => {
      providerUnsub && providerUnsub();
      bookingsUnsub && bookingsUnsub();
      earningsUnsub && earningsUnsub();
      servicesUnsub && servicesUnsub();
    };
  }, [user]);

  // Real-time listeners for bookings, earnings, services (after provider is set)
  useEffect(() => {
    if (!provider?.id) {
      setBookings([]);
      setEarnings([]);
      setServices([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    let bookingsUnsub: (() => void) | null = null;
    let earningsUnsub: (() => void) | null = null;
    let servicesUnsub: (() => void) | null = null;

    (async () => {
      const { onSnapshot, collection, query, where } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/config');

      // Bookings
      const bookingsQuery = query(
        collection(db, 'bookings'),
        where('providerId', '==', provider.id)
      );
      bookingsUnsub = onSnapshot(bookingsQuery, (snapshot) => {
        setBookings(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
        setIsLoading(false);
      }, (err) => {
        setError('Failed to load bookings');
        setIsLoading(false);
      });

      // Earnings
      const earningsQuery = query(
        collection(db, 'earnings'),
        where('providerId', '==', provider.id)
      );
      earningsUnsub = onSnapshot(earningsQuery, (snapshot) => {
        setEarnings(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
        setIsLoading(false);
      }, (err) => {
        setError('Failed to load earnings');
        setIsLoading(false);
      });

      // Services
      const servicesQuery = query(
        collection(db, 'services'),
        where('providerId', '==', provider.id)
      );
      servicesUnsub = onSnapshot(servicesQuery, (snapshot) => {
        setServices(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })));
        setIsLoading(false);
      }, (err) => {
        setError('Failed to load services');
        setIsLoading(false);
      });
    })();

    return () => {
      bookingsUnsub && bookingsUnsub();
      earningsUnsub && earningsUnsub();
      servicesUnsub && servicesUnsub();
    };
  }, [provider?.id]);

  // Deprecated: const loadProviderData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      // Load provider profile
      console.log('Loading provider data for user:', user.id);
      const providerData = await getProviderByUserId(user.id);
      console.log('Provider data loaded:', providerData);

      // If no provider profile exists and user is a provider, create one
      if (!providerData && user.role === 'provider') {
        console.log('No provider profile found for provider user. Creating default profile...');
        try {
          const { createProvider } = await import('@/lib/firebase/providers');

          const newProviderId = await createProvider({
            userId: user.id,
            businessName: user.name + "'s Business",
            ownerName: user.name,
            email: user.email,
            phone: user.phone || '',
            serviceType: 'General Pet Services',
            address: '',
            city: '',
            state: '',
            zipCode: '',
            description: 'Welcome to my pet services business!',
            experience: '0-1 years',
            specialties: [],
            status: 'pending',
            verified: false,
            featured: false,
            businessHours: {
              monday: { open: '09:00', close: '17:00', closed: false },
              tuesday: { open: '09:00', close: '17:00', closed: false },
              wednesday: { open: '09:00', close: '17:00', closed: false },
              thursday: { open: '09:00', close: '17:00', closed: false },
              friday: { open: '09:00', close: '17:00', closed: false },
              saturday: { open: '10:00', close: '16:00', closed: false },
              sunday: { open: '10:00', close: '16:00', closed: true }
            },
            profilePhoto: '/favicon.png', // Default profile picture
            bannerPhoto: '/fetchlylogo.png' // Default banner
          });

          // Reload the provider data after creation
          const newProviderData = await getProviderByUserId(user.id);
          setProvider(newProviderData);

          if (newProviderData) {
            toast.success('Provider profile created successfully!');
          }
        } catch (createError) {
          console.error('Error creating provider profile:', createError);
          setError('Failed to create provider profile');
          toast.error('Failed to create provider profile. Please try again.');
          return;
        }
      } else {
        setProvider(providerData);
      }

      if (providerData || provider) {
        const currentProvider = providerData || provider;
        if (currentProvider?.id) {
          // Load related data in parallel
          const [servicesData, bookingsData, earningsData] = await Promise.all([
            getProviderServices(currentProvider.id),
            getProviderBookings(currentProvider.id),
            getProviderEarnings(currentProvider.id)
          ]);

          setServices(servicesData);
          setBookings(bookingsData);
          setEarnings(earningsData);
        }
      }
    } catch (err) {
      console.error('Error loading provider data:', err);
      setError('Failed to load provider data');
      toast.error('Failed to load provider data');
    } finally {
      setIsLoading(false);
    }
  };

  const updateProviderProfile = async (updates: Partial<Provider>) => {
    if (!provider) {
      console.error('No provider found when trying to update profile');
      throw new Error('Provider not found');
    }

    try {
      console.log('🔄 Updating provider profile with ID:', provider.id);
      console.log('📝 Updates being applied:', updates);

      // Update in Firebase
      await updateProvider(provider.id!, updates);
      console.log('✅ Firebase update completed');

      // Update local state immediately with merged data
      const updatedProvider = { ...provider, ...updates };
      setProvider(updatedProvider);
      console.log('🔄 Local state updated');

      // Verify the update by reloading from database
      setTimeout(async () => {
        try {
          const freshProvider = await getProviderByUserId(user?.id || '');
          if (freshProvider) {
            setProvider(freshProvider);
            console.log('✅ Provider data verified and refreshed from database');
            console.log('📊 Fresh provider data:', {
              businessName: freshProvider.businessName,
              description: freshProvider.description,
              profilePhoto: freshProvider.profilePhoto,
              bannerPhoto: freshProvider.bannerPhoto,
              phone: freshProvider.phone,
              website: freshProvider.website
            });
          }
        } catch (error) {
          console.error('⚠️ Error verifying provider update:', error);
        }
      }, 1000);

      console.log('✅ Profile update completed successfully');
    } catch (err) {
      console.error('❌ Error updating provider profile:', err);
      toast.error('Failed to update profile');
      throw err;
    }
  };

  const refreshProviderData = async () => {
    await loadProviderData();
  };

  const refreshServices = async () => {
    if (!provider) return;

    try {
      const servicesData = await getProviderServices(provider.id!);
      setServices(servicesData);
    } catch (err) {
      console.error('Error refreshing services:', err);
      toast.error('Failed to refresh services');
    }
  };

  const createService = async (serviceData: any) => {
    if (!user) {
      toast.error('You must be logged in to create services');
      return;
    }

    if (!provider?.id) {
      console.error('Provider not found. User:', user.id, 'Provider:', provider);

      // Try to reload provider data first
      try {
        await loadProviderData();

        // Check again after reload
        if (!provider?.id) {
          toast.error('Provider profile not found. Please complete your provider profile setup first.');
          return;
        }
      } catch (error) {
        console.error('Error reloading provider data:', error);
        toast.error('Unable to load provider profile. Please try again or contact support.');
        return;
      }
    }

    try {
      const { createService: createServiceFn } = await import('@/lib/firebase/providers');

      await createServiceFn({
        ...serviceData,
        providerId: provider.id
      });

      // Refresh services to get updated data
      await refreshServices();

      toast.success('Service created successfully!');
    } catch (err) {
      console.error('Error creating service:', err);
      toast.error('Failed to create service');
      throw err;
    }
  };

  const updateService = async (serviceId: string, updates: any) => {
    if (!user || !provider) {
      toast.error('You must be logged in as a provider to update services');
      return;
    }

    try {
      const { updateService: updateServiceFn } = await import('@/lib/firebase/providers');

      await updateServiceFn(serviceId, updates);

      // Refresh services to get updated data
      await refreshServices();

      toast.success('Service updated successfully!');
    } catch (err) {
      console.error('Error updating service:', err);
      toast.error('Failed to update service');
    }
  };

  const deleteService = async (serviceId: string) => {
    if (!user || !provider) {
      toast.error('You must be logged in as a provider to delete services');
      return;
    }

    try {
      const { deleteService: deleteServiceFn } = await import('@/lib/firebase/providers');

      await deleteServiceFn(serviceId);

      // Refresh services to get updated data
      await refreshServices();

      toast.success('Service deleted successfully!');
    } catch (err) {
      console.error('Error deleting service:', err);
      toast.error('Failed to delete service');
    }
  };

  const refreshBookings = async () => {
    if (!provider) return;
    
    try {
      const bookingsData = await getProviderBookings(provider.id!);
      setBookings(bookingsData);
    } catch (err) {
      console.error('Error refreshing bookings:', err);
      toast.error('Failed to refresh bookings');
    }
  };

  const refreshEarnings = async () => {
    if (!provider) return;
    
    try {
      const earningsData = await getProviderEarnings(provider.id!);
      setEarnings(earningsData);
    } catch (err) {
      console.error('Error refreshing earnings:', err);
      toast.error('Failed to refresh earnings');
    }
  };

  const getDashboardStats = () => {
    if (!provider) {
      return {
        totalBookings: 0,
        totalRevenue: 0,
        rating: 0,
        reviewCount: 0,
        responseRate: 0,
        completionRate: 0
      };
    }

    return {
      totalBookings: provider.totalBookings || 0,
      totalRevenue: provider.totalRevenue || 0,
      rating: provider.rating || 0,
      reviewCount: provider.reviewCount || 0,
      responseRate: provider.responseRate || 0,
      completionRate: provider.completionRate || 0
    };
  };

  const updateBookingStatus = async (bookingId: string, status: string) => {
    if (!provider?.id) {
      toast.error('Provider not found');
      return;
    }

    try {
      // Update booking status in Firebase
      const { updateDoc, doc } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/config');

      await updateDoc(doc(db, 'bookings', bookingId), {
        status,
        updatedAt: new Date()
      });

      // Refresh bookings to get updated data
      await refreshBookings();

      toast.success(`Booking ${status === 'confirmed' ? 'approved' : status} successfully`);
    } catch (err) {
      console.error('Error updating booking status:', err);
      toast.error('Failed to update booking status');
    }
  };

  const value: ProviderContextType = {
    provider,
    services,
    bookings,
    earnings,
    payouts,
    isLoading,
    error,
    updateProviderProfile,
    refreshProviderData,
    refreshServices,
    createService,
    updateService,
    deleteService,
    refreshBookings,
    updateBookingStatus,
    refreshEarnings,
    getDashboardStats
  };

  return (
    <ProviderContext.Provider value={value}>
      {children}
    </ProviderContext.Provider>
  );
}

export function useProvider() {
  const context = useContext(ProviderContext);
  if (context === undefined) {
    throw new Error('useProvider must be used within a ProviderProvider');
  }
  return context;
}
