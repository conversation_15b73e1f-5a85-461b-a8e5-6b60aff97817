'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  getCountFromServer, 
  Timestamp, 
  onSnapshot, 
  doc, 
  getDoc, 
  orderBy, 
  limit,
  updateDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { useAuth } from './AuthContext';

interface AdminStats {
  totalUsers: number;
  activeProviders: number;
  monthlyBookings: number;
  totalRevenue: number;
  recentActivity: any[];
  pendingApprovals: any[];
  isLoading: boolean;
  error: string | null;
}

interface AdminContextType extends AdminStats {
  refreshData: () => Promise<void>;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

export function AdminProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [stats, setStats] = useState<Omit<AdminStats, 'isLoading' | 'error'>>({
    totalUsers: 0,
    activeProviders: 0,
    monthlyBookings: 0,
    totalRevenue: 0,
    recentActivity: [],
    pendingApprovals: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    if (!user || user.role !== 'admin') return;
    
    try {
      setIsLoading(true);
      setError(null);

      // Get current month's start and end dates
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      // Fetch total users count
      const usersQuery = collection(db, 'users');
      const usersSnapshot = await getCountFromServer(usersQuery);
      const totalUsers = usersSnapshot.data().count;

      // Fetch active providers count
      const providersQuery = query(
        collection(db, 'providers'),
        where('status', '==', 'approved')
      );
      const providersSnapshot = await getCountFromServer(providersQuery);
      const activeProviders = providersSnapshot.data().count;

      // Fetch monthly bookings count
      const bookingsQuery = query(
        collection(db, 'bookings'),
        where('createdAt', '>=', Timestamp.fromDate(firstDayOfMonth)),
        where('createdAt', '<=', Timestamp.fromDate(lastDayOfMonth))
      );
      const bookingsSnapshot = await getCountFromServer(bookingsQuery);
      const monthlyBookings = bookingsSnapshot.data().count;

      // Fetch total revenue (simplified - in a real app, you'd sum up transaction amounts)
      // This is a placeholder - you'd need to implement actual revenue calculation
      const totalRevenue = 0; // Implement actual revenue calculation

      setStats({
        totalUsers,
        activeProviders,
        monthlyBookings,
        totalRevenue,
        recentActivity: [], // Will be populated by real-time listener
        pendingApprovals: [] // Will be populated by real-time listener
      });

      // Set up real-time listeners
      setupRealtimeListeners();
    } catch (err) {
      console.error('Error fetching admin stats:', err);
      setError('Failed to load admin dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const setupRealtimeListeners = () => {
    if (!user || user.role !== 'admin') return;

    // Listen for recent activity
    const activityQuery = query(
      collection(db, 'activity'),
      orderBy('timestamp', 'desc'),
      limit(10)
    );
    
    const unsubscribeActivity = onSnapshot(activityQuery, (snapshot) => {
      const activities = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      }));
      
      setStats(prev => ({
        ...prev,
        recentActivity: activities
      }));
    });

    // Listen for pending approvals
    const approvalsQuery = query(
      collection(db, 'approvals'),
      where('status', '==', 'pending')
    );
    
    const unsubscribeApprovals = onSnapshot(approvalsQuery, (snapshot) => {
      const approvals = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setStats(prev => ({
        ...prev,
        pendingApprovals: approvals
      }));
    });

    // Cleanup function
    return () => {
      unsubscribeActivity();
      unsubscribeApprovals();
    };
  };

  // Initial data fetch
  useEffect(() => {
    fetchStats();
  }, [user]);

  return (
    <AdminContext.Provider
      value={{
        ...stats,
        isLoading,
        error,
        refreshData: fetchStats
      }}
    >
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
}
