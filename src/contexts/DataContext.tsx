'use client';

import { createContext, useContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import { useAuth } from './AuthContext';
import { Pet, PetService } from '@/lib/pets';
import { collection, query, where, orderBy, onSnapshot, addDoc, updateDoc, doc, deleteDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import toast from 'react-hot-toast';

export interface Post {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  image?: string;
  likes: number;
  comments: number;
  timestamp: Date;
  isLiked: boolean;
  isPublic: boolean;
  isStory: boolean;
  likedBy: string[];
}

interface Stats {
  posts: number;
  following: number;
  followers: number;
  totalPets: number;
  upcomingAppointments: number;
  rewardPoints: number;
  walletBalance: number;
  totalPosts: number;
  totalLikes: number;
}

export interface Comment {
  id: string;
  postId: string;
  userId: string;
  userName: string;
}

interface DataContextType {
  posts: Post[];
  userPosts: Post[];
  publicPosts: Post[];
  createPost: (content: string, image?: string, isPublic?: boolean, isStory?: boolean) => Promise<void>;
  updatePost: (postId: string, updates: Partial<Post>) => Promise<void>;
  deletePost: (postId: string) => Promise<void>;
  toggleLike: (postId: string) => Promise<void>;
  getPostById: (id: string) => Post | undefined;
  pets: Pet[];
  refreshPets: () => Promise<void>;
  stats: Stats;
  loading: boolean;
  postsLoading: boolean;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export function DataProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [posts, setPosts] = useState<Post[]>([]);
  const [pets, setPets] = useState<Pet[]>([]);
  const [loading, setLoading] = useState(true);
  const [postsLoading, setPostsLoading] = useState(true);
  
  // Create a firebaseUser that combines Firebase User with our custom User type
  const firebaseUser = useMemo(() => (user ? { ...user, id: user.id } : null), [user]);
  
  // Computed values
  const userPosts = useMemo(() => posts.filter(post => post.userId === firebaseUser?.id), [posts, firebaseUser?.id]);

  // Load user's pets
  const refreshPets = useCallback(async () => {
    if (!firebaseUser?.id) return;
    
    try {
      const userPets = await PetService.getPetsByUser(firebaseUser.id);
      setPets(userPets);
    } catch (error) {
      console.error('Error loading pets:', error);
      toast.error('Failed to load pets');
    }
  }, [firebaseUser?.id]);

  // Load posts from Firestore
  useEffect(() => {
    if (!firebaseUser?.id) {
      // Only load public posts for unauthenticated users
      console.log('Setting up public posts listener for unauthenticated user');
      const publicPostsQuery = query(
        collection(db, 'posts'),
        where('isPublic', '==', true),
        orderBy('timestamp', 'desc')
      );

      const unsubscribe = onSnapshot(publicPostsQuery, 
        (snapshot) => {
          try {
            const publicPostsData: Post[] = [];
            snapshot.forEach((doc) => {
              const data = doc.data();
              publicPostsData.push({
                id: doc.id,
                userId: data.userId,
                userName: data.userName,
                userAvatar: data.userAvatar || '',
                content: data.content,
                image: data.image || '',
                likes: data.likes || 0,
                comments: data.comments || 0,
                timestamp: data.timestamp?.toDate() || new Date(),
                isLiked: false, // Unauthenticated users can't like
                isPublic: true,
                isStory: data.isStory || false,
                likedBy: data.likedBy || []
              });
            });
            setPosts(publicPostsData);
          } catch (error) {
            console.error('Error processing public posts:', error);
            toast.error('Failed to load posts');
          } finally {
            setPostsLoading(false);
            setLoading(false);
          }
        },
        (error) => {
          console.error('Error loading public posts:', error);
          toast.error('Failed to load posts');
          setPostsLoading(false);
          setLoading(false);
        }
      );

      return () => unsubscribe();
    }

    // For authenticated users, load both public and private posts
    console.log('Setting up posts listener for user:', firebaseUser.id);
    
    // Load pets for authenticated users
    refreshPets();

    // Set up real-time posts listener for all posts
    const postsQuery = query(
      collection(db, 'posts'),
      orderBy('timestamp', 'desc')
    );

    const unsubscribe = onSnapshot(postsQuery, 
      (snapshot) => {
        try {
          const postsData: Post[] = [];
          snapshot.forEach((doc) => {
            const data = doc.data();
            // Only include public posts or posts by the current user
            if (data.isPublic || data.userId === firebaseUser.id) {
              postsData.push({
                id: doc.id,
                userId: data.userId,
                userName: data.userName,
                userAvatar: data.userAvatar || '',
                content: data.content,
                image: data.image || '',
                likes: data.likes || 0,
                comments: data.comments || 0,
                timestamp: data.timestamp?.toDate() || new Date(),
                isLiked: (data.likedBy || []).includes(firebaseUser.id),
                isPublic: data.isPublic !== false, // Default to true if not set
                isStory: data.isStory || false,
                likedBy: data.likedBy || []
              });
            }
          });
          setPosts(postsData);
        } catch (error) {
          console.error('Error processing posts:', error);
          toast.error('Failed to load posts');
        } finally {
          setPostsLoading(false);
          setLoading(false);
        }
      },
      (error) => {
        console.error('Error loading posts:', error);
        toast.error('Failed to load posts');
        setPostsLoading(false);
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [firebaseUser?.id]);

  // Create a new post or story
  const createPost = async (content: string, image?: string, isPublic: boolean = true, isStory: boolean = false) => {
    if (!firebaseUser) {
      toast.error('You must be logged in to create a post');
      return;
    }

    try {
      setPostsLoading(true);
      
      const postData: Omit<Post, 'id'> = {
        userId: firebaseUser.id,
        userName: firebaseUser.name || 'Anonymous',
        userAvatar: firebaseUser.avatar || '/default-avatar.png',
        content,
        image,
        likes: 0,
        comments: 0,
        timestamp: new Date(),
        isLiked: false,
        isPublic,
        isStory,
        likedBy: []
      };

      // Add the post to Firestore
      const docRef = await addDoc(collection(db, 'posts'), postData);
      
      // Update local state optimistically
      const newPost: Post = { id: docRef.id, ...postData };
      setPosts(prevPosts => [newPost, ...prevPosts]);
      
      toast.success(isStory ? 'Story created successfully!' : 'Post created successfully!');
    } catch (error) {
      console.error('Error creating post:', error);
      toast.error(`Failed to create ${isStory ? 'story' : 'post'}`);
    } finally {
      setPostsLoading(false);
    }
  };

  // Update a post
  const updatePost = async (postId: string, updates: Partial<Post>) => {
    try {
      const postRef = doc(db, 'posts', postId);
      await updateDoc(postRef, {
        ...updates,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating post:', error);
      throw error;
    }
  };

  // Delete a post
  const deletePost = async (postId: string) => {
    try {
      await deleteDoc(doc(db, 'posts', postId));
      toast.success('Post deleted successfully');
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('Failed to delete post');
    }
  };

  // Toggle like on a post
  const toggleLike = useCallback(async (postId: string) => {
    if (!firebaseUser?.id) {
      console.log('User not authenticated');
      return;
    }

    try {
      const post = posts.find(p => p.id === postId);
      if (!post) {
        console.error('Post not found');
        return;
      }

      const postRef = doc(db, 'posts', postId);
      const isLiked = post.likedBy.includes(firebaseUser.id);

      if (isLiked) {
        // Unlike the post
        await updateDoc(postRef, {
          likes: Math.max(0, post.likes - 1),
          likedBy: post.likedBy.filter(id => id !== firebaseUser.id)
        });
      } else {
        // Like the post
        await updateDoc(postRef, {
          likes: post.likes + 1,
          likedBy: [...post.likedBy, firebaseUser.id]
        });
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Failed to update like');
    }
  }, [posts, firebaseUser?.id]);

  // Get post by ID function
  const getPostById = useCallback((postId: string): Post | undefined => {
    return posts.find(p => p.id === postId);
  }, [posts]);

  // Context value
  const value = useMemo(() => ({
    posts,
    userPosts,
    publicPosts: posts.filter(post => post.isPublic && !post.isStory),
    createPost,
    updatePost,
    deletePost,
    toggleLike,
    getPostById,
    pets,
    refreshPets,
    stats: {
      posts: posts.length,
      following: 0, // Will be implemented
      followers: 0, // Will be implemented
      totalPets: pets.length,
      upcomingAppointments: 0, // Will be implemented
      rewardPoints: 0, // Will be implemented
      walletBalance: 0, // Will be implemented
      totalPosts: posts.filter(post => post.userId === firebaseUser?.id).length,
      totalLikes: posts.reduce((sum: number, post: Post) => sum + (post.likes || 0), 0)
    },
    loading,
    postsLoading
  }), [posts, userPosts, firebaseUser?.id, createPost, updatePost, deletePost, toggleLike, getPostById, pets, refreshPets, loading, postsLoading]);

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
}

// Hook to use the DataContext
export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}
