import { stripe, logProductionEvent } from './server-config';
import { PRODUCTION_CONFIG, ProductionStripeService, StripeProductionError } from './production-config';
import { adminDb } from '@/lib/firebase-admin';
import { COLLECTIONS } from '@/lib/database';
import <PERSON><PERSON> from 'stripe';

export class FetchlyPaymentService {
  
  /**
   * Create a payment intent for wallet top-up
   */
  static async createWalletTopUpIntent(
    userId: string,
    amount: number,
    paymentMethodId?: string
  ): Promise<{ success: boolean; clientSecret?: string; error?: string }> {
    try {
      // Validate amount
      if (!ProductionStripeService.validatePaymentAmount(amount, 'wallet')) {
        throw new StripeProductionError(
          `Amount must be between $${PRODUCTION_CONFIG.MIN_WALLET_TOPUP} and $${PRODUCTION_CONFIG.MAX_WALLET_TOPUP}`,
          'INVALID_AMOUNT'
        );
      }

      const amountInCents = ProductionStripeService.dollarsToCents(amount);

      const paymentIntent = await stripe.paymentIntents.create({
        amount: amountInCents,
        currency: PRODUCTION_CONFIG.DEFAULT_CURRENCY,
        payment_method: paymentMethodId,
        confirmation_method: 'manual',
        confirm: !!paymentMethodId,
        metadata: {
          type: 'wallet_topup',
          userId,
          amount: amount.toString(),
        },
      });

      logProductionEvent('wallet_topup_intent_created', {
        userId,
        amount,
        paymentIntentId: paymentIntent.id,
      });

      return {
        success: true,
        clientSecret: paymentIntent.client_secret!,
      };
    } catch (error: any) {
      logProductionEvent('wallet_topup_intent_failed', { userId, amount, error: error.message }, 'error');
      return {
        success: false,
        error: error.message || 'Failed to create payment intent',
      };
    }
  }

  /**
   * Create a checkout session for service payment
   */
  static async createServiceCheckoutSession(
    serviceId: string,
    providerId: string,
    userId: string,
    amount: number,
    serviceName: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<{ success: boolean; sessionUrl?: string; error?: string }> {
    try {
      // Validate amount
      if (!ProductionStripeService.validatePaymentAmount(amount, 'service')) {
        throw new StripeProductionError(
          `Service amount must be between $${PRODUCTION_CONFIG.MIN_SERVICE_PAYMENT} and $${PRODUCTION_CONFIG.MAX_SERVICE_PAYMENT}`,
          'INVALID_AMOUNT'
        );
      }

      const platformFee = ProductionStripeService.calculatePlatformFee(amount);
      const providerEarnings = ProductionStripeService.calculateProviderEarnings(amount);

      // Get provider's Stripe account
      if (!adminDb) throw new Error('Database not available');
      
      const providerDoc = await adminDb.collection(COLLECTIONS.PROVIDERS).doc(providerId).get();
      if (!providerDoc.exists) {
        throw new StripeProductionError('Provider not found', 'PROVIDER_NOT_FOUND', 404);
      }

      const provider = providerDoc.data();
      if (!provider?.stripeAccountId) {
        throw new StripeProductionError('Provider not connected to Stripe', 'PROVIDER_NOT_CONNECTED');
      }

      const session = await stripe.checkout.sessions.create({
        payment_method_types: PRODUCTION_CONFIG.PAYMENT_METHODS,
        line_items: [
          {
            price_data: {
              currency: PRODUCTION_CONFIG.DEFAULT_CURRENCY,
              product_data: {
                name: serviceName,
                description: `Pet service provided by ${provider.businessName || provider.ownerName}`,
              },
              unit_amount: ProductionStripeService.dollarsToCents(amount),
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: successUrl,
        cancel_url: cancelUrl,
        payment_intent_data: {
          application_fee_amount: ProductionStripeService.dollarsToCents(platformFee),
          transfer_data: {
            destination: provider.stripeAccountId,
          },
          metadata: {
            type: 'service_payment',
            serviceId,
            providerId,
            userId,
            platformFee: platformFee.toString(),
            providerEarnings: providerEarnings.toString(),
          },
        },
        metadata: {
          type: 'service_payment',
          serviceId,
          providerId,
          userId,
        },
      });

      logProductionEvent('service_checkout_created', {
        serviceId,
        providerId,
        userId,
        amount,
        platformFee,
        sessionId: session.id,
      });

      return {
        success: true,
        sessionUrl: session.url!,
      };
    } catch (error: any) {
      logProductionEvent('service_checkout_failed', { serviceId, providerId, userId, error: error.message }, 'error');
      return {
        success: false,
        error: error.message || 'Failed to create checkout session',
      };
    }
  }

  /**
   * Create an invoice for provider services
   */
  static async createProviderInvoice(
    providerId: string,
    amount: number,
    description: string,
    customerEmail?: string
  ): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      // Validate amount
      if (!ProductionStripeService.validatePaymentAmount(amount, 'invoice')) {
        throw new StripeProductionError(
          `Invoice amount must be between $${PRODUCTION_CONFIG.MIN_INVOICE_AMOUNT} and $${PRODUCTION_CONFIG.MAX_INVOICE_AMOUNT}`,
          'INVALID_AMOUNT'
        );
      }

      // Get provider's Stripe account
      if (!adminDb) throw new Error('Database not available');
      
      const providerDoc = await adminDb.collection(COLLECTIONS.PROVIDERS).doc(providerId).get();
      if (!providerDoc.exists) {
        throw new StripeProductionError('Provider not found', 'PROVIDER_NOT_FOUND', 404);
      }

      const provider = providerDoc.data();
      if (!provider?.stripeAccountId) {
        throw new StripeProductionError('Provider not connected to Stripe', 'PROVIDER_NOT_CONNECTED');
      }

      const platformFee = ProductionStripeService.calculatePlatformFee(amount);

      // Create customer if email provided
      let customer;
      if (customerEmail) {
        customer = await stripe.customers.create({
          email: customerEmail,
        });
      }

      // Create invoice
      const invoice = await stripe.invoices.create({
        customer: customer?.id,
        collection_method: 'send_invoice',
        days_until_due: 30,
        application_fee_amount: ProductionStripeService.dollarsToCents(platformFee),
        transfer_data: {
          destination: provider.stripeAccountId,
        },
        metadata: {
          type: 'provider_invoice',
          providerId,
          platformFee: platformFee.toString(),
        },
      });

      // Add invoice item
      await stripe.invoiceItems.create({
        customer: customer?.id,
        invoice: invoice.id,
        amount: ProductionStripeService.dollarsToCents(amount),
        currency: PRODUCTION_CONFIG.DEFAULT_CURRENCY,
        description,
      });

      // Finalize and send invoice
      const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);

      logProductionEvent('provider_invoice_created', {
        providerId,
        amount,
        platformFee,
        invoiceId: invoice.id,
      });

      return {
        success: true,
        invoiceUrl: finalizedInvoice.hosted_invoice_url!,
      };
    } catch (error: any) {
      logProductionEvent('provider_invoice_failed', { providerId, amount, error: error.message }, 'error');
      return {
        success: false,
        error: error.message || 'Failed to create invoice',
      };
    }
  }

  /**
   * Process wallet payment for services
   */
  static async processWalletPayment(
    userId: string,
    providerId: string,
    serviceId: string,
    amount: number,
    serviceName: string
  ): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    try {
      if (!adminDb) throw new Error('Database not available');

      // Get user's wallet balance
      const userDoc = await adminDb.collection(COLLECTIONS.USERS).doc(userId).get();
      if (!userDoc.exists) {
        throw new StripeProductionError('User not found', 'USER_NOT_FOUND', 404);
      }

      const user = userDoc.data();
      const walletBalance = user?.fetchlyBalance || 0;

      if (walletBalance < amount) {
        throw new StripeProductionError('Insufficient wallet balance', 'INSUFFICIENT_FUNDS');
      }

      // Get provider info
      const providerDoc = await adminDb.collection(COLLECTIONS.PROVIDERS).doc(providerId).get();
      if (!providerDoc.exists) {
        throw new StripeProductionError('Provider not found', 'PROVIDER_NOT_FOUND', 404);
      }

      const provider = providerDoc.data();
      const platformFee = ProductionStripeService.calculatePlatformFee(amount);
      const providerEarnings = ProductionStripeService.calculateProviderEarnings(amount);

      // Create transaction record
      const transactionRef = await adminDb.collection(COLLECTIONS.TRANSACTIONS).add({
        userId,
        providerId,
        serviceId,
        type: 'wallet_payment',
        amount,
        platformFee,
        providerEarnings,
        status: 'completed',
        paymentMethod: 'wallet',
        serviceName,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update user's wallet balance
      await adminDb.collection(COLLECTIONS.USERS).doc(userId).update({
        fetchlyBalance: walletBalance - amount,
        updatedAt: new Date(),
      });

      // Update provider's earnings
      const currentEarnings = provider.totalEarnings || 0;
      await adminDb.collection(COLLECTIONS.PROVIDERS).doc(providerId).update({
        totalEarnings: currentEarnings + providerEarnings,
        updatedAt: new Date(),
      });

      logProductionEvent('wallet_payment_processed', {
        userId,
        providerId,
        serviceId,
        amount,
        platformFee,
        transactionId: transactionRef.id,
      });

      return {
        success: true,
        transactionId: transactionRef.id,
      };
    } catch (error: any) {
      logProductionEvent('wallet_payment_failed', { userId, providerId, serviceId, error: error.message }, 'error');
      return {
        success: false,
        error: error.message || 'Failed to process wallet payment',
      };
    }
  }
}
