import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from './config';

export interface Provider {
  id: string;
  businessName: string;
  ownerName: string;
  serviceType: string;
  rating: number;
  reviewCount: number;
  city: string;
  state: string;
  zipCode: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  description: string;
  specialties: string[];
  verified: boolean;
  featured: boolean;
  profilePhoto?: string;
  responseTime: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  activeServices?: any[];
  hasActiveServices?: boolean;
  distance?: number;
  businessHours?: any;
}

export interface SearchParams {
  query?: string;
  zipCode?: string;
  city?: string;
  state?: string;
  serviceType?: string;
  radius?: number;
}

/**
 * Search providers in Firebase based on various criteria
 */
export const searchFirebaseProviders = async (searchParams: SearchParams): Promise<Provider[]> => {
  try {
    console.log('🔍 Searching Firebase providers with params:', searchParams);

    // Get all approved providers with active services
    const providersQuery = query(
      collection(db, 'providers'),
      where('status', '==', 'approved')
    );

    const providersSnapshot = await getDocs(providersQuery);
    console.log(`📊 Found ${providersSnapshot.size} approved providers`);

    if (providersSnapshot.empty) {
      console.log('❌ No approved providers found in database');
      return [];
    }

    // Get all providers data
    const allProviders = providersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Provider[];

    // Filter providers that have active services
    const providersWithServices = await Promise.all(
      allProviders.map(async (provider) => {
        try {
          const servicesQuery = query(
            collection(db, 'services'),
            where('providerId', '==', provider.id),
            where('active', '==', true)
          );

          const servicesSnapshot = await getDocs(servicesQuery);
          const activeServices = servicesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

          return {
            ...provider,
            activeServices,
            hasActiveServices: activeServices.length > 0
          };
        } catch (error) {
          console.error(`❌ Error getting services for provider ${provider.id}:`, error);
          return {
            ...provider,
            activeServices: [],
            hasActiveServices: false
          };
        }
      })
    );

    // Only include providers with active services
    let filteredProviders = providersWithServices.filter(provider => provider.hasActiveServices);
    console.log(`✅ Found ${filteredProviders.length} providers with active services`);

    // Apply search filters
    if (searchParams.query) {
      const searchQuery = searchParams.query.toLowerCase().trim();
      console.log(`🔍 Filtering by search query: "${searchQuery}"`);
      
      filteredProviders = filteredProviders.filter(provider => {
        const searchableFields = [
          provider.businessName,
          provider.ownerName,
          provider.serviceType,
          provider.description,
          provider.city,
          provider.state,
          provider.zipCode,
          provider.address,
          ...(provider.specialties || []),
          ...(provider.activeServices?.map(s => s.name) || []),
          ...(provider.activeServices?.map(s => s.description) || []),
          ...(provider.activeServices?.map(s => s.category) || [])
        ].filter(Boolean);

        return searchableFields.some(field => 
          field?.toString().toLowerCase().includes(searchQuery)
        );
      });
    }

    // Filter by zipCode (exact match or nearby)
    if (searchParams.zipCode) {
      console.log(`📍 Filtering by zipCode: ${searchParams.zipCode}`);
      filteredProviders = filteredProviders.filter(provider => 
        provider.zipCode === searchParams.zipCode ||
        // You could add nearby zipcode logic here
        provider.zipCode?.startsWith(searchParams.zipCode.substring(0, 3))
      );
    }

    // Filter by city
    if (searchParams.city) {
      console.log(`🏙️ Filtering by city: ${searchParams.city}`);
      filteredProviders = filteredProviders.filter(provider => 
        provider.city?.toLowerCase().includes(searchParams.city!.toLowerCase())
      );
    }

    // Filter by state
    if (searchParams.state) {
      console.log(`🗺️ Filtering by state: ${searchParams.state}`);
      filteredProviders = filteredProviders.filter(provider => 
        provider.state?.toLowerCase().includes(searchParams.state!.toLowerCase())
      );
    }

    // Filter by service type
    if (searchParams.serviceType && searchParams.serviceType !== 'All Services') {
      console.log(`🛠️ Filtering by service type: ${searchParams.serviceType}`);
      filteredProviders = filteredProviders.filter(provider => 
        provider.serviceType?.toLowerCase().includes(searchParams.serviceType!.toLowerCase()) ||
        provider.activeServices?.some(service => 
          service.category?.toLowerCase().includes(searchParams.serviceType!.toLowerCase()) ||
          service.name?.toLowerCase().includes(searchParams.serviceType!.toLowerCase())
        )
      );
    }

    // Sort results (featured first, then by rating)
    filteredProviders.sort((a, b) => {
      // Featured providers first
      if (a.featured && !b.featured) return -1;
      if (!a.featured && b.featured) return 1;
      
      // Then by rating
      return (b.rating || 0) - (a.rating || 0);
    });

    console.log(`✅ Final filtered results: ${filteredProviders.length} providers`);
    
    return filteredProviders;

  } catch (error) {
    console.error('❌ Error searching Firebase providers:', error);
    throw error;
  }
};

/**
 * Get all approved providers with active services (for initial load)
 */
export const getAllActiveProviders = async (): Promise<Provider[]> => {
  return searchFirebaseProviders({});
};

/**
 * Search providers by location (zipcode, city, state)
 */
export const searchProvidersByLocation = async (location: string): Promise<Provider[]> => {
  // Try to determine if it's a zipcode (5 digits) or city/state
  const isZipCode = /^\d{5}(-\d{4})?$/.test(location);
  
  if (isZipCode) {
    return searchFirebaseProviders({ zipCode: location });
  } else {
    // Try as city first, then state
    const cityResults = await searchFirebaseProviders({ city: location });
    if (cityResults.length > 0) {
      return cityResults;
    }
    return searchFirebaseProviders({ state: location });
  }
};

/**
 * Search providers by service type
 */
export const searchProvidersByService = async (serviceType: string): Promise<Provider[]> => {
  return searchFirebaseProviders({ serviceType });
};

/**
 * Advanced search with multiple criteria
 */
export const advancedSearchProviders = async (
  query: string,
  location: string,
  serviceType: string
): Promise<Provider[]> => {
  const isZipCode = /^\d{5}(-\d{4})?$/.test(location);
  
  return searchFirebaseProviders({
    query,
    ...(isZipCode ? { zipCode: location } : { city: location }),
    serviceType: serviceType !== 'All Services' ? serviceType : undefined
  });
};
