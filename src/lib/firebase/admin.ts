import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from './config';
import { toast } from 'react-hot-toast';

// Function to set up admin user
export const setupAdminUser = async (userId: string, email: string) => {
  try {
    console.log('🔧 Setting up admin user:', userId);
    
    const userRef = doc(db, 'users', userId);
    
    // Check if user already exists
    const userDoc = await getDoc(userRef);
    
    const adminData = {
      email: email,
      role: 'admin',
      superAdmin: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      displayName: 'Admin User',
      isActive: true
    };

    if (userDoc.exists()) {
      // Update existing user to admin
      await setDoc(userRef, {
        ...userDoc.data(),
        ...adminData,
        updatedAt: new Date()
      }, { merge: true });
      console.log('✅ Updated existing user to admin');
    } else {
      // Create new admin user
      await setDoc(userRef, adminData);
      console.log('✅ Created new admin user');
    }

    toast.success('Admin user setup complete!');
    return true;
  } catch (error) {
    console.error('❌ Error setting up admin user:', error);
    toast.error('Failed to setup admin user');
    return false;
  }
};

// Function to check if current user is admin
export const checkAdminStatus = async (userId: string) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const isAdmin = userData.role === 'admin';
      console.log('👤 User admin status:', isAdmin);
      return isAdmin;
    }
    
    console.log('❌ User document not found');
    return false;
  } catch (error) {
    console.error('❌ Error checking admin status:', error);
    return false;
  }
};

// Quick admin setup for development
export const quickAdminSetup = async () => {
  try {
    // Use a default admin user ID for development
    const adminUserId = 'admin-dev-user';
    const adminEmail = '<EMAIL>';
    
    await setupAdminUser(adminUserId, adminEmail);
    
    console.log('🎉 Quick admin setup complete!');
    console.log('Admin User ID:', adminUserId);
    console.log('Admin Email:', adminEmail);
    
    return { userId: adminUserId, email: adminEmail };
  } catch (error) {
    console.error('❌ Quick admin setup failed:', error);
    throw error;
  }
};
