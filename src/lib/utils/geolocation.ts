// Geolocation utilities for distance calculations and location-based search

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface LocationData {
  address: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates?: Coordinates;
}

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param coord1 First coordinate
 * @param coord2 Second coordinate
 * @returns Distance in miles
 */
export function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
  const R = 3959; // Earth's radius in miles
  const dLat = toRadians(coord2.lat - coord1.lat);
  const dLng = toRadians(coord2.lng - coord1.lng);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(coord1.lat)) * Math.cos(toRadians(coord2.lat)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return Math.round(distance * 10) / 10; // Round to 1 decimal place
}

/**
 * Convert degrees to radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Geocode an address to get coordinates
 * Uses Google Geocoding API (you'll need to add your API key)
 */
export async function geocodeAddress(address: string): Promise<Coordinates | null> {
  try {
    // For now, return null - you can implement Google Geocoding API later
    // const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`);
    // const data = await response.json();
    // if (data.results && data.results.length > 0) {
    //   const location = data.results[0].geometry.location;
    //   return { lat: location.lat, lng: location.lng };
    // }
    return null;
  } catch (error) {
    console.error('Error geocoding address:', error);
    return null;
  }
}

/**
 * Get approximate coordinates for major cities (fallback when geocoding is not available)
 */
export function getCityCoordinates(city: string, state: string): Coordinates | null {
  const cityKey = `${city.toLowerCase()}, ${state.toLowerCase()}`;
  
  // Major US cities coordinates
  const cityCoordinates: Record<string, Coordinates> = {
    'new york, ny': { lat: 40.7128, lng: -74.0060 },
    'los angeles, ca': { lat: 34.0522, lng: -118.2437 },
    'chicago, il': { lat: 41.8781, lng: -87.6298 },
    'houston, tx': { lat: 29.7604, lng: -95.3698 },
    'phoenix, az': { lat: 33.4484, lng: -112.0740 },
    'philadelphia, pa': { lat: 39.9526, lng: -75.1652 },
    'san antonio, tx': { lat: 29.4241, lng: -98.4936 },
    'san diego, ca': { lat: 32.7157, lng: -117.1611 },
    'dallas, tx': { lat: 32.7767, lng: -96.7970 },
    'san jose, ca': { lat: 37.3382, lng: -121.8863 },
    'austin, tx': { lat: 30.2672, lng: -97.7431 },
    'jacksonville, fl': { lat: 30.3322, lng: -81.6557 },
    'fort worth, tx': { lat: 32.7555, lng: -97.3308 },
    'columbus, oh': { lat: 39.9612, lng: -82.9988 },
    'charlotte, nc': { lat: 35.2271, lng: -80.8431 },
    'san francisco, ca': { lat: 37.7749, lng: -122.4194 },
    'indianapolis, in': { lat: 39.7684, lng: -86.1581 },
    'seattle, wa': { lat: 47.6062, lng: -122.3321 },
    'denver, co': { lat: 39.7392, lng: -104.9903 },
    'washington, dc': { lat: 38.9072, lng: -77.0369 },
    'boston, ma': { lat: 42.3601, lng: -71.0589 },
    'el paso, tx': { lat: 31.7619, lng: -106.4850 },
    'detroit, mi': { lat: 42.3314, lng: -83.0458 },
    'nashville, tn': { lat: 36.1627, lng: -86.7816 },
    'portland, or': { lat: 45.5152, lng: -122.6784 },
    'memphis, tn': { lat: 35.1495, lng: -90.0490 },
    'oklahoma city, ok': { lat: 35.4676, lng: -97.5164 },
    'las vegas, nv': { lat: 36.1699, lng: -115.1398 },
    'louisville, ky': { lat: 38.2527, lng: -85.7585 },
    'baltimore, md': { lat: 39.2904, lng: -76.6122 },
    'milwaukee, wi': { lat: 43.0389, lng: -87.9065 },
    'albuquerque, nm': { lat: 35.0844, lng: -106.6504 },
    'tucson, az': { lat: 32.2226, lng: -110.9747 },
    'fresno, ca': { lat: 36.7378, lng: -119.7871 },
    'mesa, az': { lat: 33.4152, lng: -111.8315 },
    'sacramento, ca': { lat: 38.5816, lng: -121.4944 },
    'atlanta, ga': { lat: 33.7490, lng: -84.3880 },
    'kansas city, mo': { lat: 39.0997, lng: -94.5786 },
    'colorado springs, co': { lat: 38.8339, lng: -104.8214 },
    'miami, fl': { lat: 25.7617, lng: -80.1918 },
    'raleigh, nc': { lat: 35.7796, lng: -78.6382 },
    'omaha, ne': { lat: 41.2565, lng: -95.9345 },
    'long beach, ca': { lat: 33.7701, lng: -118.1937 },
    'virginia beach, va': { lat: 36.8529, lng: -75.9780 },
    'oakland, ca': { lat: 37.8044, lng: -122.2711 },
    'minneapolis, mn': { lat: 44.9778, lng: -93.2650 },
    'tulsa, ok': { lat: 36.1540, lng: -95.9928 },
    'tampa, fl': { lat: 27.9506, lng: -82.4572 },
    'arlington, tx': { lat: 32.7357, lng: -97.1081 },
    'new orleans, la': { lat: 29.9511, lng: -90.0715 }
  };
  
  return cityCoordinates[cityKey] || null;
}

/**
 * Parse location string and extract city, state
 */
export function parseLocation(location: string): { city: string; state: string } | null {
  // Handle formats like "City, State", "City, ST", "City State", etc.
  const patterns = [
    /^(.+),\s*([A-Z]{2})$/i, // "City, ST"
    /^(.+),\s*([A-Za-z\s]+)$/i, // "City, State Name"
    /^(.+)\s+([A-Z]{2})$/i, // "City ST"
  ];
  
  for (const pattern of patterns) {
    const match = location.trim().match(pattern);
    if (match) {
      return {
        city: match[1].trim(),
        state: match[2].trim().toUpperCase()
      };
    }
  }
  
  return null;
}

/**
 * Filter providers by distance from a location
 */
export function filterProvidersByDistance(
  providers: any[],
  searchLocation: Coordinates,
  maxDistanceMiles: number = 20
): any[] {
  return providers
    .map(provider => {
      if (!provider.coordinates) {
        // Try to get coordinates from city/state
        const coords = getCityCoordinates(provider.city, provider.state);
        if (coords) {
          provider.coordinates = coords;
        } else {
          return null; // Skip providers without location data
        }
      }
      
      const distance = calculateDistance(searchLocation, provider.coordinates);
      return {
        ...provider,
        distance
      };
    })
    .filter(provider => provider && provider.distance <= maxDistanceMiles)
    .sort((a, b) => a.distance - b.distance); // Sort by distance
}
