'use client';

import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid';

// Plaid configuration
const configuration = new Configuration({
  basePath: PlaidEnvironments.sandbox, // Use sandbox for development
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': process.env.NEXT_PUBLIC_PLAID_CLIENT_ID,
      'PLAID-SECRET': process.env.PLAID_SECRET,
    },
  },
});

export const plaidClient = new PlaidApi(configuration);

// Plaid Link Token interface
export interface PlaidLinkToken {
  link_token: string;
  expiration: string;
  request_id: string;
}

// Bank Account interface
export interface BankAccount {
  id: string;
  name: string;
  mask: string;
  type: string;
  subtype: string;
  institution_name: string;
  account_id: string;
  access_token: string;
}

// Create link token for Plaid Link
export const createLinkToken = async (userId: string): Promise<PlaidLinkToken> => {
  try {
    const response = await fetch('/api/plaid/create-link-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    if (!response.ok) {
      throw new Error('Failed to create link token');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating link token:', error);
    throw error;
  }
};

// Exchange public token for access token
export const exchangePublicToken = async (publicToken: string, userId: string): Promise<BankAccount[]> => {
  try {
    const response = await fetch('/api/plaid/exchange-public-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ publicToken, userId }),
    });

    if (!response.ok) {
      throw new Error('Failed to exchange public token');
    }

    return await response.json();
  } catch (error) {
    console.error('Error exchanging public token:', error);
    throw error;
  }
};

// Get bank accounts for a user
export const getBankAccounts = async (userId: string): Promise<BankAccount[]> => {
  try {
    const response = await fetch(`/api/plaid/accounts/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to get bank accounts');
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting bank accounts:', error);
    throw error;
  }
};

// Initiate a payout to bank account
export const initiatePayout = async (
  userId: string, 
  accountId: string, 
  amount: number, 
  description: string
): Promise<{ success: boolean; payoutId?: string; error?: string }> => {
  try {
    const response = await fetch('/api/plaid/initiate-payout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, accountId, amount, description }),
    });

    if (!response.ok) {
      throw new Error('Failed to initiate payout');
    }

    return await response.json();
  } catch (error) {
    console.error('Error initiating payout:', error);
    throw error;
  }
};

// Get account balance
export const getAccountBalance = async (userId: string, accountId: string): Promise<number> => {
  try {
    const response = await fetch(`/api/plaid/balance/${userId}/${accountId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to get account balance');
    }

    const data = await response.json();
    return data.balance;
  } catch (error) {
    console.error('Error getting account balance:', error);
    throw error;
  }
};

// Verify account for ACH transfers
export const verifyAccount = async (userId: string, accountId: string): Promise<boolean> => {
  try {
    const response = await fetch('/api/plaid/verify-account', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, accountId }),
    });

    if (!response.ok) {
      throw new Error('Failed to verify account');
    }

    const data = await response.json();
    return data.verified;
  } catch (error) {
    console.error('Error verifying account:', error);
    throw error;
  }
};
