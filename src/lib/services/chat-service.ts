import { query } from '../database';

export interface ChatRoom {
  id: string;
  type: 'direct' | 'group' | 'support';
  name?: string;
  description?: string;
  participants: string[];
  isActive: boolean;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Message {
  id: string;
  chatRoomId: string;
  senderId: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  isEdited: boolean;
  editedAt?: string;
  isDeleted: boolean;
  deletedAt?: string;
  readBy: string[];
  createdAt: string;
  updatedAt?: string;
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

export class ChatService {
  // Get user's chat rooms
  static async getUserChatRooms(userId: string): Promise<ChatRoom[]> {
    try {
      const result = await query(`
        SELECT 
          id, type, name, description, participants, is_active,
          last_message_at, created_at, updated_at
        FROM chat_rooms 
        WHERE $1 = ANY(participants) AND is_active = true
        ORDER BY last_message_at DESC NULLS LAST, created_at DESC
      `, [userId]);

      return result.rows.map(this.mapDatabaseChatRoomToChatRoom);
    } catch (error) {
      console.error('Error getting user chat rooms:', error);
      throw new Error('Failed to get chat rooms');
    }
  }

  // Get chat room by ID
  static async getChatRoomById(roomId: string, userId?: string): Promise<ChatRoom | null> {
    try {
      let whereClause = 'WHERE id = $1';
      const params = [roomId];

      if (userId) {
        whereClause += ' AND $2 = ANY(participants)';
        params.push(userId);
      }

      const result = await query(`
        SELECT 
          id, type, name, description, participants, is_active,
          last_message_at, created_at, updated_at
        FROM chat_rooms 
        ${whereClause}
      `, params);

      if (result.rows.length === 0) {
        return null;
      }

      return this.mapDatabaseChatRoomToChatRoom(result.rows[0]);
    } catch (error) {
      console.error('Error getting chat room by ID:', error);
      throw new Error('Failed to get chat room');
    }
  }

  // Create new chat room
  static async createChatRoom(data: {
    type: 'direct' | 'group' | 'support';
    participants: string[];
    name?: string;
    description?: string;
  }): Promise<ChatRoom> {
    try {
      // For direct chats, check if room already exists
      if (data.type === 'direct' && data.participants.length === 2) {
        const existingRoom = await query(`
          SELECT id FROM chat_rooms 
          WHERE type = 'direct' 
            AND participants @> $1 
            AND participants <@ $1
            AND array_length(participants, 1) = 2
        `, [data.participants]);

        if (existingRoom.rows.length > 0) {
          const room = await this.getChatRoomById(existingRoom.rows[0].id);
          if (room) return room;
        }
      }

      const result = await query(`
        INSERT INTO chat_rooms (type, participants, name, description)
        VALUES ($1, $2, $3, $4)
        RETURNING 
          id, type, name, description, participants, is_active,
          last_message_at, created_at, updated_at
      `, [data.type, data.participants, data.name, data.description]);

      return this.mapDatabaseChatRoomToChatRoom(result.rows[0]);
    } catch (error) {
      console.error('Error creating chat room:', error);
      throw new Error('Failed to create chat room');
    }
  }

  // Get messages for a chat room
  static async getRoomMessages(
    roomId: string, 
    userId: string, 
    limit: number = 50, 
    offset: number = 0
  ): Promise<Message[]> {
    try {
      // Verify user has access to this room
      const hasAccess = await this.verifyRoomAccess(userId, roomId);
      if (!hasAccess) {
        throw new Error('Access denied to this room');
      }

      const result = await query(`
        SELECT 
          m.id, m.chat_room_id, m.sender_id, m.content, m.type,
          m.file_url, m.file_name, m.file_size, m.is_edited, m.edited_at,
          m.is_deleted, m.deleted_at, m.read_by, m.created_at, m.updated_at,
          u.name as sender_name, u.avatar as sender_avatar
        FROM messages m
        JOIN users u ON m.sender_id = u.id
        WHERE m.chat_room_id = $1 AND m.is_deleted = false
        ORDER BY m.created_at DESC
        LIMIT $2 OFFSET $3
      `, [roomId, limit, offset]);

      return result.rows.reverse().map(this.mapDatabaseMessageToMessage);
    } catch (error) {
      console.error('Error getting room messages:', error);
      throw new Error('Failed to get messages');
    }
  }

  // Send message
  static async sendMessage(data: {
    chatRoomId: string;
    senderId: string;
    content: string;
    type?: 'text' | 'image' | 'file' | 'system';
    fileUrl?: string;
    fileName?: string;
    fileSize?: number;
  }): Promise<Message> {
    try {
      // Verify user has access to this room
      const hasAccess = await this.verifyRoomAccess(data.senderId, data.chatRoomId);
      if (!hasAccess) {
        throw new Error('Access denied to this room');
      }

      const result = await query(`
        INSERT INTO messages (
          chat_room_id, sender_id, content, type, file_url, file_name, file_size
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING 
          id, chat_room_id, sender_id, content, type, file_url, file_name, file_size,
          is_edited, edited_at, is_deleted, deleted_at, read_by, created_at, updated_at
      `, [
        data.chatRoomId,
        data.senderId,
        data.content,
        data.type || 'text',
        data.fileUrl,
        data.fileName,
        data.fileSize
      ]);

      // Update room's last message timestamp
      await query(
        'UPDATE chat_rooms SET last_message_at = CURRENT_TIMESTAMP WHERE id = $1',
        [data.chatRoomId]
      );

      // Get sender info
      const senderResult = await query(
        'SELECT name, avatar FROM users WHERE id = $1',
        [data.senderId]
      );

      const message = this.mapDatabaseMessageToMessage(result.rows[0]);
      if (senderResult.rows.length > 0) {
        message.sender = {
          id: data.senderId,
          name: senderResult.rows[0].name,
          avatar: senderResult.rows[0].avatar
        };
      }

      return message;
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message');
    }
  }

  // Mark messages as read
  static async markMessagesAsRead(userId: string, messageIds: string[]): Promise<void> {
    try {
      await query(`
        UPDATE messages 
        SET read_by = array_append(read_by, $1)
        WHERE id = ANY($2) AND NOT ($1 = ANY(read_by))
      `, [userId, messageIds]);
    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw new Error('Failed to mark messages as read');
    }
  }

  // Edit message
  static async editMessage(messageId: string, userId: string, newContent: string): Promise<Message> {
    try {
      const result = await query(`
        UPDATE messages 
        SET 
          content = $3,
          is_edited = true,
          edited_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND sender_id = $2 AND is_deleted = false
        RETURNING 
          id, chat_room_id, sender_id, content, type, file_url, file_name, file_size,
          is_edited, edited_at, is_deleted, deleted_at, read_by, created_at, updated_at
      `, [messageId, userId, newContent]);

      if (result.rows.length === 0) {
        throw new Error('Message not found or access denied');
      }

      return this.mapDatabaseMessageToMessage(result.rows[0]);
    } catch (error) {
      console.error('Error editing message:', error);
      throw new Error('Failed to edit message');
    }
  }

  // Delete message
  static async deleteMessage(messageId: string, userId: string): Promise<void> {
    try {
      const result = await query(`
        UPDATE messages 
        SET 
          is_deleted = true,
          deleted_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND sender_id = $2
      `, [messageId, userId]);

      if (result.rowCount === 0) {
        throw new Error('Message not found or access denied');
      }
    } catch (error) {
      console.error('Error deleting message:', error);
      throw new Error('Failed to delete message');
    }
  }

  // Get unread message count for user
  static async getUnreadMessageCount(userId: string): Promise<number> {
    try {
      const result = await query(`
        SELECT COUNT(*) as unread_count
        FROM messages m
        JOIN chat_rooms cr ON m.chat_room_id = cr.id
        WHERE $1 = ANY(cr.participants) 
          AND m.sender_id != $1
          AND NOT ($1 = ANY(m.read_by))
          AND m.is_deleted = false
      `, [userId]);

      return parseInt(result.rows[0].unread_count) || 0;
    } catch (error) {
      console.error('Error getting unread message count:', error);
      return 0;
    }
  }

  // Verify user has access to room
  private static async verifyRoomAccess(userId: string, roomId: string): Promise<boolean> {
    try {
      const result = await query(
        'SELECT participants FROM chat_rooms WHERE id = $1',
        [roomId]
      );

      if (result.rows.length === 0) {
        return false;
      }

      const participants = result.rows[0].participants;
      return participants.includes(userId);
    } catch (error) {
      console.error('Error verifying room access:', error);
      return false;
    }
  }

  // Helper methods
  private static mapDatabaseChatRoomToChatRoom(dbRoom: any): ChatRoom {
    return {
      id: dbRoom.id,
      type: dbRoom.type,
      name: dbRoom.name,
      description: dbRoom.description,
      participants: dbRoom.participants,
      isActive: dbRoom.is_active,
      lastMessageAt: dbRoom.last_message_at?.toISOString(),
      createdAt: dbRoom.created_at.toISOString(),
      updatedAt: dbRoom.updated_at?.toISOString()
    };
  }

  private static mapDatabaseMessageToMessage(dbMessage: any): Message {
    return {
      id: dbMessage.id,
      chatRoomId: dbMessage.chat_room_id,
      senderId: dbMessage.sender_id,
      content: dbMessage.content,
      type: dbMessage.type,
      fileUrl: dbMessage.file_url,
      fileName: dbMessage.file_name,
      fileSize: dbMessage.file_size,
      isEdited: dbMessage.is_edited,
      editedAt: dbMessage.edited_at?.toISOString(),
      isDeleted: dbMessage.is_deleted,
      deletedAt: dbMessage.deleted_at?.toISOString(),
      readBy: dbMessage.read_by || [],
      createdAt: dbMessage.created_at.toISOString(),
      updatedAt: dbMessage.updated_at?.toISOString(),
      sender: dbMessage.sender_name ? {
        id: dbMessage.sender_id,
        name: dbMessage.sender_name,
        avatar: dbMessage.sender_avatar
      } : undefined
    };
  }
}
