import { DatabaseService, COLLECTIONS, timestampToDate, dateToTimestamp } from '../database';
import { Pet, Vaccination, Medication, VetInfo } from '@/types/user';

export class PetService {
  // Get all pets for a user
  static async getUserPets(userId: string): Promise<Pet[]> {
    try {
      const pets = await DatabaseService.query(
        COLLECTIONS.PETS,
        [{ field: 'userId', operator: '==', value: userId }],
        'createdAt',
        'asc'
      );

      return pets.map(this.mapFirebasePetToPet);
    } catch (error) {
      console.error('Error getting user pets:', error);
      throw new Error('Failed to get pets');
    }
  }

  // Get pet by ID
  static async getPetById(petId: string, userId?: string): Promise<Pet | null> {
    try {
      const pet = await DatabaseService.getById(COLLECTIONS.PETS, petId);

      if (!pet) {
        return null;
      }

      // Check if user owns this pet (if userId provided)
      if (userId && pet.userId !== userId) {
        return null;
      }

      return this.mapFirebasePetToPet(pet);
    } catch (error) {
      console.error('Error getting pet by ID:', error);
      throw new Error('Failed to get pet');
    }
  }

  // Create new pet
  static async createPet(userId: string, petData: Omit<Pet, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<Pet> {
    try {
      const newPetData = {
        userId,
        name: petData.name,
        type: petData.type,
        breed: petData.breed,
        dateOfBirth: petData.dateOfBirth ? dateToTimestamp(new Date(petData.dateOfBirth)) : null,
        weight: petData.weight,
        color: petData.color,
        gender: petData.gender,
        microchipId: petData.microchipId,
        photo: petData.photo,
        vaccinations: petData.vaccinations || [],
        allergies: petData.allergies || [],
        medications: petData.medications || [],
        medicalNotes: petData.medicalNotes,
        vetInfo: petData.vetInfo
      };

      const petId = await DatabaseService.create(COLLECTIONS.PETS, newPetData);
      const createdPet = await DatabaseService.getById(COLLECTIONS.PETS, petId);

      return this.mapFirebasePetToPet(createdPet);
    } catch (error) {
      console.error('Error creating pet:', error);
      throw new Error('Failed to create pet');
    }
  }

  // Update pet
  static async updatePet(petId: string, userId: string, updates: Partial<Pet>): Promise<Pet> {
    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      // Build dynamic update query
      if (updates.name !== undefined) {
        setClause.push(`name = $${paramIndex++}`);
        values.push(updates.name);
      }
      if (updates.type !== undefined) {
        setClause.push(`type = $${paramIndex++}`);
        values.push(updates.type);
      }
      if (updates.breed !== undefined) {
        setClause.push(`breed = $${paramIndex++}`);
        values.push(updates.breed);
      }
      if (updates.dateOfBirth !== undefined) {
        setClause.push(`date_of_birth = $${paramIndex++}`);
        values.push(updates.dateOfBirth);
      }
      if (updates.weight !== undefined) {
        setClause.push(`weight = $${paramIndex++}`);
        values.push(updates.weight);
      }
      if (updates.color !== undefined) {
        setClause.push(`color = $${paramIndex++}`);
        values.push(updates.color);
      }
      if (updates.gender !== undefined) {
        setClause.push(`gender = $${paramIndex++}`);
        values.push(updates.gender);
      }
      if (updates.microchipId !== undefined) {
        setClause.push(`microchip_id = $${paramIndex++}`);
        values.push(updates.microchipId);
      }
      if (updates.photo !== undefined) {
        setClause.push(`photo = $${paramIndex++}`);
        values.push(updates.photo);
      }
      if (updates.vaccinations !== undefined) {
        setClause.push(`vaccinations = $${paramIndex++}`);
        values.push(JSON.stringify(updates.vaccinations));
      }
      if (updates.allergies !== undefined) {
        setClause.push(`allergies = $${paramIndex++}`);
        values.push(updates.allergies);
      }
      if (updates.medications !== undefined) {
        setClause.push(`medications = $${paramIndex++}`);
        values.push(JSON.stringify(updates.medications));
      }
      if (updates.medicalNotes !== undefined) {
        setClause.push(`medical_notes = $${paramIndex++}`);
        values.push(updates.medicalNotes);
      }
      if (updates.vetInfo !== undefined) {
        setClause.push(`vet_info = $${paramIndex++}`);
        values.push(JSON.stringify(updates.vetInfo));
      }

      if (setClause.length === 0) {
        throw new Error('No valid fields to update');
      }

      // Add updated_at timestamp
      setClause.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(petId, userId);

      const result = await query(`
        UPDATE pets 
        SET ${setClause.join(', ')}
        WHERE id = $${paramIndex++} AND user_id = $${paramIndex}
        RETURNING 
          id, user_id, name, type, breed, date_of_birth, weight, color, gender,
          microchip_id, photo, vaccinations, allergies, medications, medical_notes,
          vet_info, created_at, updated_at
      `, values);

      if (result.rows.length === 0) {
        throw new Error('Pet not found or access denied');
      }

      return this.mapDatabasePetToPet(result.rows[0]);
    } catch (error) {
      console.error('Error updating pet:', error);
      throw new Error('Failed to update pet');
    }
  }

  // Delete pet
  static async deletePet(petId: string, userId: string): Promise<void> {
    try {
      const result = await query(`
        DELETE FROM pets 
        WHERE id = $1 AND user_id = $2
      `, [petId, userId]);

      if (result.rowCount === 0) {
        throw new Error('Pet not found or access denied');
      }
    } catch (error) {
      console.error('Error deleting pet:', error);
      throw new Error('Failed to delete pet');
    }
  }

  // Add vaccination record
  static async addVaccination(petId: string, userId: string, vaccination: Vaccination): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedVaccinations = [...(pet.vaccinations || []), vaccination];

      return await this.updatePet(petId, userId, {
        vaccinations: updatedVaccinations
      });
    } catch (error) {
      console.error('Error adding vaccination:', error);
      throw new Error('Failed to add vaccination');
    }
  }

  // Update vaccination record
  static async updateVaccination(petId: string, userId: string, vaccinationIndex: number, vaccination: Vaccination): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedVaccinations = [...(pet.vaccinations || [])];
      if (vaccinationIndex >= 0 && vaccinationIndex < updatedVaccinations.length) {
        updatedVaccinations[vaccinationIndex] = vaccination;
      } else {
        throw new Error('Invalid vaccination index');
      }

      return await this.updatePet(petId, userId, {
        vaccinations: updatedVaccinations
      });
    } catch (error) {
      console.error('Error updating vaccination:', error);
      throw new Error('Failed to update vaccination');
    }
  }

  // Remove vaccination record
  static async removeVaccination(petId: string, userId: string, vaccinationIndex: number): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedVaccinations = [...(pet.vaccinations || [])];
      if (vaccinationIndex >= 0 && vaccinationIndex < updatedVaccinations.length) {
        updatedVaccinations.splice(vaccinationIndex, 1);
      } else {
        throw new Error('Invalid vaccination index');
      }

      return await this.updatePet(petId, userId, {
        vaccinations: updatedVaccinations
      });
    } catch (error) {
      console.error('Error removing vaccination:', error);
      throw new Error('Failed to remove vaccination');
    }
  }

  // Add medication
  static async addMedication(petId: string, userId: string, medication: Medication): Promise<Pet> {
    try {
      const pet = await this.getPetById(petId, userId);
      if (!pet) {
        throw new Error('Pet not found');
      }

      const updatedMedications = [...(pet.medications || []), medication];

      return await this.updatePet(petId, userId, {
        medications: updatedMedications
      });
    } catch (error) {
      console.error('Error adding medication:', error);
      throw new Error('Failed to add medication');
    }
  }

  // Helper method to map Firebase pet to Pet interface
  private static mapFirebasePetToPet(fbPet: any): Pet {
    return {
      id: fbPet.id,
      userId: fbPet.userId,
      name: fbPet.name,
      type: fbPet.type,
      breed: fbPet.breed,
      dateOfBirth: fbPet.dateOfBirth ? timestampToDate(fbPet.dateOfBirth).toISOString().split('T')[0] : undefined,
      weight: fbPet.weight,
      color: fbPet.color,
      gender: fbPet.gender,
      microchipId: fbPet.microchipId,
      photo: fbPet.photo,
      vaccinations: fbPet.vaccinations || [],
      allergies: fbPet.allergies || [],
      medications: fbPet.medications || [],
      medicalNotes: fbPet.medicalNotes,
      vetInfo: fbPet.vetInfo,
      createdAt: fbPet.createdAt ? timestampToDate(fbPet.createdAt).toISOString() : new Date().toISOString(),
      updatedAt: fbPet.updatedAt ? timestampToDate(fbPet.updatedAt).toISOString() : new Date().toISOString()
    };
  }

  // Legacy method for backward compatibility
  private static mapDatabasePetToPet(dbPet: any): Pet {
    console.warn('Legacy PostgreSQL mapping function called. Please use mapFirebasePetToPet instead.');
    return this.mapFirebasePetToPet(dbPet);
  }
}
