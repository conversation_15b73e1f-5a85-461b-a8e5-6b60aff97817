import { query } from '../database';

export interface AnalyticsMetric {
  id: number;
  metric_name: string;
  metric_value: number;
  last_updated: Date;
}

export interface DashboardStats {
  totalUsers: number;
  totalPets: number;
  totalBookings: number;
  totalRevenue: number;
  totalRewardPointsEarned: number;
  totalRewardPointsRedeemed: number;
  activeChatRooms: number;
  totalMessagesSent: number;
  newUsersThisMonth: number;
  bookingsThisMonth: number;
  revenueThisMonth: number;
}

export interface UserAnalytics {
  totalBookings: number;
  totalSpent: number;
  rewardPointsEarned: number;
  rewardPointsRedeemed: number;
  rewardPointsBalance: number;
  joinDate: Date;
  lastActivity: Date;
  favoriteServices: string[];
  averageRating: number;
}

export class AnalyticsService {
  /**
   * Get real-time dashboard statistics from database
   */
  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Get current counts from database (real data only)
      const [
        userCount,
        petCount,
        bookingCount,
        revenueData,
        rewardData,
        chatData,
        messageCount,
        monthlyData
      ] = await Promise.all([
        // Total users (excluding admin)
        query('SELECT COUNT(*) as count FROM users WHERE role != $1', ['admin']),
        
        // Total pets
        query('SELECT COUNT(*) as count FROM pets'),
        
        // Total bookings
        query('SELECT COUNT(*) as count FROM bookings'),
        
        // Total revenue
        query(`
          SELECT 
            COALESCE(SUM(amount), 0) as total_revenue
          FROM transactions 
          WHERE transaction_type = 'payment' AND status = 'completed'
        `),
        
        // Reward points data
        query(`
          SELECT 
            COALESCE(SUM(CASE WHEN transaction_type = 'earned' THEN points ELSE 0 END), 0) as earned,
            COALESCE(SUM(CASE WHEN transaction_type = 'redeemed' THEN points ELSE 0 END), 0) as redeemed
          FROM reward_transactions
        `),
        
        // Active chat rooms
        query(`
          SELECT COUNT(*) as count 
          FROM chat_rooms 
          WHERE last_message_at > NOW() - INTERVAL '30 days'
        `),
        
        // Total messages
        query('SELECT COUNT(*) as count FROM messages'),
        
        // Monthly data
        query(`
          SELECT 
            COUNT(DISTINCT CASE WHEN u.created_at >= DATE_TRUNC('month', CURRENT_DATE) THEN u.id END) as new_users,
            COUNT(DISTINCT CASE WHEN b.created_at >= DATE_TRUNC('month', CURRENT_DATE) THEN b.id END) as new_bookings,
            COALESCE(SUM(CASE WHEN t.created_at >= DATE_TRUNC('month', CURRENT_DATE) AND t.transaction_type = 'payment' AND t.status = 'completed' THEN t.amount ELSE 0 END), 0) as monthly_revenue
          FROM users u
          FULL OUTER JOIN bookings b ON true
          FULL OUTER JOIN transactions t ON true
          WHERE u.role != 'admin' OR u.id IS NULL
        `)
      ]);

      return {
        totalUsers: parseInt(userCount.rows[0].count),
        totalPets: parseInt(petCount.rows[0].count),
        totalBookings: parseInt(bookingCount.rows[0].count),
        totalRevenue: parseFloat(revenueData.rows[0].total_revenue || '0'),
        totalRewardPointsEarned: parseInt(rewardData.rows[0].earned || '0'),
        totalRewardPointsRedeemed: parseInt(rewardData.rows[0].redeemed || '0'),
        activeChatRooms: parseInt(chatData.rows[0].count),
        totalMessagesSent: parseInt(messageCount.rows[0].count),
        newUsersThisMonth: parseInt(monthlyData.rows[0].new_users || '0'),
        bookingsThisMonth: parseInt(monthlyData.rows[0].new_bookings || '0'),
        revenueThisMonth: parseFloat(monthlyData.rows[0].monthly_revenue || '0')
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Return zero values if database query fails
      return {
        totalUsers: 0,
        totalPets: 0,
        totalBookings: 0,
        totalRevenue: 0,
        totalRewardPointsEarned: 0,
        totalRewardPointsRedeemed: 0,
        activeChatRooms: 0,
        totalMessagesSent: 0,
        newUsersThisMonth: 0,
        bookingsThisMonth: 0,
        revenueThisMonth: 0
      };
    }
  }

  /**
   * Get user-specific analytics
   */
  static async getUserAnalytics(userId: string): Promise<UserAnalytics> {
    try {
      const [userStats, bookingStats, rewardStats, serviceStats] = await Promise.all([
        // Basic user stats
        query(`
          SELECT 
            created_at,
            updated_at as last_activity,
            total_bookings,
            total_spent
          FROM users 
          WHERE id = $1
        `, [userId]),
        
        // Booking statistics
        query(`
          SELECT 
            COUNT(*) as total_bookings,
            COALESCE(SUM(total_amount), 0) as total_spent,
            COALESCE(AVG(rating), 0) as average_rating
          FROM bookings 
          WHERE user_id = $1 AND status = 'completed'
        `, [userId]),
        
        // Reward points
        query(`
          SELECT 
            COALESCE(SUM(CASE WHEN transaction_type = 'earned' THEN points ELSE 0 END), 0) as earned,
            COALESCE(SUM(CASE WHEN transaction_type = 'redeemed' THEN points ELSE 0 END), 0) as redeemed
          FROM reward_transactions 
          WHERE user_id = $1
        `, [userId]),
        
        // Favorite services
        query(`
          SELECT 
            s.name,
            COUNT(*) as booking_count
          FROM bookings b
          JOIN services s ON b.service_id = s.id
          WHERE b.user_id = $1
          GROUP BY s.id, s.name
          ORDER BY booking_count DESC
          LIMIT 5
        `, [userId])
      ]);

      const user = userStats.rows[0];
      const bookings = bookingStats.rows[0];
      const rewards = rewardStats.rows[0];
      const services = serviceStats.rows;

      return {
        totalBookings: parseInt(bookings.total_bookings || '0'),
        totalSpent: parseFloat(bookings.total_spent || '0'),
        rewardPointsEarned: parseInt(rewards.earned || '0'),
        rewardPointsRedeemed: parseInt(rewards.redeemed || '0'),
        rewardPointsBalance: parseInt(rewards.earned || '0') - parseInt(rewards.redeemed || '0'),
        joinDate: user ? new Date(user.created_at) : new Date(),
        lastActivity: user ? new Date(user.last_activity) : new Date(),
        favoriteServices: services.map(s => s.name),
        averageRating: parseFloat(bookings.average_rating || '0')
      };
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      return {
        totalBookings: 0,
        totalSpent: 0,
        rewardPointsEarned: 0,
        rewardPointsRedeemed: 0,
        rewardPointsBalance: 0,
        joinDate: new Date(),
        lastActivity: new Date(),
        favoriteServices: [],
        averageRating: 0
      };
    }
  }

  /**
   * Update analytics metrics (called after significant events)
   */
  static async updateMetrics(): Promise<void> {
    try {
      const stats = await this.getDashboardStats();
      
      // Update analytics summary table
      const metrics = [
        ['total_users', stats.totalUsers],
        ['total_pets', stats.totalPets],
        ['total_bookings', stats.totalBookings],
        ['total_revenue', stats.totalRevenue],
        ['total_reward_points_earned', stats.totalRewardPointsEarned],
        ['total_reward_points_redeemed', stats.totalRewardPointsRedeemed],
        ['active_chat_rooms', stats.activeChatRooms],
        ['total_messages_sent', stats.totalMessagesSent]
      ];

      for (const [metric_name, metric_value] of metrics) {
        await query(`
          INSERT INTO analytics_summary (metric_name, metric_value, last_updated)
          VALUES ($1, $2, CURRENT_TIMESTAMP)
          ON CONFLICT (metric_name)
          DO UPDATE SET 
            metric_value = EXCLUDED.metric_value,
            last_updated = CURRENT_TIMESTAMP
        `, [metric_name, metric_value]);
      }
    } catch (error) {
      console.error('Error updating analytics metrics:', error);
    }
  }

  /**
   * Get growth trends over time
   */
  static async getGrowthTrends(days: number = 30): Promise<any[]> {
    try {
      const result = await query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as new_users
        FROM users 
        WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
          AND role != 'admin'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `);

      return result.rows;
    } catch (error) {
      console.error('Error fetching growth trends:', error);
      return [];
    }
  }

  /**
   * Reset all analytics to zero (for fresh start)
   */
  static async resetAnalytics(): Promise<void> {
    try {
      await query(`
        UPDATE analytics_summary 
        SET metric_value = 0, last_updated = CURRENT_TIMESTAMP
      `);
      
      console.log('✅ Analytics reset to zero');
    } catch (error) {
      console.error('Error resetting analytics:', error);
    }
  }
}
