import { query, transaction } from '../database';
import { User, NotificationPreferences } from '@/types/user';

export class UserService {
  // Get user by ID
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const result = await query(`
        SELECT 
          id, email, name, role, avatar, phone, address, location,
          verified, email_verified, phone_verified, fetchly_balance,
          membership_status, membership_start_date, membership_renewal_date,
          reward_points, notification_preferences, saved_providers,
          emergency_contact, last_login_at, created_at, updated_at
        FROM users 
        WHERE id = $1
      `, [userId]);

      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      return this.mapDatabaseUserToUser(user);
    } catch (error) {
      console.error('Error getting user by ID:', error);
      throw new Error('Failed to get user');
    }
  }

  // Get user by email
  static async getUserByEmail(email: string): Promise<User | null> {
    try {
      const result = await query(`
        SELECT 
          id, email, name, role, avatar, phone, address, location,
          verified, email_verified, phone_verified, fetchly_balance,
          membership_status, membership_start_date, membership_renewal_date,
          reward_points, notification_preferences, saved_providers,
          emergency_contact, last_login_at, created_at, updated_at
        FROM users 
        WHERE email = $1
      `, [email.toLowerCase()]);

      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      return this.mapDatabaseUserToUser(user);
    } catch (error) {
      console.error('Error getting user by email:', error);
      throw new Error('Failed to get user');
    }
  }

  // Update user profile
  static async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      // Build dynamic update query
      if (updates.name !== undefined) {
        setClause.push(`name = $${paramIndex++}`);
        values.push(updates.name);
      }
      if (updates.phone !== undefined) {
        setClause.push(`phone = $${paramIndex++}`);
        values.push(updates.phone);
      }
      if (updates.address !== undefined) {
        setClause.push(`address = $${paramIndex++}`);
        values.push(updates.address);
      }
      if (updates.location !== undefined) {
        setClause.push(`location = $${paramIndex++}`);
        values.push(updates.location);
      }
      if (updates.avatar !== undefined) {
        setClause.push(`avatar = $${paramIndex++}`);
        values.push(updates.avatar);
      }
      if (updates.notificationPreferences !== undefined) {
        setClause.push(`notification_preferences = $${paramIndex++}`);
        values.push(JSON.stringify(updates.notificationPreferences));
      }
      if (updates.emergencyContact !== undefined) {
        setClause.push(`emergency_contact = $${paramIndex++}`);
        values.push(JSON.stringify(updates.emergencyContact));
      }

      if (setClause.length === 0) {
        throw new Error('No valid fields to update');
      }

      // Add updated_at timestamp
      setClause.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(userId);

      const result = await query(`
        UPDATE users 
        SET ${setClause.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING 
          id, email, name, role, avatar, phone, address, location,
          verified, email_verified, phone_verified, fetchly_balance,
          membership_status, membership_start_date, membership_renewal_date,
          reward_points, notification_preferences, saved_providers,
          emergency_contact, last_login_at, created_at, updated_at
      `, values);

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      return this.mapDatabaseUserToUser(result.rows[0]);
    } catch (error) {
      console.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }
  }

  // Update Fetchly Balance
  static async updateBalance(userId: string, amount: number, description: string): Promise<User> {
    try {
      const queries = [
        {
          text: `
            UPDATE users 
            SET fetchly_balance = fetchly_balance + $1
            WHERE id = $2
            RETURNING fetchly_balance
          `,
          params: [amount, userId]
        },
        {
          text: `
            INSERT INTO transactions (
              user_id, type, amount, description, balance_before, balance_after, status
            ) VALUES (
              $1, $2, $3, $4, 
              (SELECT fetchly_balance - $3 FROM users WHERE id = $1),
              (SELECT fetchly_balance FROM users WHERE id = $1),
              'completed'
            )
          `,
          params: [userId, amount > 0 ? 'credit' : 'debit', Math.abs(amount), description]
        }
      ];

      await transaction(queries);

      // Return updated user
      return await this.getUserById(userId) as User;
    } catch (error) {
      console.error('Error updating balance:', error);
      throw new Error('Failed to update balance');
    }
  }

  // Update reward points
  static async updateRewardPoints(userId: string, points: number, description: string): Promise<User> {
    try {
      const queries = [
        {
          text: `
            UPDATE users 
            SET reward_points = reward_points + $1
            WHERE id = $2
          `,
          params: [points, userId]
        },
        {
          text: `
            INSERT INTO reward_transactions (
              user_id, type, points, description
            ) VALUES ($1, $2, $3, $4)
          `,
          params: [userId, points > 0 ? 'earned' : 'redeemed', Math.abs(points), description]
        }
      ];

      await transaction(queries);

      // Return updated user
      return await this.getUserById(userId) as User;
    } catch (error) {
      console.error('Error updating reward points:', error);
      throw new Error('Failed to update reward points');
    }
  }

  // Add saved provider
  static async addSavedProvider(userId: string, providerId: string): Promise<void> {
    try {
      await query(`
        UPDATE users 
        SET saved_providers = array_append(saved_providers, $1)
        WHERE id = $2 AND NOT ($1 = ANY(saved_providers))
      `, [providerId, userId]);
    } catch (error) {
      console.error('Error adding saved provider:', error);
      throw new Error('Failed to add saved provider');
    }
  }

  // Remove saved provider
  static async removeSavedProvider(userId: string, providerId: string): Promise<void> {
    try {
      await query(`
        UPDATE users 
        SET saved_providers = array_remove(saved_providers, $1)
        WHERE id = $2
      `, [providerId, userId]);
    } catch (error) {
      console.error('Error removing saved provider:', error);
      throw new Error('Failed to remove saved provider');
    }
  }

  // Get user's transaction history
  static async getTransactionHistory(userId: string, limit: number = 50, offset: number = 0): Promise<any[]> {
    try {
      const result = await query(`
        SELECT 
          id, type, amount, description, payment_method,
          balance_before, balance_after, status, created_at
        FROM transactions 
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `, [userId, limit, offset]);

      return result.rows;
    } catch (error) {
      console.error('Error getting transaction history:', error);
      throw new Error('Failed to get transaction history');
    }
  }

  // Get user's reward transaction history
  static async getRewardHistory(userId: string, limit: number = 50, offset: number = 0): Promise<any[]> {
    try {
      const result = await query(`
        SELECT 
          id, type, points, description, reward_item_name, created_at
        FROM reward_transactions 
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `, [userId, limit, offset]);

      return result.rows;
    } catch (error) {
      console.error('Error getting reward history:', error);
      throw new Error('Failed to get reward history');
    }
  }

  // Helper method to map database user to User interface
  private static mapDatabaseUserToUser(dbUser: any): User {
    return {
      id: dbUser.id,
      email: dbUser.email,
      name: dbUser.name,
      role: dbUser.role,
      avatar: dbUser.avatar,
      phone: dbUser.phone,
      address: dbUser.address,
      location: dbUser.location,
      verified: dbUser.verified,
      emailVerified: dbUser.email_verified,
      phoneVerified: dbUser.phone_verified,
      fetchlyBalance: parseFloat(dbUser.fetchly_balance) || 0,
      membershipStatus: dbUser.membership_status,
      membershipStartDate: dbUser.membership_start_date?.toISOString(),
      membershipRenewalDate: dbUser.membership_renewal_date?.toISOString(),
      rewardPoints: dbUser.reward_points || 0,
      notificationPreferences: dbUser.notification_preferences || {
        email: true,
        sms: false,
        push: true,
        marketing: false,
        bookingReminders: true,
        promotions: false
      },
      savedProviders: dbUser.saved_providers || [],
      emergencyContact: dbUser.emergency_contact,
      lastLoginAt: dbUser.last_login_at?.toISOString(),
      joinedDate: dbUser.created_at.toISOString(),
      updatedAt: dbUser.updated_at?.toISOString()
    };
  }
}
