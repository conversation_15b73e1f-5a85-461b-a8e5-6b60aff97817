import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove,
} from 'firebase/firestore';
import { db } from './firebase/config';
import {
  User,
  Pet,
  Booking,
  Transaction,
  RewardTransaction,
  RewardItem,
  MembershipStatus,
  BookingStatus,
  TransactionType,
} from '@/types/user';

// Collection references
export const COLLECTIONS = {
  USERS: 'users',
  PETS: 'pets',
  BOOKINGS: 'bookings',
  TRANSACTIONS: 'transactions',
  REWARD_TRANSACTIONS: 'rewardTransactions',
  REWARD_ITEMS: 'rewardItems',
  PROVIDERS: 'providers',
  SERVICES: 'services',
} as const;

// User Management
export class UserService {
  static async createUser(userId: string, userData: Partial<User>): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const defaultUser: Partial<User> = {
      fetchlyBalance: 0,
      membershipStatus: 'free',
      rewardPoints: 0,
      savedProviders: [],
      notificationPreferences: {
        email: true,
        sms: false,
        push: true,
        marketing: false,
        bookingReminders: true,
        promotions: false,
      },
      joinedDate: Timestamp.now(),
      ...userData,
    };
    
    await setDoc(userRef, defaultUser);
  }

  static async getUser(userId: string): Promise<User | null> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      return { id: userSnap.id, ...userSnap.data() } as User;
    }
    return null;
  }

  static async updateUser(userId: string, updates: Partial<User>): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, { ...updates, updatedAt: Timestamp.now() });
  }

  static async updateBalance(userId: string, amount: number): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, {
      fetchlyBalance: increment(amount),
    });
  }

  static async updateRewardPoints(userId: string, points: number): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, {
      rewardPoints: increment(points),
    });
  }

  static async upgradeMembership(userId: string): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, {
      membershipStatus: 'pro',
      membershipStartDate: Timestamp.now(),
      membershipRenewalDate: Timestamp.fromDate(
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      ),
    });
  }

  static async addSavedProvider(userId: string, providerId: string): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, {
      savedProviders: arrayUnion(providerId),
    });
  }

  static async removeSavedProvider(userId: string, providerId: string): Promise<void> {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, {
      savedProviders: arrayRemove(providerId),
    });
  }
}

// Pet Management
export class PetService {
  static async createPet(petData: Omit<Pet, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const petsRef = collection(db, COLLECTIONS.PETS);
    const newPet = {
      ...petData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };
    
    const docRef = await addDoc(petsRef, newPet);
    return docRef.id;
  }

  static async getPet(petId: string): Promise<Pet | null> {
    const petRef = doc(db, COLLECTIONS.PETS, petId);
    const petSnap = await getDoc(petRef);
    
    if (petSnap.exists()) {
      return { id: petSnap.id, ...petSnap.data() } as Pet;
    }
    return null;
  }

  static async getUserPets(userId: string): Promise<Pet[]> {
    const petsRef = collection(db, COLLECTIONS.PETS);
    const q = query(
      petsRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Pet[];
  }

  static async updatePet(petId: string, updates: Partial<Pet>): Promise<void> {
    const petRef = doc(db, COLLECTIONS.PETS, petId);
    await updateDoc(petRef, { ...updates, updatedAt: Timestamp.now() });
  }

  static async deletePet(petId: string): Promise<void> {
    const petRef = doc(db, COLLECTIONS.PETS, petId);
    await deleteDoc(petRef);
  }

  static async addVaccination(petId: string, vaccination: any): Promise<void> {
    const petRef = doc(db, COLLECTIONS.PETS, petId);
    await updateDoc(petRef, {
      vaccinations: arrayUnion(vaccination),
      updatedAt: Timestamp.now(),
    });
  }

  static async addMedication(petId: string, medication: any): Promise<void> {
    const petRef = doc(db, COLLECTIONS.PETS, petId);
    await updateDoc(petRef, {
      medications: arrayUnion(medication),
      updatedAt: Timestamp.now(),
    });
  }
}

// Booking Management
export class BookingService {
  static async createBooking(bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const bookingsRef = collection(db, COLLECTIONS.BOOKINGS);
    const newBooking = {
      ...bookingData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };
    
    const docRef = await addDoc(bookingsRef, newBooking);
    return docRef.id;
  }

  static async getBooking(bookingId: string): Promise<Booking | null> {
    const bookingRef = doc(db, COLLECTIONS.BOOKINGS, bookingId);
    const bookingSnap = await getDoc(bookingRef);
    
    if (bookingSnap.exists()) {
      return { id: bookingSnap.id, ...bookingSnap.data() } as Booking;
    }
    return null;
  }

  static async getUserBookings(userId: string, limitCount = 50): Promise<Booking[]> {
    const bookingsRef = collection(db, COLLECTIONS.BOOKINGS);
    const q = query(
      bookingsRef,
      where('userId', '==', userId),
      orderBy('scheduledDate', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Booking[];
  }

  static async getUpcomingBookings(userId: string): Promise<Booking[]> {
    const bookingsRef = collection(db, COLLECTIONS.BOOKINGS);
    const q = query(
      bookingsRef,
      where('userId', '==', userId),
      where('status', 'in', ['pending', 'confirmed']),
      where('scheduledDate', '>=', Timestamp.now()),
      orderBy('scheduledDate', 'asc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Booking[];
  }

  static async updateBookingStatus(bookingId: string, status: BookingStatus): Promise<void> {
    const bookingRef = doc(db, COLLECTIONS.BOOKINGS, bookingId);
    const updates: any = {
      status,
      updatedAt: Timestamp.now(),
    };

    if (status === 'completed') {
      updates.completedAt = Timestamp.now();
    } else if (status === 'cancelled') {
      updates.cancelledAt = Timestamp.now();
    }

    await updateDoc(bookingRef, updates);
  }

  static async addReview(bookingId: string, rating: number, review: string): Promise<void> {
    const bookingRef = doc(db, COLLECTIONS.BOOKINGS, bookingId);
    await updateDoc(bookingRef, {
      rating,
      review,
      reviewDate: Timestamp.now(),
      updatedAt: Timestamp.now(),
    });
  }

  static async rescheduleBooking(
    bookingId: string,
    newDate: Timestamp,
    newTime: string
  ): Promise<void> {
    const bookingRef = doc(db, COLLECTIONS.BOOKINGS, bookingId);
    await updateDoc(bookingRef, {
      scheduledDate: newDate,
      scheduledTime: newTime,
      updatedAt: Timestamp.now(),
    });
  }
}

// Transaction Management
export class TransactionService {
  static async createTransaction(
    transactionData: Omit<Transaction, 'id' | 'createdAt'>
  ): Promise<string> {
    const transactionsRef = collection(db, COLLECTIONS.TRANSACTIONS);
    const newTransaction = {
      ...transactionData,
      createdAt: Timestamp.now(),
      status: 'pending' as const,
    };

    const docRef = await addDoc(transactionsRef, newTransaction);
    return docRef.id;
  }

  static async getTransaction(transactionId: string): Promise<Transaction | null> {
    const transactionRef = doc(db, COLLECTIONS.TRANSACTIONS, transactionId);
    const transactionSnap = await getDoc(transactionRef);

    if (transactionSnap.exists()) {
      return { id: transactionSnap.id, ...transactionSnap.data() } as Transaction;
    }
    return null;
  }

  static async getUserTransactions(userId: string, limitCount = 50): Promise<Transaction[]> {
    const transactionsRef = collection(db, COLLECTIONS.TRANSACTIONS);
    const q = query(
      transactionsRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as Transaction[];
  }

  static async processTransaction(transactionId: string): Promise<void> {
    const transactionRef = doc(db, COLLECTIONS.TRANSACTIONS, transactionId);
    await updateDoc(transactionRef, {
      status: 'completed',
      processedAt: Timestamp.now(),
    });
  }

  static async failTransaction(transactionId: string, reason: string): Promise<void> {
    const transactionRef = doc(db, COLLECTIONS.TRANSACTIONS, transactionId);
    await updateDoc(transactionRef, {
      status: 'failed',
      failureReason: reason,
      processedAt: Timestamp.now(),
    });
  }

  static async addFunds(
    userId: string,
    amount: number,
    paymentMethod: string,
    stripePaymentIntentId?: string
  ): Promise<string> {
    const batch = writeBatch(db);

    // Get current user balance
    const user = await UserService.getUser(userId);
    if (!user) throw new Error('User not found');

    const balanceBefore = user.fetchlyBalance;
    const balanceAfter = balanceBefore + amount;

    // Create transaction record
    const transactionRef = doc(collection(db, COLLECTIONS.TRANSACTIONS));
    const transactionData: Omit<Transaction, 'id'> = {
      userId,
      type: 'deposit',
      amount,
      description: `Added funds via ${paymentMethod}`,
      paymentMethod: paymentMethod as any,
      stripePaymentIntentId,
      balanceBefore,
      balanceAfter,
      status: 'completed',
      createdAt: Timestamp.now(),
      processedAt: Timestamp.now(),
    };

    batch.set(transactionRef, transactionData);

    // Update user balance
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    batch.update(userRef, {
      fetchlyBalance: balanceAfter,
    });

    await batch.commit();
    return transactionRef.id;
  }

  static async processPayment(
    userId: string,
    bookingId: string,
    amount: number,
    description: string
  ): Promise<string> {
    const batch = writeBatch(db);

    // Get current user balance
    const user = await UserService.getUser(userId);
    if (!user) throw new Error('User not found');

    if (user.fetchlyBalance < amount) {
      throw new Error('Insufficient balance');
    }

    const balanceBefore = user.fetchlyBalance;
    const balanceAfter = balanceBefore - amount;

    // Create transaction record
    const transactionRef = doc(collection(db, COLLECTIONS.TRANSACTIONS));
    const transactionData: Omit<Transaction, 'id'> = {
      userId,
      type: 'payment',
      amount: -amount, // Negative for payments
      description,
      bookingId,
      paymentMethod: 'fetchly_balance',
      balanceBefore,
      balanceAfter,
      status: 'completed',
      createdAt: Timestamp.now(),
      processedAt: Timestamp.now(),
    };

    batch.set(transactionRef, transactionData);

    // Update user balance
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    batch.update(userRef, {
      fetchlyBalance: balanceAfter,
    });

    // Update booking payment status
    const bookingRef = doc(db, COLLECTIONS.BOOKINGS, bookingId);
    batch.update(bookingRef, {
      paidAmount: amount,
      paymentMethod: 'fetchly_balance',
      updatedAt: Timestamp.now(),
    });

    await batch.commit();
    return transactionRef.id;
  }
}

// Rewards Management
export class RewardsService {
  static async createRewardTransaction(
    rewardData: Omit<RewardTransaction, 'id' | 'createdAt'>
  ): Promise<string> {
    const rewardsRef = collection(db, COLLECTIONS.REWARD_TRANSACTIONS);
    const newReward = {
      ...rewardData,
      createdAt: Timestamp.now(),
    };

    const docRef = await addDoc(rewardsRef, newReward);
    return docRef.id;
  }

  static async getUserRewardTransactions(userId: string): Promise<RewardTransaction[]> {
    const rewardsRef = collection(db, COLLECTIONS.REWARD_TRANSACTIONS);
    const q = query(
      rewardsRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as RewardTransaction[];
  }

  static async earnPoints(
    userId: string,
    points: number,
    description: string,
    bookingId?: string
  ): Promise<void> {
    const batch = writeBatch(db);

    // Create reward transaction
    const rewardRef = doc(collection(db, COLLECTIONS.REWARD_TRANSACTIONS));
    const rewardData: Omit<RewardTransaction, 'id'> = {
      userId,
      type: 'earned',
      points,
      description,
      bookingId,
      createdAt: Timestamp.now(),
    };

    batch.set(rewardRef, rewardData);

    // Update user points
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    batch.update(userRef, {
      rewardPoints: increment(points),
    });

    await batch.commit();
  }

  static async redeemPoints(
    userId: string,
    points: number,
    rewardItemId: string,
    rewardItemName: string
  ): Promise<void> {
    const batch = writeBatch(db);

    // Get current user points
    const user = await UserService.getUser(userId);
    if (!user) throw new Error('User not found');

    if (user.rewardPoints < points) {
      throw new Error('Insufficient reward points');
    }

    // Create reward transaction
    const rewardRef = doc(collection(db, COLLECTIONS.REWARD_TRANSACTIONS));
    const rewardData: Omit<RewardTransaction, 'id'> = {
      userId,
      type: 'redeemed',
      points: -points, // Negative for redemptions
      description: `Redeemed: ${rewardItemName}`,
      rewardItemId,
      rewardItemName,
      createdAt: Timestamp.now(),
    };

    batch.set(rewardRef, rewardData);

    // Update user points
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    batch.update(userRef, {
      rewardPoints: increment(-points),
    });

    await batch.commit();
  }

  static async getAvailableRewards(): Promise<RewardItem[]> {
    const rewardsRef = collection(db, COLLECTIONS.REWARD_ITEMS);
    const q = query(
      rewardsRef,
      where('isActive', '==', true),
      orderBy('pointsCost', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })) as RewardItem[];
  }
}
