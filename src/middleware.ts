import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // For admin routes, let the client-side handle authentication
  // This avoids Edge Runtime compatibility issues with Firebase Admin SDK
  if (pathname.startsWith('/admin')) {
    // Add a header to indicate this is an admin route
    const response = NextResponse.next();
    response.headers.set('x-admin-route', 'true');
    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
