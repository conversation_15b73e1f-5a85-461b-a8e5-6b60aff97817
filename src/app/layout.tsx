import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { AuthProvider } from "@/contexts/AuthContext";
import { DataProvider } from "@/contexts/DataContext";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const nunito = Nunito({
  subsets: ["latin"],
  variable: "--font-nunito",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Fetchly - Pet Services Ecosystem",
  description: "Find the perfect care for your pet. Book grooming, vet appointments, pet hotels and more.",
  keywords: ["pet services", "pet grooming", "veterinary", "pet hotels", "pet care"],
  authors: [{ name: "Fetchly Team" }],
  creator: "<PERSON><PERSON><PERSON>",
  publisher: "<PERSON><PERSON><PERSON>",
  icons: {
    icon: "/favicon.png",
    shortcut: "/favicon.png",
    apple: "/favicon.png",
  },
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://fetchly.app"),
  openGraph: {
    title: "Fetchly - Pet Services Ecosystem",
    description: "Find the perfect care for your pet. Book grooming, vet appointments, pet hotels and more.",
    url: "https://fetchly.app",
    siteName: "Fetchly",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Fetchly - Pet Services Ecosystem",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Fetchly - Pet Services Ecosystem",
    description: "Find the perfect care for your pet. Book grooming, vet appointments, pet hotels and more.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${nunito.variable}`} suppressHydrationWarning>
      <body className="font-sans antialiased min-h-screen" suppressHydrationWarning>
        <AuthProvider>
          <DataProvider>
            <div className="relative min-h-screen flex flex-col">
              <Header />
              <main className="flex-1">
                {children}
              </main>
              <Footer />
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(12px)',
                    border: '1px solid rgba(205, 237, 252, 0.3)',
                    borderRadius: '1rem',
                    color: '#1f2937',
                    fontFamily: 'Inter, system-ui, sans-serif',
                  },
                  success: {
                    iconTheme: {
                      primary: '#CDEDFC',
                      secondary: '#1f2937',
                    },
                  },
                  error: {
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#ffffff',
                    },
                  },
                }}
              />
            </div>
          </DataProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
