'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  Settings, Bell, Shield, Eye, Globe, Smartphone,
  Mail, Lock, CreditCard, Download, Trash2, ArrowLeft,
  Check, X, AlertTriangle, Info
} from 'lucide-react';
import QuickActions from '@/components/QuickActions';

interface SettingsSection {
  id: string;
  title: string;
  description: string;
  icon: any;
  settings: Setting[];
}

interface Setting {
  id: string;
  label: string;
  description: string;
  type: 'toggle' | 'select' | 'button';
  value?: boolean | string;
  options?: string[];
  action?: () => void;
  danger?: boolean;
}

export default function SettingsPage() {
  const { user, signOut } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<Record<string, any>>({
    notifications: {
      email: true,
      push: true,
      sms: false,
      marketing: false
    },
    privacy: {
      profileVisibility: 'public',
      showLocation: true,
      showPets: true
    },
    security: {
      twoFactor: false,
      loginAlerts: true
    }
  });

  useEffect(() => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }
    setLoading(false);
  }, [user, router]);

  const updateSetting = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const settingsSections: SettingsSection[] = [
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Manage how you receive updates and alerts',
      icon: Bell,
      settings: [
        {
          id: 'email',
          label: 'Email Notifications',
          description: 'Receive updates about appointments and messages via email',
          type: 'toggle',
          value: settings.notifications.email
        },
        {
          id: 'push',
          label: 'Push Notifications',
          description: 'Get instant notifications on your device',
          type: 'toggle',
          value: settings.notifications.push
        },
        {
          id: 'sms',
          label: 'SMS Notifications',
          description: 'Receive text messages for urgent updates',
          type: 'toggle',
          value: settings.notifications.sms
        },
        {
          id: 'marketing',
          label: 'Marketing Communications',
          description: 'Receive promotional offers and news',
          type: 'toggle',
          value: settings.notifications.marketing
        }
      ]
    },
    {
      id: 'privacy',
      title: 'Privacy',
      description: 'Control your privacy and data sharing preferences',
      icon: Eye,
      settings: [
        {
          id: 'profileVisibility',
          label: 'Profile Visibility',
          description: 'Who can see your profile information',
          type: 'select',
          value: settings.privacy.profileVisibility,
          options: ['public', 'friends', 'private']
        },
        {
          id: 'showLocation',
          label: 'Show Location',
          description: 'Display your city in your profile',
          type: 'toggle',
          value: settings.privacy.showLocation
        },
        {
          id: 'showPets',
          label: 'Show Pets',
          description: 'Display your pets in your public profile',
          type: 'toggle',
          value: settings.privacy.showPets
        }
      ]
    },
    {
      id: 'security',
      title: 'Security',
      description: 'Manage your account security settings',
      icon: Shield,
      settings: [
        {
          id: 'twoFactor',
          label: 'Two-Factor Authentication',
          description: 'Add an extra layer of security to your account',
          type: 'toggle',
          value: settings.security.twoFactor
        },
        {
          id: 'loginAlerts',
          label: 'Login Alerts',
          description: 'Get notified when someone logs into your account',
          type: 'toggle',
          value: settings.security.loginAlerts
        },
        {
          id: 'changePassword',
          label: 'Change Password',
          description: 'Update your account password',
          type: 'button',
          action: () => alert('Password change functionality coming soon!')
        }
      ]
    },
    {
      id: 'account',
      title: 'Account Management',
      description: 'Manage your account data and preferences',
      icon: Settings,
      settings: [
        {
          id: 'downloadData',
          label: 'Download My Data',
          description: 'Download a copy of your account data',
          type: 'button',
          action: () => alert('Data download will be available soon!')
        },
        {
          id: 'deleteAccount',
          label: 'Delete Account',
          description: 'Permanently delete your account and all data',
          type: 'button',
          danger: true,
          action: () => {
            if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
              alert('Account deletion functionality coming soon!');
            }
          }
        }
      ]
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/dashboard')}
                className="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-lg"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-3xl font-bold">Settings</h1>
                <p className="text-gray-300 mt-2">Manage your account preferences and privacy</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {settingsSections.map((section) => {
              const Icon = section.icon;
              return (
                <motion.div
                  key={section.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-lg shadow-sm overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <Icon className="w-5 h-5 text-gray-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                        <p className="text-sm text-gray-600">{section.description}</p>
                      </div>
                    </div>
                  </div>

                  <div className="divide-y divide-gray-200">
                    {section.settings.map((setting) => (
                      <div key={setting.id} className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="text-sm font-medium text-gray-900">{setting.label}</h4>
                              {setting.danger && (
                                <AlertTriangle className="w-4 h-4 text-red-500" />
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{setting.description}</p>
                          </div>

                          <div className="ml-4">
                            {setting.type === 'toggle' && (
                              <button
                                onClick={() => updateSetting(section.id, setting.id, !setting.value)}
                                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                                  setting.value ? 'bg-blue-600' : 'bg-gray-200'
                                }`}
                              >
                                <span
                                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                    setting.value ? 'translate-x-6' : 'translate-x-1'
                                  }`}
                                />
                              </button>
                            )}

                            {setting.type === 'select' && (
                              <select
                                value={setting.value as string}
                                onChange={(e) => updateSetting(section.id, setting.id, e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              >
                                {setting.options?.map((option) => (
                                  <option key={option} value={option}>
                                    {option.charAt(0).toUpperCase() + option.slice(1)}
                                  </option>
                                ))}
                              </select>
                            )}

                            {setting.type === 'button' && (
                              <button
                                onClick={setting.action}
                                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                                  setting.danger
                                    ? 'bg-red-600 text-white hover:bg-red-700'
                                    : 'bg-blue-600 text-white hover:bg-blue-700'
                                }`}
                              >
                                {setting.danger ? 'Delete' : 'Manage'}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Sidebar */}
          <div>
            <QuickActions currentPage="settings" />
          </div>
        </div>
      </div>
    </div>
  );
}
