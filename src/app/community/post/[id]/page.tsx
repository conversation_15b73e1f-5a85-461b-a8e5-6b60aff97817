'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  ArrowLeft, 
  Calendar,
  User,
  Globe,
  Lock,
  Eye
} from 'lucide-react';
import { doc, getDoc, updateDoc, increment } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import CommentsModal from '@/components/CommentsModal';
import ShareModal from '@/components/ShareModal';
import toast from 'react-hot-toast';
import Link from 'next/link';

interface PublicPost {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  userRole: 'provider' | 'petowner';
  content: string;
  image?: string;
  likes: number;
  comments: number;
  timestamp: Date;
  isPublic: boolean;
  likedBy: string[];
  views: number;
}

export default function PublicPostPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const postId = params.id as string;
  
  const [post, setPost] = useState<PublicPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [showComments, setShowComments] = useState(false);
  const [showShare, setShowShare] = useState(false);
  const [isLiking, setIsLiking] = useState(false);

  // Load post data
  useEffect(() => {
    const loadPost = async () => {
      if (!postId) return;

      try {
        const postDoc = await getDoc(doc(db, 'posts', postId));
        
        if (postDoc.exists()) {
          const postData = postDoc.data();
          
          // Check if post is public or user has access
          if (!postData.isPublic && (!user || user.id !== postData.userId)) {
            toast.error('This post is private or not found');
            router.push('/community');
            return;
          }

          const post: PublicPost = {
            id: postDoc.id,
            ...postData,
            timestamp: postData.timestamp?.toDate() || new Date(),
            views: postData.views || 0
          } as PublicPost;

          setPost(post);

          // Increment view count
          await updateDoc(doc(db, 'posts', postId), {
            views: increment(1)
          });
        } else {
          toast.error('Post not found');
          router.push('/community');
        }
      } catch (error) {
        console.error('Error loading post:', error);
        toast.error('Failed to load post');
        router.push('/community');
      } finally {
        setLoading(false);
      }
    };

    loadPost();
  }, [postId, user, router]);

  // Handle like
  const handleLike = async () => {
    if (!post || !user || isLiking) return;

    try {
      setIsLiking(true);
      
      const isCurrentlyLiked = post.likedBy.includes(user.id);
      const newLikedBy = isCurrentlyLiked
        ? post.likedBy.filter(id => id !== user.id)
        : [...post.likedBy, user.id];

      const postRef = doc(db, 'posts', post.id);
      await updateDoc(postRef, {
        likes: newLikedBy.length,
        likedBy: newLikedBy
      });

      setPost(prev => prev ? {
        ...prev,
        likes: newLikedBy.length,
        likedBy: newLikedBy
      } : null);
    } catch (error) {
      console.error('Error liking post:', error);
      toast.error('Failed to like post');
    } finally {
      setIsLiking(false);
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading post...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Post Not Found</h1>
          <p className="text-gray-600 mb-6">The post you're looking for doesn't exist or is private.</p>
          <Link
            href="/community"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Community
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Eye className="w-4 h-4" />
                <span>{post.views} views</span>
              </div>
              
              {!user && (
                <Link
                  href="/auth/login"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Join Community
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {/* Post Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img
                  src={post.userAvatar || '/favicon.png'}
                  alt={post.userName}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <div className="flex items-center space-x-2">
                    <h2 className="text-lg font-semibold text-gray-900">{post.userName}</h2>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      post.userRole === 'provider' 
                        ? 'bg-blue-100 text-blue-700' 
                        : 'bg-green-100 text-green-700'
                    }`}>
                      {post.userRole === 'provider' ? 'Provider' : 'Pet Owner'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatTimeAgo(post.timestamp)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {post.isPublic ? (
                        <>
                          <Globe className="w-4 h-4" />
                          <span>Public</span>
                        </>
                      ) : (
                        <>
                          <Lock className="w-4 h-4" />
                          <span>Private</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Post Content */}
          <div className="p-6">
            <p className="text-gray-800 text-lg leading-relaxed whitespace-pre-wrap">
              {post.content}
            </p>
            
            {post.image && (
              <div className="mt-6">
                <img
                  src={post.image}
                  alt="Post content"
                  className="w-full max-h-96 object-cover rounded-lg"
                />
              </div>
            )}
          </div>

          {/* Post Actions */}
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <button
                  onClick={handleLike}
                  disabled={!user || isLiking}
                  className={`flex items-center space-x-2 transition-colors ${
                    user && post.likedBy.includes(user.id)
                      ? 'text-red-500 hover:text-red-600'
                      : 'text-gray-500 hover:text-gray-700'
                  } ${!user ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <Heart className={`w-5 h-5 ${
                    user && post.likedBy.includes(user.id) ? 'fill-current' : ''
                  }`} />
                  <span className="font-medium">{post.likes}</span>
                </button>

                <button
                  onClick={() => setShowComments(true)}
                  className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span className="font-medium">{post.comments}</span>
                </button>

                <button
                  onClick={() => setShowShare(true)}
                  className="flex items-center space-x-2 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <Share2 className="w-5 h-5" />
                  <span className="font-medium">Share</span>
                </button>
              </div>

              {!user && (
                <div className="text-sm text-gray-500">
                  <Link href="/auth/login" className="text-blue-600 hover:text-blue-700">
                    Sign in
                  </Link> to interact with this post
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Call to Action for Non-Users */}
        {!user && (
          <div className="mt-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-8 text-center text-white">
            <h3 className="text-2xl font-bold mb-4">Join the Fetchly Community</h3>
            <p className="text-blue-100 mb-6">
              Connect with pet owners and service providers. Share your pet stories, get advice, and discover amazing services.
            </p>
            <div className="flex items-center justify-center space-x-4">
              <Link
                href="/auth/register"
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                Sign Up Free
              </Link>
              <Link
                href="/auth/login"
                className="border border-white text-white px-6 py-3 rounded-lg font-medium hover:bg-white hover:text-blue-600 transition-colors"
              >
                Sign In
              </Link>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <CommentsModal
        postId={post.id}
        postAuthor={post.userName}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
      />

      <ShareModal
        post={post}
        isOpen={showShare}
        onClose={() => setShowShare(false)}
      />
    </div>
  );
}
