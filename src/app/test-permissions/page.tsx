'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  Timestamp,
  doc,
  updateDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import toast from 'react-hot-toast';

export default function TestPermissionsPage() {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runPermissionTests = async () => {
    if (!user) {
      toast.error('Please log in to run tests');
      return;
    }

    setIsRunning(true);
    setTestResults([]);
    
    try {
      addResult('🔍 Starting permission tests...');
      addResult(`👤 User: ${user.name} (${user.id})`);
      addResult(`🔑 Role: ${user.role}`);

      // Test 1: Create a test post
      addResult('📝 Test 1: Creating a test post...');
      const testPostData = {
        userId: user.id,
        userName: user.name || user.displayName || 'Test User',
        userAvatar: user.avatar || user.photoURL || '/favicon.png',
        userRole: user.role || 'petowner',
        content: 'This is a test post for permission testing',
        timestamp: Timestamp.now(),
        likes: 0,
        likedBy: [],
        comments: 0,
        views: 0,
        isPublic: true
      };

      const postRef = await addDoc(collection(db, 'posts'), testPostData);
      addResult(`✅ Test post created with ID: ${postRef.id}`);

      // Test 2: Like the test post
      addResult('❤️ Test 2: Liking the test post...');
      await updateDoc(doc(db, 'posts', postRef.id), {
        likes: 1,
        likedBy: [user.id]
      });
      addResult('✅ Successfully liked the test post');

      // Test 3: Create a test comment
      addResult('💬 Test 3: Creating a test comment...');
      const testCommentData = {
        postId: postRef.id,
        userId: user.id,
        userName: user.name || user.displayName || 'Test User',
        userAvatar: user.avatar || user.photoURL || '/favicon.png',
        userRole: user.role || 'petowner',
        content: 'This is a test comment',
        timestamp: Timestamp.now(),
        likes: 0,
        likedBy: []
      };

      const commentRef = await addDoc(collection(db, 'comments'), testCommentData);
      addResult(`✅ Test comment created with ID: ${commentRef.id}`);

      // Test 4: Update post comment count
      addResult('🔢 Test 4: Updating post comment count...');
      await updateDoc(doc(db, 'posts', postRef.id), {
        comments: 1
      });
      addResult('✅ Successfully updated comment count');

      // Test 5: Query comments
      addResult('🔍 Test 5: Querying comments...');
      const commentsQuery = query(
        collection(db, 'comments'),
        where('postId', '==', postRef.id)
      );
      const commentsSnapshot = await getDocs(commentsQuery);
      addResult(`✅ Found ${commentsSnapshot.docs.length} comments`);

      // Test 6: Like the comment
      addResult('❤️ Test 6: Liking the test comment...');
      await updateDoc(doc(db, 'comments', commentRef.id), {
        likes: 1,
        likedBy: [user.id]
      });
      addResult('✅ Successfully liked the test comment');

      addResult('🎉 All permission tests passed!');
      toast.success('All permission tests passed!');

    } catch (error: any) {
      addResult(`❌ Error: ${error.message}`);
      console.error('Permission test error:', error);
      toast.error(`Test failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Permission Test Tool</h1>
          <p className="text-gray-600">Please log in to test Firebase permissions.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">Firebase Permission Test Tool</h1>
          
          <div className="mb-6">
            <div className="bg-blue-50 rounded-lg p-4 mb-4">
              <h2 className="font-semibold text-blue-900 mb-2">Current User</h2>
              <div className="text-sm text-blue-700">
                <p><strong>Name:</strong> {user.name || user.displayName || 'N/A'}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Role:</strong> {user.role || 'N/A'}</p>
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={runPermissionTests}
                disabled={isRunning}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isRunning ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Running Tests...</span>
                  </>
                ) : (
                  <span>Run Permission Tests</span>
                )}
              </button>

              <button
                onClick={clearResults}
                className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700"
              >
                Clear Results
              </button>
            </div>
          </div>

          {testResults.length > 0 && (
            <div className="bg-gray-100 rounded-lg p-4">
              <h2 className="font-semibold text-gray-800 mb-4">Test Results</h2>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`text-sm font-mono p-2 rounded ${
                      result.includes('❌') 
                        ? 'bg-red-100 text-red-800' 
                        : result.includes('✅') 
                        ? 'bg-green-100 text-green-800'
                        : result.includes('🎉')
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-50 text-gray-700'
                    }`}
                  >
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mt-8 bg-yellow-50 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-900 mb-2">What This Tests</h3>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• Creating posts with proper user data</li>
              <li>• Liking posts (updating likes and likedBy arrays)</li>
              <li>• Creating comments with proper permissions</li>
              <li>• Updating post comment counts</li>
              <li>• Querying comments by postId</li>
              <li>• Liking comments</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
