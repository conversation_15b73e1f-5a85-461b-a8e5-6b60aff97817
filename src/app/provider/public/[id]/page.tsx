'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Provider, Service } from '@/lib/firebase/providers';
import { getProviderByUserId, getProviderServices } from '@/lib/firebase/providers';
import { collection, query, where, getDocs, doc, getDoc, addDoc, deleteDoc, updateDoc, increment } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  MapPin, Phone, Mail, Globe, Award, Star, Clock, CheckCircle,
  Heart, Share2, MessageCircle, Calendar, PawPrint, ArrowLeft,
  Verified, Shield, Users, TrendingUp
} from 'lucide-react';
import toast from 'react-hot-toast';

export default function PublicProviderProfile({ params }: { params: Promise<{ id: string }> }) {
  const { user } = useAuth();
  const router = useRouter();
  const resolvedParams = use(params);
  const providerId = resolvedParams.id;
  const [provider, setProvider] = useState<Provider | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviews, setReviews] = useState<any[]>([]);

  useEffect(() => {
    const loadProviderData = async () => {
      try {
        setLoading(true);
        console.log('🔍 Loading public provider profile for userId:', providerId);

        // Load provider data
        let providerData;
        try {
          providerData = await getProviderByUserId(providerId);
        } catch (error) {
          console.error('Error fetching provider data:', error);
          throw new Error('Failed to load provider data. Please try again later.');
        }

        if (!providerData) {
          toast.error('Provider not found');
          router.push('/community');
          return;
        }

        setProvider(providerData);
        console.log('✅ Provider loaded:', {
          businessName: providerData.businessName,
          profilePhoto: providerData.profilePhoto,
          bannerPhoto: providerData.bannerPhoto,
          hasProfilePhoto: !!providerData.profilePhoto,
          hasBannerPhoto: !!providerData.bannerPhoto
        });

        // Load services
        const servicesData = await getProviderServices(providerData.id!);
        setServices(servicesData);

        // Load public posts
        const postsQuery = query(
          collection(db, 'posts'),
          where('userId', '==', providerId),
          where('isPublic', '==', true)
        );
        const postsSnapshot = await getDocs(postsQuery);
        const postsData = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate() || new Date()
        }));
        setPosts(postsData);

        // Load real follower count from Firestore
        await loadFollowerCount();

        // Load reviews
        await loadReviews();

        // Check if current user is following this provider
        if (user) {
          await checkIfFollowing();
        }

      } catch (error) {
        console.error('❌ Error loading provider:', error);
        toast.error('Failed to load provider profile');
      } finally {
        setLoading(false);
      }
    };

    loadProviderData();
  }, [providerId, router]);

  // Load follower count from Firestore
  const loadFollowerCount = async () => {
    try {
      const followersQuery = query(
        collection(db, 'followers'),
        where('providerId', '==', providerId)
      );
      const followersSnapshot = await getDocs(followersQuery);
      setFollowerCount(followersSnapshot.size);
    } catch (error) {
      console.error('Error loading follower count:', error);
      setFollowerCount(0);
    }
  };

  // Check if current user is following this provider
  const checkIfFollowing = async () => {
    if (!user) return;

    try {
      const followQuery = query(
        collection(db, 'followers'),
        where('providerId', '==', providerId),
        where('userId', '==', user.id)
      );
      const followSnapshot = await getDocs(followQuery);
      setIsFollowing(!followSnapshot.empty);
    } catch (error) {
      console.error('Error checking follow status:', error);
    }
  };

  // Load reviews from Firestore
  const loadReviews = async () => {
    try {
      const reviewsQuery = query(
        collection(db, 'reviews'),
        where('providerId', '==', providerId)
      );
      const reviewsSnapshot = await getDocs(reviewsQuery);
      const reviewsData = reviewsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));
      setReviews(reviewsData);
    } catch (error) {
      console.error('Error loading reviews:', error);
    }
  };

  const handleBookService = (service: Service) => {
    if (!user) {
      // Store the current URL to redirect back after sign in
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to book services');
      return;
    }
    
    // TODO: Implement booking modal
    toast.success(`Booking for "${service.name}" - Coming soon!`);
  };



  const handleFollow = async () => {
    if (!user) {
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to follow providers');
      return;
    }

    try {
      if (isFollowing) {
        // Unfollow: Remove from followers collection
        const followQuery = query(
          collection(db, 'followers'),
          where('providerId', '==', providerId),
          where('userId', '==', user.id)
        );
        const followSnapshot = await getDocs(followQuery);

        if (!followSnapshot.empty) {
          await deleteDoc(followSnapshot.docs[0].ref);
          setIsFollowing(false);
          setFollowerCount(prev => prev - 1);
          toast.success('Unfollowed provider');
        }
      } else {
        // Follow: Add to followers collection
        await addDoc(collection(db, 'followers'), {
          providerId: providerId,
          userId: user.id,
          userName: user.name,
          userEmail: user.email,
          followedAt: new Date()
        });

        setIsFollowing(true);
        setFollowerCount(prev => prev + 1);
        toast.success('Following provider!');
      }
    } catch (error) {
      console.error('Error following/unfollowing provider:', error);
      toast.error('Failed to update follow status');
    }
  };

  const handleBookNow = () => {
    if (!user) {
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to book services');
      return;
    }
    setShowBookingModal(true);
  };

  const handleViewAvailability = () => {
    setShowAvailabilityModal(true);
  };

  const handleLeaveReview = () => {
    if (!user) {
      const redirectUrl = `/provider/public/${providerId}`;
      router.push(`/auth/signin?redirect=${encodeURIComponent(redirectUrl)}`);
      toast.error('Please sign in to leave a review');
      return;
    }
    setShowReviewModal(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading provider profile...</p>
        </div>
      </div>
    );
  }

  if (!provider) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-6 max-w-md">
          <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Provider Not Found</h2>
          <p className="text-gray-600 mb-4">This provider profile doesn't exist or isn't available.</p>
          <button
            onClick={() => router.push('/community')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Community
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="w-5 h-5" />
              Back
            </button>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={`p-2 rounded-full ${isFavorite ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'} hover:scale-105 transition-all`}
              >
                <Heart className={`w-5 h-5 ${isFavorite ? 'fill-current' : ''}`} />
              </button>
              <button
                onClick={handleFollow}
                className={`p-2 rounded-full ${isFollowing ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'} hover:scale-105 transition-all`}
              >
                <Users className={`w-5 h-5 ${isFollowing ? 'fill-current' : ''}`} />
              </button>
              <button className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200">
                <Share2 className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Banner Section */}
      <div className="relative">
        {/* Banner Image */}
        <div className="h-80 bg-gradient-to-r from-blue-500 to-indigo-600 relative overflow-hidden">
          {/* Banner Image */}
          {provider.bannerPhoto && provider.bannerPhoto !== '/fetchlylogo.png' ? (
            <img
              src={provider.bannerPhoto}
              alt="Provider Banner"
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error('Banner image failed to load:', provider.bannerPhoto);
                e.currentTarget.style.display = 'none';
              }}
              onLoad={() => {
                console.log('✅ Banner image loaded successfully:', provider.bannerPhoto);
              }}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center">
              <div className="text-center text-white">
                <PawPrint className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <h2 className="text-2xl font-bold opacity-75">{provider.businessName || provider.ownerName}</h2>
                <p className="text-lg opacity-60">{provider.serviceType}</p>
              </div>
            </div>
          )}

          {/* Debug info */}
          <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs p-1 rounded">
            Banner: {provider.bannerPhoto || 'NOT SET'}
            {provider.bannerPhoto && (
              <div>Full URL: {provider.bannerPhoto}</div>
            )}
          </div>

          {/* Overlay for better text readability */}
          <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        {/* Profile Section */}
        <div className="max-w-6xl mx-auto px-4">
          <div className="relative -mt-16 bg-white rounded-xl shadow-lg p-8 mb-6">
            <div className="flex items-start gap-6">
              {/* Profile Photo */}
              <div className="relative">
                {provider.profilePhoto ? (
                  <img
                    src={provider.profilePhoto}
                    alt={provider.businessName || provider.ownerName}
                    className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
                  />
                ) : (
                  <div className="w-32 h-32 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white text-3xl font-bold border-4 border-white shadow-lg">
                    {provider.businessName?.charAt(0) || provider.ownerName?.charAt(0) || 'P'}
                  </div>
                )}
                {/* Online Status Indicator */}
                <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-500 border-2 border-white rounded-full"></div>
              </div>

            {/* Provider Info */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">
                  {provider.businessName || provider.ownerName}
                </h1>
                {provider.verified && (
                  <Verified className="w-6 h-6 text-blue-500" />
                )}
                {provider.featured && (
                  <Award className="w-6 h-6 text-yellow-500" />
                )}
              </div>

              <p className="text-lg text-gray-600 mb-4">{provider.serviceType}</p>

              <div className="flex items-center gap-6 mb-4">
                <div className="flex items-center gap-1">
                  <Star className="w-5 h-5 text-yellow-500 fill-current" />
                  <span className="font-semibold">{provider.rating || 5.0}</span>
                  <span className="text-gray-500">({provider.reviewCount || 0} reviews)</span>
                </div>
                <div className="flex items-center gap-1 text-gray-600">
                  <MapPin className="w-4 h-4" />
                  <span>{provider.city}, {provider.state}</span>
                </div>
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span>Available</span>
                </div>
              </div>

              <p className="text-gray-700 mb-6">{provider.description}</p>

              {/* Action Buttons */}
              <div className="flex gap-3 flex-wrap">
                <button
                  onClick={handleBookNow}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2 font-semibold"
                >
                  <Calendar className="w-5 h-5" />
                  Book Now
                </button>
                <button
                  onClick={handleFollow}
                  className={`px-6 py-3 rounded-lg flex items-center gap-2 transition-all ${
                    isFollowing
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'border border-blue-600 text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  <Users className="w-5 h-5" />
                  {isFollowing ? `Following (${followerCount})` : `Follow (${followerCount})`}
                </button>
                <button
                  onClick={handleViewAvailability}
                  className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 flex items-center gap-2"
                >
                  <Clock className="w-5 h-5" />
                  View Availability
                </button>
                {user && (
                  <button
                    onClick={handleLeaveReview}
                    className="border border-yellow-500 text-yellow-600 px-6 py-3 rounded-lg hover:bg-yellow-50 flex items-center gap-2"
                  >
                    <Star className="w-5 h-5" />
                    Leave Review
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Services */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Services Offered</h2>
              <div className="grid gap-4">
                {services.map((service) => (
                  <div key={service.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-gray-900">{service.name}</h3>
                      <span className="text-lg font-bold text-blue-600">${service.price}</span>
                    </div>
                    <p className="text-gray-600 mb-3">{service.description}</p>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-1 text-gray-500">
                        <Clock className="w-4 h-4" />
                        <span>{service.duration || '1 hour'}</span>
                      </div>
                      <button
                        onClick={() => handleBookService(service)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                      >
                        Book Now
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Posts */}
            {posts.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Updates</h2>
                <div className="space-y-4">
                  {posts.slice(0, 3).map((post) => (
                    <div key={post.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                      <p className="text-gray-800 mb-2">{post.content}</p>
                      <span className="text-sm text-gray-500">
                        {post.timestamp.toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Reviews */}
            {reviews.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Customer Reviews</h2>
                <div className="space-y-4">
                  {reviews.slice(0, 3).map((review) => (
                    <div key={review.id} className="border-b border-gray-100 pb-4 last:border-b-0">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < (review.rating || 5) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="font-medium text-gray-900">{review.userName || 'Anonymous'}</span>
                      </div>
                      {review.title && (
                        <h4 className="font-medium text-gray-800 mb-1">{review.title}</h4>
                      )}
                      <p className="text-gray-600 mb-2">{review.comment}</p>
                      <span className="text-sm text-gray-500">
                        {review.createdAt.toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Info */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="font-bold text-gray-900 mb-4">Contact Information</h3>
              <div className="space-y-3">
                {provider.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">{provider.phone}</span>
                  </div>
                )}
                {provider.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">{provider.email}</span>
                  </div>
                )}
                {provider.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="w-5 h-5 text-gray-400" />
                    <a href={provider.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      Visit Website
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Stats */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="font-bold text-gray-900 mb-4">Provider Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Bookings</span>
                  <span className="font-semibold">{provider.totalBookings || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Response Rate</span>
                  <span className="font-semibold">{provider.responseRate || 100}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Response Time</span>
                  <span className="font-semibold">{provider.responseTime || '< 1 hour'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Book Service</h3>
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Service
                  </label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Choose a service...</option>
                    {services.map((service) => (
                      <option key={service.id} value={service.id}>
                        {service.name} - ${service.price}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Date
                  </label>
                  <input
                    type="date"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Time
                  </label>
                  <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select time...</option>
                    <option value="09:00">9:00 AM</option>
                    <option value="10:00">10:00 AM</option>
                    <option value="11:00">11:00 AM</option>
                    <option value="14:00">2:00 PM</option>
                    <option value="15:00">3:00 PM</option>
                    <option value="16:00">4:00 PM</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requests (Optional)
                  </label>
                  <textarea
                    rows={3}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Any special requests or notes..."
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => setShowBookingModal(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      // TODO: Submit booking request
                      toast.success('Booking request sent! Provider will be notified.');
                      setShowBookingModal(false);
                    }}
                    className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Send Request
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Availability Modal */}
      {showAvailabilityModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Available Hours</h3>
                <button
                  onClick={() => setShowAvailabilityModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                {provider?.businessHours && Object.entries(provider.businessHours).map(([day, hours]) => (
                  <div key={day} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium text-gray-900 capitalize">
                      {day}
                    </div>
                    <div className="text-gray-600">
                      {hours.closed ? (
                        <span className="text-red-500">Closed</span>
                      ) : (
                        <span>{hours.open} - {hours.close}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <button
                  onClick={() => setShowAvailabilityModal(false)}
                  className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Review Modal */}
      {showReviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Leave a Review</h3>
                <button
                  onClick={() => setShowReviewModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rating
                  </label>
                  <div className="flex gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        className="text-2xl text-yellow-400 hover:text-yellow-500"
                      >
                        ⭐
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Review Title
                  </label>
                  <input
                    type="text"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Great service!"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Review
                  </label>
                  <textarea
                    rows={4}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Tell others about your experience..."
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => setShowReviewModal(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      // TODO: Submit review to Firestore
                      toast.success('Review submitted! Thank you for your feedback.');
                      setShowReviewModal(false);
                      loadReviews(); // Reload reviews
                    }}
                    className="flex-1 px-4 py-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
                  >
                    Submit Review
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
    </div>
  );
}
