'use client';

import { useState } from 'react';
import { Star, MapPin, Phone, Calendar, Clock, Heart, Share2, MessageSquare } from 'lucide-react';

export default function ProviderProfileClient({ params }: { params: { id: string } }) {
  const [isFavorite, setIsFavorite] = useState(false);

  const mockProvider = {
    id: params.id,
    name: 'Dr. <PERSON> Veterinary Clinic',
    type: 'Veterinary Services',
    rating: 4.8,
    reviewCount: 127,
    location: 'Downtown Seattle, WA',
    phone: '+****************',
    description: 'Board-certified veterinarian with over 15 years of experience.',
    services: [
      { name: 'General Checkup', price: '$75', duration: '30 minutes' },
      { name: 'Vaccinations', price: '$45', duration: '15 minutes' }
    ]
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={i < Math.floor(rating) ? 'w-4 h-4 text-yellow-500 fill-current' : 'w-4 h-4 text-gray-300'}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        <div className="glass-card rounded-2xl p-8 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-cool-800 mb-2">{mockProvider.name}</h1>
              <p className="text-lg text-cool-600 mb-4">{mockProvider.type}</p>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-1">
                  {renderStars(mockProvider.rating)}
                  <span className="ml-2 text-sm font-medium text-cool-700">{mockProvider.rating}</span>
                  <span className="text-sm text-cool-500">({mockProvider.reviewCount} reviews)</span>
                </div>
              </div>
              <div className="flex items-center gap-6 text-sm text-cool-600">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{mockProvider.location}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Phone className="w-4 h-4" />
                  <span>{mockProvider.phone}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={`p-3 rounded-full transition-colors duration-200 ${
                  isFavorite ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Heart className={`w-5 h-5 ${isFavorite ? 'fill-current' : ''}`} />
              </button>
              <button className="p-3 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors duration-200">
                <Share2 className="w-5 h-5" />
              </button>
              <button className="btn-primary">
                <MessageSquare className="w-4 h-4 mr-2" />
                Contact
              </button>
            </div>
          </div>
          
          <p className="text-cool-700 leading-relaxed">{mockProvider.description}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="glass-card rounded-2xl p-6 mb-6">
              <h2 className="text-xl font-bold text-cool-800 mb-4">Services</h2>
              <div className="space-y-4">
                {mockProvider.services.map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-white/50 rounded-lg">
                    <div>
                      <h3 className="font-medium text-cool-800">{service.name}</h3>
                      <div className="flex items-center gap-4 text-sm text-cool-600 mt-1">
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>{service.duration}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-cool-800">{service.price}</div>
                      <button className="btn-primary mt-2">
                        <Calendar className="w-4 h-4 mr-2" />
                        Book Now
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="glass-card rounded-2xl p-6">
              <h3 className="text-lg font-bold text-cool-800 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full btn-primary">
                  <Calendar className="w-4 h-4 mr-2" />
                  Book Appointment
                </button>
                <button className="w-full btn-secondary">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Send Message
                </button>
                <button className="w-full btn-secondary">
                  <Phone className="w-4 h-4 mr-2" />
                  Call Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
