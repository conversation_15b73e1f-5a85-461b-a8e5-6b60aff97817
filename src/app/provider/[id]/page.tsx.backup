'use client';

import { useState } from 'react';
import { 
  Star, 
  MapPin, 
  Phone, 
  Globe, 
  Mail, 
  Calendar, 
  Clock, 
  DollarSign,
  Award,
  Verified,
  Heart,
  Share2,
  MessageSquare,
  Camera,
  ChevronLeft,
  ChevronRight,
  Facebook,
  Instagram,
  Twitter,
  CheckCircle,
  Users,
  Briefcase
} from 'lucide-react';
import Link from 'next/link';

interface ProviderProfile {
  id: string;
  name: string;
  type: string;
  rating: number;
  reviewCount: number;
  location: string;
  phone: string;
  email: string;
  website: string;
  description: string;
  services: Service[];
  availability: string[];
  pricing: PricingTier[];
  gallery: string[];
  reviews: Review[];
  verified: boolean;
  featured: boolean;
  responseTime: string;
  completionRate: number;
  yearsExperience: number;
  certifications: string[];
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  businessHours: {
    [key: string]: string;
  };
}

interface Service {
  id: string;
  name: string;
  description: string;
  duration: string;
  price: string;
}

interface PricingTier {
  name: string;
  price: string;
  description: string;
  features: string[];
}

interface Review {
  id: string;
  customerName: string;
  rating: number;
  comment: string;
  date: string;
  service: string;
}

const mockProvider: ProviderProfile = {
  id: '1',
  name: 'Dr. <PERSON> - Veterinary Care',
  type: 'Veterinary',
  rating: 4.8,
  reviewCount: 89,
  location: '123 Pet Care Ave, Los Angeles, CA 90210',
  phone: '+****************',
  email: '<EMAIL>',
  website: 'https://drchen-vet.com',
  description: 'Board-certified veterinarian with over 15 years of experience in small animal medicine and surgery. We provide comprehensive veterinary care with a focus on preventive medicine and compassionate treatment.',
  services: [
    {
      id: '1',
      name: 'Health Checkup',
      description: 'Comprehensive health examination including vital signs, weight check, and general assessment',
      duration: '30 minutes',
      price: '$80'
    },
    {
      id: '2',
      name: 'Vaccinations',
      description: 'Core and non-core vaccinations to keep your pet protected',
      duration: '15 minutes',
      price: '$45'
    },
    {
      id: '3',
      name: 'Surgery',
      description: 'Surgical procedures including spay/neuter and emergency surgery',
      duration: '2-4 hours',
      price: '$300-800'
    },
    {
      id: '4',
      name: 'Emergency Care',
      description: '24/7 emergency veterinary services for urgent medical needs',
      duration: 'Variable',
      price: '$150-500'
    }
  ],
  availability: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  pricing: [
    {
      name: 'Basic Care',
      price: '$80',
      description: 'Essential veterinary services',
      features: ['Health Checkup', 'Basic Consultation', 'Vaccination Records']
    },
    {
      name: 'Comprehensive Care',
      price: '$150',
      description: 'Complete health assessment',
      features: ['Full Health Checkup', 'Blood Work', 'Dental Examination', 'Nutrition Consultation']
    },
    {
      name: 'Premium Care',
      price: '$250',
      description: 'Advanced veterinary care',
      features: ['Comprehensive Exam', 'Advanced Diagnostics', 'Specialist Consultation', 'Follow-up Care']
    }
  ],
  gallery: [
    'https://images.unsplash.com/photo-1576201836106-db1758fd1c97?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-**********-2a8555f1a136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1628009368231-7bb7cfcb0def?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-**********-df5a28aab5c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ],
  reviews: [
    {
      id: '1',
      customerName: 'Sarah Johnson',
      rating: 5,
      comment: 'Dr. Chen is amazing! He took great care of my dog and explained everything clearly. Highly recommended!',
      date: '2024-07-20',
      service: 'Health Checkup'
    },
    {
      id: '2',
      customerName: 'Mike Davis',
      rating: 5,
      comment: 'Professional and caring service. The clinic is clean and the staff is very friendly.',
      date: '2024-07-15',
      service: 'Vaccinations'
    },
    {
      id: '3',
      customerName: 'Emily Wilson',
      rating: 4,
      comment: 'Great experience overall. Dr. Chen was very thorough and my cat felt comfortable.',
      date: '2024-07-10',
      service: 'Health Checkup'
    }
  ],
  verified: true,
  featured: true,
  responseTime: '< 2 hours',
  completionRate: 98,
  yearsExperience: 15,
  certifications: ['DVM - Doctor of Veterinary Medicine', 'ACVS - American College of Veterinary Surgeons', 'Fear Free Certified'],
  socialMedia: {
    facebook: 'https://facebook.com/drchenveterinary',
    instagram: 'https://instagram.com/drchenveterinary',
    twitter: 'https://twitter.com/drchenveterinary'
  },
  businessHours: {
    'Monday': '8:00 AM - 6:00 PM',
    'Tuesday': '8:00 AM - 6:00 PM',
    'Wednesday': '8:00 AM - 6:00 PM',
    'Thursday': '8:00 AM - 6:00 PM',
    'Friday': '8:00 AM - 6:00 PM',
    'Saturday': '9:00 AM - 4:00 PM',
    'Sunday': 'Closed'
  }
};

export default function ProviderProfile({ params }: { params: { id: string } }) {
  const [activeGalleryIndex, setActiveGalleryIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showAllReviews, setShowAllReviews] = useState(false);

  const provider = mockProvider;

  const nextGalleryImage = () => {
    setActiveGalleryIndex((prev) => (prev + 1) % provider.gallery.length);
  };

  const prevGalleryImage = () => {
    setActiveGalleryIndex((prev) => (prev - 1 + provider.gallery.length) % provider.gallery.length);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={i < Math.floor(rating) ? 'w-4 h-4 text-yellow-500 fill-current' : 'w-4 h-4 text-gray-300'}
      />
    ));
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Banner Section */}
        <div className="relative mb-8 rounded-2xl overflow-hidden">
          <div className="h-64 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 relative">
            <img
              src="https://images.unsplash.com/photo-1576201836106-db1758fd1c97?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
              alt="Veterinary clinic banner"
              className="w-full h-full object-cover opacity-80"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-gray-900 from-opacity-40 to-transparent"></div>

            {/* Provider Avatar and Basic Info Overlay */}
            <div className="absolute bottom-6 left-6 flex items-end gap-6">
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt={provider.name}
                  className="w-24 h-24 rounded-2xl border-4 border-white shadow-lg object-cover"
                />
                {provider.verified && (
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white">
                    <Verified className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>

              <div className="text-white mb-2">
                <h1 className="text-2xl font-bold mb-1">{provider.name}</h1>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    {renderStars(provider.rating)}
                    <span className="font-bold ml-1">{provider.rating}</span>
                    <span className="opacity-90">({provider.reviewCount} reviews)</span>
                  </div>
                  <span className="px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full text-sm font-medium">
                    {provider.type}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="absolute top-6 right-6 flex gap-3">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={
                  isFavorite
                    ? 'p-3 rounded-xl backdrop-blur-sm transition-colors duration-200 bg-red-500 bg-opacity-20 text-red-300'
                    : 'p-3 rounded-xl backdrop-blur-sm transition-colors duration-200 bg-white bg-opacity-20 text-white hover:bg-red-500 hover:bg-opacity-20 hover:text-red-300'
                }
              >
                <Heart className={isFavorite ? 'w-5 h-5 fill-current' : 'w-5 h-5'} />
              </button>
              <button className="p-3 bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 backdrop-blur-sm rounded-xl transition-colors duration-200">
                <Share2 className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="glass-card rounded-2xl p-8 mb-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Provider Info */}
            <div className="flex-1">
              <div className="mb-6">
                <p className="text-cool-700 mb-6 leading-relaxed text-lg">{provider.description}</p>
                  
                  {/* Key Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-4 bg-white bg-opacity-50 rounded-xl">
                      <Clock className="w-6 h-6 text-primary-500 mx-auto mb-2" />
                      <p className="text-sm text-cool-600">Response Time</p>
                      <p className="font-bold text-cool-800">{provider.responseTime}</p>
                    </div>
                    <div className="text-center p-4 bg-white bg-opacity-50 rounded-xl">
                      <CheckCircle className="w-6 h-6 text-green-500 mx-auto mb-2" />
                      <p className="text-sm text-cool-600">Completion Rate</p>
                      <p className="font-bold text-cool-800">{provider.completionRate}%</p>
                    </div>
                    <div className="text-center p-4 bg-white bg-opacity-50 rounded-xl">
                      <Briefcase className="w-6 h-6 text-secondary-500 mx-auto mb-2" />
                      <p className="text-sm text-cool-600">Experience</p>
                      <p className="font-bold text-cool-800">{provider.yearsExperience} years</p>
                    </div>
                    <div className="text-center p-4 bg-white bg-opacity-50 rounded-xl">
                      <Users className="w-6 h-6 text-accent-500 mx-auto mb-2" />
                      <p className="text-sm text-cool-600">Clients Served</p>
                      <p className="font-bold text-cool-800">500+</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Contact Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-3 p-4 bg-white bg-opacity-50 rounded-xl">
                  <MapPin className="w-5 h-5 text-primary-500" />
                  <div>
                    <p className="text-sm text-cool-600">Location</p>
                    <p className="font-medium text-cool-800">{provider.location}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 bg-white bg-opacity-50 rounded-xl">
                  <Phone className="w-5 h-5 text-secondary-500" />
                  <div>
                    <p className="text-sm text-cool-600">Phone</p>
                    <p className="font-medium text-cool-800">{provider.phone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 bg-white bg-opacity-50 rounded-xl">
                  <Mail className="w-5 h-5 text-accent-500" />
                  <div>
                    <p className="text-sm text-cool-600">Email</p>
                    <p className="font-medium text-cool-800">{provider.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-4 bg-white bg-opacity-50 rounded-xl">
                  <Globe className="w-5 h-5 text-warm-500" />
                  <div>
                    <p className="text-sm text-cool-600">Website</p>
                    <a href={provider.website} target="_blank" rel="noopener noreferrer" className="font-medium text-primary-600 hover:text-primary-700">
                      Visit Website
                    </a>
                  </div>
                </div>
              </div>
              
              {/* Action Buttons */}
              <div className="flex gap-4">
                <button className="flex-1 btn-primary">
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Appointment
                </button>
                <button className="btn-outline">
                  <MessageSquare className="w-5 h-5 mr-2" />
                  Send Message
                </button>
                <button className="btn-outline">
                  <Phone className="w-5 h-5 mr-2" />
                  Call Now
                </button>
              </div>
            </div>
            
            {/* Gallery */}
            <div className="lg:w-96">
              <div className="relative">
                <div className="aspect-square rounded-2xl overflow-hidden">
                  <img
                    src={provider.gallery[activeGalleryIndex]}
                    alt={provider.name + ' - Image ' + (activeGalleryIndex + 1)}
                    className="w-full h-full object-cover"
                  />
                </div>

                {provider.gallery.length > 1 && (
                  <>
                    <button
                      onClick={prevGalleryImage}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-black hover:bg-opacity-70 transition-colors duration-200"
                    >
                      <ChevronLeft className="w-5 h-5" />
                    </button>
                    <button
                      onClick={nextGalleryImage}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-black hover:bg-opacity-70 transition-colors duration-200"
                    >
                      <ChevronRight className="w-5 h-5" />
                    </button>

                    {/* Image Counter */}
                    <div className="absolute bottom-4 right-4 px-3 py-1 bg-black bg-opacity-50 text-white rounded-full text-sm">
                      {activeGalleryIndex + 1} of {provider.gallery.length}
                    </div>
                  </>
                )}
              </div>

              {/* Gallery Thumbnails */}
              {provider.gallery.length > 1 && (
                <div className="flex gap-2 mt-4 overflow-x-auto">
                  {provider.gallery.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveGalleryIndex(index)}
                      className={
                        activeGalleryIndex === index
                          ? 'flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden transition-all duration-200 ring-2 ring-primary-500 scale-105'
                          : 'flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden transition-all duration-200 opacity-70 hover:opacity-100'
                      }
                    >
                      <img
                        src={image}
                        alt={'Thumbnail ' + (index + 1)}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Services & Pricing */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Services */}
          <div className="glass-card rounded-2xl p-6">
            <h2 className="text-2xl font-bold text-cool-800 mb-6">Services Offered</h2>
            <div className="space-y-4">
              {provider.services.map((service) => (
                <div key={service.id} className="p-4 bg-white bg-opacity-50 rounded-xl">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-bold text-cool-800">{service.name}</h3>
                    <span className="font-bold text-primary-600">{service.price}</span>
                  </div>
                  <p className="text-cool-600 text-sm mb-2">{service.description}</p>
                  <div className="flex items-center gap-2 text-xs text-cool-500">
                    <Clock className="w-3 h-3" />
                    <span>{service.duration}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Business Hours */}
          <div className="glass-card rounded-2xl p-6">
            <h2 className="text-2xl font-bold text-cool-800 mb-6">Business Hours</h2>
            <div className="space-y-3">
              {Object.entries(provider.businessHours).map(([day, hours]) => (
                <div key={day} className="flex items-center justify-between p-3 bg-white bg-opacity-50 rounded-xl">
                  <span className="font-medium text-cool-800">{day}</span>
                  <span className="text-cool-600">{hours}</span>
                </div>
              ))}
            </div>
            
            {/* Certifications */}
            <div className="mt-6">
              <h3 className="font-bold text-cool-800 mb-4">Certifications</h3>
              <div className="space-y-2">
                {provider.certifications.map((cert, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-cool-700">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>{cert}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Social Media */}
            <div className="mt-6">
              <h3 className="font-bold text-cool-800 mb-4">Follow Us</h3>
              <div className="flex gap-3">
                {provider.socialMedia.facebook && (
                  <a href={provider.socialMedia.facebook} target="_blank" rel="noopener noreferrer" className="p-3 bg-blue-100 text-blue-600 rounded-xl hover:bg-blue-200 transition-colors duration-200">
                    <Facebook className="w-5 h-5" />
                  </a>
                )}
                {provider.socialMedia.instagram && (
                  <a href={provider.socialMedia.instagram} target="_blank" rel="noopener noreferrer" className="p-3 bg-pink-100 text-pink-600 rounded-xl hover:bg-pink-200 transition-colors duration-200">
                    <Instagram className="w-5 h-5" />
                  </a>
                )}
                {provider.socialMedia.twitter && (
                  <a href={provider.socialMedia.twitter} target="_blank" rel="noopener noreferrer" className="p-3 bg-blue-100 text-blue-600 rounded-xl hover:bg-blue-200 transition-colors duration-200">
                    <Twitter className="w-5 h-5" />
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Reviews Section */}
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-cool-800">Customer Reviews</h2>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                {renderStars(provider.rating)}
              </div>
              <span className="font-bold text-cool-800">{provider.rating}</span>
              <span className="text-cool-600">({provider.reviewCount} reviews)</span>
            </div>
          </div>
          
          <div className="space-y-4">
            {provider.reviews.slice(0, showAllReviews ? provider.reviews.length : 3).map((review) => (
              <div key={review.id} className="p-4 bg-white bg-opacity-50 rounded-xl">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {review.customerName.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-cool-800">{review.customerName}</p>
                      <p className="text-sm text-cool-600">{review.service} • {review.date}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    {renderStars(review.rating)}
                  </div>
                </div>
                <p className="text-cool-700">{review.comment}</p>
              </div>
            ))}
          </div>
          
          {provider.reviews.length > 3 && (
            <div className="text-center mt-6">
              <button
                onClick={() => setShowAllReviews(!showAllReviews)}
                className="btn-outline"
              >
                {showAllReviews ? 'Show Less' : `Show All ${provider.reviews.length} Reviews`}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
