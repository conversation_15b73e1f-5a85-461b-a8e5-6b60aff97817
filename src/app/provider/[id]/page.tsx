'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ProviderProfile({ params }: { params: { id: string } }) {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the public provider profile page
    // This provides a clean, public-facing view for pet owners
    router.replace(`/provider/public/${params.id}`);
  }, [params.id, router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading provider profile...</p>
      </div>
    </div>
  );
}


