'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Building2,
  User,
  Mail,
  Phone,
  MapPin,
  Clock,
  Camera,
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  ArrowLeft,
  Star,
  Shield,
  Award
} from 'lucide-react';

interface RegistrationData {
  // Basic Info
  businessName: string;
  ownerName: string;
  email: string;
  phone: string;
  serviceType: string;
  
  // Location
  address: string;
  city: string;
  state: string;
  zipCode: string;
  
  // Business Details
  description: string;
  experience: string;
  specialties: string[];
  
  // Hours
  businessHours: {
    [key: string]: { open: string; close: string; closed: boolean };
  };
  
  // Documents
  businessLicense: File | null;
  insurance: File | null;
  certifications: File[];
  
  // Photos
  businessPhotos: File[];
  profilePhoto: File | null;
}

const serviceTypes = [
  'Veterinary Care',
  'Pet Grooming',
  'Pet Hotel/Boarding',
  'Dog Training',
  'Pet Daycare',
  'Pet Sitting',
  'Dog Walking',
  'Pet Photography',
  'Pet Transportation',
  'Other'
];

const specialtyOptions = [
  'Small Dogs', 'Large Dogs', 'Cats', 'Exotic Pets', 'Senior Pets',
  'Puppies/Kittens', 'Aggressive Pets', 'Special Needs', 'Show Grooming',
  'Emergency Care', 'Surgery', 'Dental Care', 'Behavioral Training'
];

const daysOfWeek = [
  'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
];

export default function ProviderRegistration() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<RegistrationData>({
    businessName: '',
    ownerName: '',
    email: '',
    phone: '',
    serviceType: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    description: '',
    experience: '',
    specialties: [],
    businessHours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '09:00', close: '15:00', closed: false },
      sunday: { open: '09:00', close: '15:00', closed: true }
    },
    businessLicense: null,
    insurance: null,
    certifications: [],
    businessPhotos: [],
    profilePhoto: null
  });

  const totalSteps = 5;

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSpecialtyToggle = (specialty: string) => {
    setFormData(prev => ({
      ...prev,
      specialties: prev.specialties.includes(specialty)
        ? prev.specialties.filter(s => s !== specialty)
        : [...prev.specialties, specialty]
    }));
  };

  const handleHoursChange = (day: string, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      businessHours: {
        ...prev.businessHours,
        [day]: {
          ...prev.businessHours[day],
          [field]: value
        }
      }
    }));
  };

  const handleFileUpload = (field: string, files: FileList | null) => {
    if (!files) return;
    
    if (field === 'businessPhotos' || field === 'certifications') {
      setFormData(prev => ({
        ...prev,
        [field]: Array.from(files)
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: files[0]
      }));
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Here you would submit to Firebase/backend
    console.log('Registration data:', formData);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Redirect to success page or dashboard
    router.push('/provider/dashboard?registration=success');
  };

  const renderProgressBar = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold text-cool-800">Provider Registration</h1>
        <span className="text-sm text-cool-600">Step {currentStep} of {totalSteps}</span>
      </div>
      <div className="w-full bg-white/30 rounded-full h-2">
        <div 
          className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${(currentStep / totalSteps) * 100}%` }}
        ></div>
      </div>
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <Building2 className="w-16 h-16 text-primary-600 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-cool-800 mb-2">Basic Information</h2>
        <p className="text-cool-600">Tell us about your business</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">Business Name *</label>
          <input
            type="text"
            value={formData.businessName}
            onChange={(e) => handleInputChange('businessName', e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Happy Paws Grooming"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">Owner Name *</label>
          <input
            type="text"
            value={formData.ownerName}
            onChange={(e) => handleInputChange('ownerName', e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="John Smith"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">Email Address *</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">Phone Number *</label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="+****************"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-cool-700 mb-2">Service Type *</label>
        <select
          value={formData.serviceType}
          onChange={(e) => handleInputChange('serviceType', e.target.value)}
          className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        >
          <option value="">Select your primary service</option>
          {serviceTypes.map(type => (
            <option key={type} value={type}>{type}</option>
          ))}
        </select>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <MapPin className="w-16 h-16 text-primary-600 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-cool-800 mb-2">Location & Address</h2>
        <p className="text-cool-600">Where is your business located?</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-cool-700 mb-2">Street Address *</label>
        <input
          type="text"
          value={formData.address}
          onChange={(e) => handleInputChange('address', e.target.value)}
          className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="123 Main Street"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">City *</label>
          <input
            type="text"
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="San Francisco"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">State *</label>
          <input
            type="text"
            value={formData.state}
            onChange={(e) => handleInputChange('state', e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="CA"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">ZIP Code *</label>
          <input
            type="text"
            value={formData.zipCode}
            onChange={(e) => handleInputChange('zipCode', e.target.value)}
            className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="94102"
            required
          />
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <FileText className="w-16 h-16 text-primary-600 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-cool-800 mb-2">Business Details</h2>
        <p className="text-cool-600">Tell us more about your services</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-cool-700 mb-2">Business Description *</label>
        <textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={4}
          className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Describe your business, services, and what makes you special..."
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-cool-700 mb-2">Years of Experience *</label>
        <select
          value={formData.experience}
          onChange={(e) => handleInputChange('experience', e.target.value)}
          className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        >
          <option value="">Select experience level</option>
          <option value="less-than-1">Less than 1 year</option>
          <option value="1-3">1-3 years</option>
          <option value="3-5">3-5 years</option>
          <option value="5-10">5-10 years</option>
          <option value="10-plus">10+ years</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-cool-700 mb-2">Specialties</label>
        <p className="text-sm text-cool-600 mb-3">Select all that apply to your services</p>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {specialtyOptions.map(specialty => (
            <label key={specialty} className="flex items-center">
              <input
                type="checkbox"
                checked={formData.specialties.includes(specialty)}
                onChange={() => handleSpecialtyToggle(specialty)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-cool-700">{specialty}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <Clock className="w-16 h-16 text-primary-600 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-cool-800 mb-2">Business Hours</h2>
        <p className="text-cool-600">When are you available for appointments?</p>
      </div>

      <div className="space-y-4">
        {daysOfWeek.map(day => (
          <div key={day} className="flex items-center gap-4 p-4 bg-white/50 rounded-xl">
            <div className="w-24">
              <span className="font-medium text-cool-800 capitalize">{day}</span>
            </div>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={!formData.businessHours[day].closed}
                onChange={(e) => handleHoursChange(day, 'closed', !e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-cool-700">Open</span>
            </label>

            {!formData.businessHours[day].closed && (
              <>
                <input
                  type="time"
                  value={formData.businessHours[day].open}
                  onChange={(e) => handleHoursChange(day, 'open', e.target.value)}
                  className="px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                <span className="text-cool-600">to</span>
                <input
                  type="time"
                  value={formData.businessHours[day].close}
                  onChange={(e) => handleHoursChange(day, 'close', e.target.value)}
                  className="px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </>
            )}

            {formData.businessHours[day].closed && (
              <span className="text-cool-500 italic">Closed</span>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  const renderStep5 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <Upload className="w-16 h-16 text-primary-600 mx-auto mb-4" />
        <h2 className="text-xl font-bold text-cool-800 mb-2">Documents & Photos</h2>
        <p className="text-cool-600">Upload required documents and business photos</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">Business License *</label>
          <div className="border-2 border-dashed border-white/30 rounded-xl p-6 text-center">
            <Upload className="w-8 h-8 text-cool-400 mx-auto mb-2" />
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => handleFileUpload('businessLicense', e.target.files)}
              className="hidden"
              id="businessLicense"
            />
            <label htmlFor="businessLicense" className="cursor-pointer">
              <span className="text-sm text-cool-600">Click to upload business license</span>
            </label>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-cool-700 mb-2">Insurance Certificate *</label>
          <div className="border-2 border-dashed border-white/30 rounded-xl p-6 text-center">
            <Shield className="w-8 h-8 text-cool-400 mx-auto mb-2" />
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => handleFileUpload('insurance', e.target.files)}
              className="hidden"
              id="insurance"
            />
            <label htmlFor="insurance" className="cursor-pointer">
              <span className="text-sm text-cool-600">Click to upload insurance</span>
            </label>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-cool-700 mb-2">Certifications (Optional)</label>
        <div className="border-2 border-dashed border-white/30 rounded-xl p-6 text-center">
          <Award className="w-8 h-8 text-cool-400 mx-auto mb-2" />
          <input
            type="file"
            accept=".pdf,.jpg,.jpeg,.png"
            multiple
            onChange={(e) => handleFileUpload('certifications', e.target.files)}
            className="hidden"
            id="certifications"
          />
          <label htmlFor="certifications" className="cursor-pointer">
            <span className="text-sm text-cool-600">Click to upload certifications</span>
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-cool-700 mb-2">Business Photos</label>
        <div className="border-2 border-dashed border-white/30 rounded-xl p-6 text-center">
          <Camera className="w-8 h-8 text-cool-400 mx-auto mb-2" />
          <input
            type="file"
            accept=".jpg,.jpeg,.png"
            multiple
            onChange={(e) => handleFileUpload('businessPhotos', e.target.files)}
            className="hidden"
            id="businessPhotos"
          />
          <label htmlFor="businessPhotos" className="cursor-pointer">
            <span className="text-sm text-cool-600">Click to upload business photos</span>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="glass-card rounded-2xl p-8">
            {renderProgressBar()}

            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
            {currentStep === 5 && renderStep5()}
            
            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <button
                onClick={prevStep}
                disabled={currentStep === 1}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </button>
              
              {currentStep < totalSteps ? (
                <button onClick={nextStep} className="btn-primary">
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </button>
              ) : (
                <button onClick={handleSubmit} className="btn-primary">
                  Submit Registration
                  <CheckCircle className="w-4 h-4 ml-2" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
