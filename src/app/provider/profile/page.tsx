'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useProvider } from '@/contexts/ProviderContext';
import { Provider, Service } from '@/lib/firebase/providers';
import {
  MapPin, Phone, Mail, Globe, Award, Building, Calendar, Users, Star,
  Edit, Camera, Save, X, MessageCircle, UserPlus, UserCheck, Briefcase,
  Clock, CheckCircle, PawPrint, Heart, Share2, MoreHorizontal, Package,
  Upload, RotateCcw, Maximize2, Plus, Home, CreditCard, Shield, Search, Move, ZoomIn
} from 'lucide-react';
import { uploadImage, generateStoragePath, validateImageFile, compressImage } from '@/lib/storage';
import { collection, query, where, getDocs, addDoc, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { getProviderByUserId, getProviderServices } from '@/lib/firebase/providers';
import toast from 'react-hot-toast';
import PostCard from '@/components/PostCard';

export default function ProviderProfilePage() {
  const { user } = useAuth();
  const { provider, services, bookings, isLoading, error, updateProviderProfile, refreshProviderData } = useProvider();
  const router = useRouter();
  const searchParams = useSearchParams();
  const providerId = searchParams.get('id');
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState({ profile: false, banner: false });
  // We'll determine this based on the URL and user state
  const [viewingOwnProfile, setViewingOwnProfile] = useState(true);
  const [viewedProvider, setViewedProvider] = useState<Provider | null>(null);
  const [viewedProviderServices, setViewedProviderServices] = useState<Service[]>([]);
  const [providerPosts, setProviderPosts] = useState<any[]>([]);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isFriend, setIsFriend] = useState(false);

  // Image upload states
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showBannerModal, setShowBannerModal] = useState(false);
  const [bannerFit, setBannerFit] = useState<'cover' | 'contain' | 'fill'>('cover');
  const [bannerPosition, setBannerPosition] = useState({ x: 50, y: 50 });
  const [isDragging, setIsDragging] = useState(false);

  // Profile data state with Fetchly defaults
  const [profileData, setProfileData] = useState<{
    businessName: string;
    ownerName: string;
    description: string;
    profilePhoto: string;
    bannerPhoto: string;
    email: string;
    phone: string;
    website: string;
    serviceType: string;
    experience: string;
    specialties: string[];
    address: string;
    city: string;
    state: string;
    zipCode: string;
    businessHours: {
      monday: { open: string; close: string; closed: boolean };
      tuesday: { open: string; close: string; closed: boolean };
      wednesday: { open: string; close: string; closed: boolean };
      thursday: { open: string; close: string; closed: boolean };
      friday: { open: string; close: string; closed: boolean };
      saturday: { open: string; close: string; closed: boolean };
      sunday: { open: string; close: string; closed: boolean };
    };
    status: 'pending' | 'approved' | 'rejected' | 'suspended';
  }>({
    businessName: '',
    ownerName: '',
    description: '',
    profilePhoto: '/favicon.png', // Fetchly favicon as default
    bannerPhoto: '/fetchlylogo.png', // Fetchly logo as default banner
    email: '',
    phone: '',
    website: '',
    serviceType: '',
    experience: '',
    specialties: [] as string[],
    address: '',
    city: '',
    state: '',
    zipCode: '',
    businessHours: {
      monday: { open: '09:00', close: '17:00', closed: false },
      tuesday: { open: '09:00', close: '17:00', closed: false },
      wednesday: { open: '09:00', close: '17:00', closed: false },
      thursday: { open: '09:00', close: '17:00', closed: false },
      friday: { open: '09:00', close: '17:00', closed: false },
      saturday: { open: '10:00', close: '16:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true }
    },
    status: 'pending' as const
  });

  // Determine which provider to display
  const currentProvider = providerId && providerId !== user?.id ? viewedProvider : provider;
  // Determine which services to display
  const currentServices = viewingOwnProfile ? services : viewedProviderServices;

  // Load provider data and posts
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        if (providerId && providerId !== user?.id) {
          // Viewing someone else's profile - providerId is actually the userId
          setViewingOwnProfile(false);
          console.log('🔍 Loading provider profile for userId:', providerId);

          try {
            // Get provider by userId (since the URL contains the user ID, not provider document ID)
            console.log('🔍 Attempting to load provider with userId:', providerId);
            const providerData = await getProviderByUserId(providerId);
            console.log('📋 Provider data loaded:', providerData);

            if (providerData) {
              setViewedProvider(providerData);
              console.log('✅ Successfully set viewed provider:', providerData.businessName);

              // Also load their services
              try {
                console.log('🛠️ Loading services for provider ID:', providerData.id);
                const providerServices = await getProviderServices(providerData.id!);
                console.log('🛠️ Provider services loaded:', providerServices);
                setViewedProviderServices(providerServices);
              } catch (servicesError) {
                console.error('⚠️ Error loading provider services:', servicesError);
                // Continue without services - not critical
                setViewedProviderServices([]);
                toast.error('Could not load provider services');
              }

            setProfileData({
              businessName: providerData.businessName || '',
              ownerName: providerData.ownerName || '',
              description: providerData.description || '',
              profilePhoto: providerData.profilePhoto || '/favicon.png',
              bannerPhoto: providerData.bannerPhoto || '/fetchlylogo.png',
              email: providerData.email || '',
              phone: providerData.phone || '',
              website: providerData.website || '',
              serviceType: providerData.serviceType || '',
              experience: providerData.experience || '',
              specialties: providerData.specialties || [],
              address: providerData.address || '',
              city: providerData.city || '',
              state: providerData.state || '',
              zipCode: providerData.zipCode || '',
              businessHours: (providerData.businessHours as any) || profileData.businessHours,
              status: providerData.status || 'pending'
            });
            } else {
              console.log('❌ No provider data found for userId:', providerId);
              console.log('🔍 This could mean:');
              console.log('  1. The user is not a provider');
              console.log('  2. The provider profile hasn\'t been created yet');
              console.log('  3. The userId is incorrect');
              toast.error('Provider profile not found. This user may not be a provider or hasn\'t set up their profile yet.');
            }
          } catch (providerError) {
            console.error('❌ Error loading provider data:', providerError);
            console.error('❌ Full error details:', providerError);
            toast.error(`Failed to load provider profile: ${(providerError as any)?.message || 'Unknown error'}`);
          }
        } else {
          // Own profile - load data or show defaults for setup
          setViewingOwnProfile(true);

          console.log('Loading own profile. Provider data:', provider);
          console.log('User data:', user);

          // Always try to fetch fresh data from Firebase first
          let currentProviderData = provider;
          if (user?.id) {
            try {
              console.log('🔄 Fetching fresh provider data from Firebase...');
              const freshProviderData = await getProviderByUserId(user.id);
              if (freshProviderData) {
                currentProviderData = freshProviderData;
                console.log('✅ Fresh provider data loaded:', {
                  id: freshProviderData.id,
                  businessName: freshProviderData.businessName,
                  profilePhoto: freshProviderData.profilePhoto,
                  bannerPhoto: freshProviderData.bannerPhoto
                });
              } else {
                console.log('⚠️ No provider data found in Firebase');
              }
            } catch (error) {
              console.error('❌ Error fetching fresh provider data:', error);
              // Continue with context data if available
            }
          }

          if (currentProviderData) {
            console.log('Setting profile data from provider:', {
              businessName: currentProviderData.businessName,
              description: currentProviderData.description,
              profilePhoto: currentProviderData.profilePhoto,
              bannerPhoto: currentProviderData.bannerPhoto,
              phone: currentProviderData.phone,
              website: currentProviderData.website,
              experience: currentProviderData.experience,
              address: currentProviderData.address,
              city: currentProviderData.city,
              state: currentProviderData.state,
              zipCode: currentProviderData.zipCode
            });

            setProfileData({
              businessName: currentProviderData.businessName || '',
              ownerName: currentProviderData.ownerName || user?.name || '',
              description: currentProviderData.description || '',
              profilePhoto: currentProviderData.profilePhoto || '/favicon.png',
              bannerPhoto: currentProviderData.bannerPhoto || '/fetchlylogo.png',
              email: currentProviderData.email || user?.email || '',
              phone: currentProviderData.phone || '',
              website: currentProviderData.website || '',
              serviceType: currentProviderData.serviceType || '',
              experience: currentProviderData.experience || '',
              specialties: currentProviderData.specialties || [],
              address: currentProviderData.address || '',
              city: currentProviderData.city || user?.city || '',
              state: currentProviderData.state || '',
              zipCode: currentProviderData.zipCode || '',
              businessHours: (currentProviderData.businessHours as any) || profileData.businessHours,
              status: currentProviderData.status || 'pending'
            });
          } else {
            console.log('No provider data found, using defaults');
            // No provider data exists - show defaults with user info
            setProfileData(prev => ({
              ...prev,
              ownerName: user?.name || '',
              email: user?.email || '',
              city: user?.city || ''
            }));
          }
        }

        // Load posts
        await loadProviderPosts();

      } catch (error) {
        console.error('Error loading provider data:', error);
        toast.error('Failed to load provider profile');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadData();
    }
  }, [user, provider, providerId]);

  const loadProviderPosts = async () => {
    try {
      // For posts, we need to use the user ID, not the provider document ID
      let targetUserId;

      if (providerId && providerId !== user?.id) {
        // Viewing someone else's profile - providerId is the user ID
        targetUserId = providerId;
      } else {
        // Own profile - use current user ID
        targetUserId = user?.id;
      }

      if (!targetUserId) {
        console.log('No target user ID for loading posts');
        return;
      }

      console.log('Loading posts for user ID:', targetUserId);

      const postsQuery = query(
        collection(db, 'posts'),
        where('userId', '==', targetUserId),
        where('isPublic', '==', true)
      );

      const postsSnapshot = await getDocs(postsQuery);
      const posts = postsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log('Loaded posts:', posts.length);
      setProviderPosts(posts);
    } catch (error) {
      console.error('Error loading provider posts:', error);
      // Don't show error toast for posts as it's not critical
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };

  // Handle avatar upload
  const handleAvatarUpload = async (file: File) => {
    if (!user) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid image file');
      return;
    }

    try {
      setSaving(true);

      // Compress image
      const compressedFile = await compressImage(file, 400, 400, 0.8);

      // Generate storage path
      const path = generateStoragePath(user.id, 'provider-avatar', file.name);

      // Upload image
      const result = await uploadImage(compressedFile, path, (progress) => {
        setUploadProgress(progress.progress);
      });

      if (result.success && result.url) {
        setProfileData({ ...profileData, profilePhoto: result.url });
        setUploadProgress(0);
        toast.success('Profile picture updated!');
      } else {
        toast.error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Upload failed. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  // Handle banner upload
  const handleBannerUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid image file');
      return;
    }

    try {
      setSaving(true);

      // Compress image for banner (larger dimensions)
      const compressedFile = await compressImage(file, 1200, 400, 0.8);

      // Generate storage path
      const path = generateStoragePath(user.id, 'provider-banner', file.name);

      // Upload image
      const result = await uploadImage(compressedFile, path, (progress) => {
        setUploadProgress(progress.progress);
      });

      if (result.success && result.url) {
        setProfileData({ ...profileData, bannerPhoto: result.url });
        setUploadProgress(0);
        toast.success('Banner updated!');
      } else {
        toast.error(result.error || 'Banner upload failed');
      }
    } catch (error) {
      console.error('Error uploading banner:', error);
      toast.error('Banner upload failed. Please try again.');
    } finally {
      setSaving(false);
      setUploadProgress(0);
    }
  };

  const handleSaveProfile = async () => {
    if (!user?.id) return;

    try {
      setSaving(true);

      // Validate required fields
      if (!profileData.businessName || !profileData.email) {
        const errorMessage = 'Please fill in Business Name and Email';
        toast.error(errorMessage);
        return;
      }

      console.log('Saving profile data:', profileData);

      // Prepare data for Provider interface
      const providerData: Partial<Provider> = {
        ...profileData,
        userId: user.id,
        // Ensure required fields have defaults
        businessPhotos: [],
        rating: provider?.rating || 0,
        reviewCount: provider?.reviewCount || 0,
        totalBookings: provider?.totalBookings || 0,
        totalRevenue: provider?.totalRevenue || 0,
        completionRate: provider?.completionRate || 0,
        responseTime: provider?.responseTime || '< 1 hour',
        responseRate: provider?.responseRate || 100,
        membershipTier: provider?.membershipTier || 'free',
        fetchPoints: provider?.fetchPoints || 0,
        commissionsaved: provider?.commissionsaved || 0,
        socialMedia: provider?.socialMedia || {},
        settings: provider?.settings || {
          emailNotifications: true,
          smsNotifications: true,
          bookingNotifications: true,
          marketingEmails: false,
          autoAcceptBookings: false,
          requireDeposit: false,
          cancellationPolicy: 'Standard'
        },
        verified: provider?.verified || false,
        featured: provider?.featured || false
      };

      console.log('Updating provider with data:', providerData);
      await updateProviderProfile(providerData);

      // Force refresh the provider data to ensure it's loaded
      console.log('Refreshing provider data after save...');
      await refreshProviderData();

      const successMessage = 'Profile updated successfully!';
      toast.success(successMessage);
      setEditing({ profile: false, banner: false });
    } catch (error) {
      console.error('Error saving profile:', error);
      const errorMessage = 'Failed to update profile';
      toast.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleFollowProvider = async () => {
    try {
      if (!user || !currentProvider) return;
      
      await addDoc(collection(db, 'follows'), {
        followerId: user.id,
        followingId: currentProvider.id,
        createdAt: new Date()
      });
      
      setIsFollowing(true);
      toast.success('Following provider!');
    } catch (error) {
      console.error('Error following provider:', error);
      toast.error('Failed to follow provider');
    }
  };

  const handleAddFriend = async () => {
    try {
      if (!user || !currentProvider) return;

      await addDoc(collection(db, 'friendRequests'), {
        fromUserId: user.id,
        toUserId: currentProvider.userId,
        status: 'pending',
        createdAt: new Date()
      });

      toast.success('Friend request sent!');
    } catch (error) {
      console.error('Error sending friend request:', error);
      toast.error('Failed to send friend request');
    }
  };

  const handleBookService = (service: Service) => {
    // For now, show a simple alert. In a full implementation, this would open a booking modal
    const message = `Booking functionality for "${service.name}" coming soon! Contact the provider directly for now.`;
    toast.success(message);

    // TODO: Implement full booking modal with:
    // - Date/time selection
    // - Pet information
    // - Special requests
    // - Payment processing
    // - Booking confirmation
  };

  // Handle authentication
  useEffect(() => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }
  }, [user, router]);

  // Check if we're viewing own profile or someone else's
  const isOwnProfile = !providerId || providerId === user?.id;

  if (isLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading provider profile...</p>
        </div>
      </div>
    );
  }

  // If viewing someone else's profile and no data, show not found
  if (!currentProvider && !viewingOwnProfile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-6 max-w-md">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-4">
            <h2 className="text-lg font-semibold text-yellow-800 mb-2">Provider Profile Not Found</h2>
            <p className="text-yellow-600 mb-4">
              This provider profile doesn't exist or hasn't been set up yet.
            </p>
            <button
              onClick={() => router.push('/community')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Community
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Provider Profile</h1>
          {isOwnProfile && (
            <button
              onClick={() => router.push('/provider/dashboard')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Go to Dashboard
            </button>
          )}
        </div>

        {/* Profile Header - Matching Pet Owner Design */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
          <div className="relative w-full h-96 bg-gradient-to-r from-blue-500 to-purple-600">
            {profileData.bannerPhoto ? (
              <div className="relative w-full h-full">
                <img
                  src={profileData.bannerPhoto}
                  alt="Business banner"
                  className={`w-full h-full cursor-pointer transition-all duration-300 ${
                    bannerFit === 'cover' ? 'object-cover' :
                    bannerFit === 'contain' ? 'object-contain' : 'object-fill'
                  }`}
                  style={{
                    objectPosition: `${bannerPosition.x}% ${bannerPosition.y}%`,
                    transform: isDragging ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onClick={() => setShowBannerModal(true)}
                  onMouseDown={(e) => {
                    if (editing.profile) {
                      setIsDragging(true);
                      const rect = e.currentTarget.getBoundingClientRect();
                      const x = ((e.clientX - rect.left) / rect.width) * 100;
                      const y = ((e.clientY - rect.top) / rect.height) * 100;
                      setBannerPosition({ x: Math.max(0, Math.min(100, x)), y: Math.max(0, Math.min(100, y)) });
                    }
                  }}
                  onMouseUp={() => setIsDragging(false)}
                  onMouseLeave={() => setIsDragging(false)}
                />

                {/* Banner Controls */}
                {editing.profile && (
                  <div className="absolute top-4 left-4 flex gap-2">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md">
                      <select
                        value={bannerFit}
                        onChange={(e) => setBannerFit(e.target.value as 'cover' | 'contain' | 'fill')}
                        className="text-sm border-none bg-transparent focus:outline-none"
                      >
                        <option value="cover">Fit to Cover</option>
                        <option value="contain">Fit to Screen</option>
                        <option value="fill">Fill Container</option>
                      </select>
                    </div>
                    <button
                      onClick={() => setBannerPosition({ x: 50, y: 50 })}
                      className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md hover:bg-white transition-colors"
                      title="Reset Position"
                    >
                      <RotateCcw className="w-4 h-4 text-gray-600" />
                    </button>
                    <button
                      onClick={() => setShowBannerModal(true)}
                      className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-md hover:bg-white transition-colors"
                      title="View Full Image"
                    >
                      <Maximize2 className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            )}

            {/* Upload Button */}
            {editing.profile && (
              <label className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-lg p-2 shadow-md hover:bg-opacity-100 cursor-pointer">
                <Upload className="w-5 h-5 text-gray-600" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleBannerUpload}
                  className="hidden"
                />
              </label>
            )}

            {/* Position Indicator */}
            {editing.profile && profileData.bannerPhoto && (
              <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 text-xs text-gray-600">
                Position: {Math.round(bannerPosition.x)}%, {Math.round(bannerPosition.y)}%
              </div>
            )}
          </div>

          <div className="relative px-6 pb-6">
            <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
              {/* Profile Picture */}
              <div className="relative -mt-16 mb-4 sm:mb-0">
                <img
                  src={profileData.profilePhoto || '/favicon.png'}
                  alt={profileData.businessName || 'Business Profile'}
                  className="w-32 h-32 rounded-full border-4 border-white shadow-lg object-cover"
                />
                {isOwnProfile && (
                  <label className="absolute bottom-2 right-2 bg-gray-100 rounded-full p-2 shadow-md hover:bg-gray-200 cursor-pointer">
                    <Camera className="w-4 h-4 text-gray-600" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleAvatarUpload(file);
                      }}
                      className="hidden"
                    />
                  </label>
                )}
                {uploadProgress > 0 && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full">
                    <div className="text-white text-sm font-medium">{uploadProgress}%</div>
                  </div>
                )}
              </div>

              {/* Business Information */}
              <div className="flex-1">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                  <div className="mb-4 sm:mb-0">
                    {editing.profile ? (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={profileData.businessName}
                          onChange={handleInputChange}
                          name="businessName"
                          className="text-2xl font-bold text-gray-900 bg-transparent border-b-2 border-blue-500 focus:outline-none w-full"
                          placeholder="Business Name"
                        />
                        <input
                          type="text"
                          value={profileData.ownerName}
                          onChange={handleInputChange}
                          name="ownerName"
                          className="text-lg text-gray-600 bg-transparent border-b border-gray-300 focus:outline-none w-full"
                          placeholder="Owner Name"
                        />
                        <select
                          value={profileData.serviceType}
                          onChange={handleInputChange}
                          name="serviceType"
                          className="text-sm text-gray-500 bg-transparent border-b border-gray-300 focus:outline-none w-full"
                        >
                          <option value="">Select Service Type</option>
                          <option value="Dog Walking">Dog Walking</option>
                          <option value="Pet Sitting">Pet Sitting</option>
                          <option value="Pet Grooming">Pet Grooming</option>
                          <option value="Veterinary">Veterinary</option>
                          <option value="Pet Training">Pet Training</option>
                          <option value="Pet Boarding">Pet Boarding</option>
                        </select>
                        <div className="grid grid-cols-2 gap-2">
                          <input
                            type="text"
                            value={profileData.city}
                            onChange={handleInputChange}
                            name="city"
                            className="text-sm text-gray-500 bg-transparent border-b border-gray-300 focus:outline-none"
                            placeholder="City"
                          />
                          <input
                            type="text"
                            value={profileData.state}
                            onChange={handleInputChange}
                            name="state"
                            className="text-sm text-gray-500 bg-transparent border-b border-gray-300 focus:outline-none"
                            placeholder="State"
                          />
                        </div>
                      </div>
                    ) : (
                      <div>
                        <h1 className="text-2xl font-bold text-gray-900">
                          {profileData.businessName || 'Your Business Name'}
                        </h1>
                        <p className="text-lg text-gray-600">
                          {profileData.ownerName || 'Owner Name'}
                        </p>
                        <div className="flex items-center space-x-1 mt-1">
                          <Briefcase className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-500">
                            {profileData.serviceType || 'Pet Service Provider'}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <MapPin className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-500">
                            {profileData.city && profileData.state
                              ? `${profileData.city}, ${profileData.state}`
                              : 'Location not set'
                            }
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {isOwnProfile ? (
                      <>
                        {editing.profile ? (
                          <>
                            <button
                              onClick={handleSaveProfile}
                              disabled={saving}
                              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2"
                            >
                              <Save className="w-4 h-4" />
                              <span>{saving ? 'Saving...' : 'Save Profile'}</span>
                            </button>
                            <button
                              onClick={() => setEditing({ profile: false, banner: false })}
                              className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 flex items-center justify-center space-x-2"
                            >
                              <X className="w-4 h-4" />
                              <span>Cancel</span>
                            </button>
                          </>
                        ) : (
                          <button
                            onClick={() => setEditing({ profile: true, banner: false })}
                            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2"
                          >
                            <Edit className="w-4 h-4" />
                            <span>Edit Profile</span>
                          </button>
                        )}
                      </>
                    ) : (
                      <>
                        <button
                          onClick={handleFollowProvider}
                          className={`px-6 py-2 rounded-lg flex items-center justify-center space-x-2 ${
                            isFollowing
                              ? 'bg-green-600 text-white'
                              : 'bg-blue-600 text-white hover:bg-blue-700'
                          }`}
                        >
                          {isFollowing ? <UserCheck className="w-4 h-4" /> : <UserPlus className="w-4 h-4" />}
                          <span>{isFollowing ? 'Following' : 'Follow'}</span>
                        </button>
                        <button
                          onClick={handleAddFriend}
                          className="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 flex items-center justify-center space-x-2"
                        >
                          <UserPlus className="w-4 h-4" />
                          <span>Add Friend</span>
                        </button>
                        <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 flex items-center justify-center space-x-2">
                          <MessageCircle className="w-4 h-4" />
                          <span>Message</span>
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Sidebar - Business Details */}
          <div className="lg:col-span-1 space-y-6">
            {/* Business Contact Info */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Contact Information</h2>
              {editing.profile ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={profileData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="(*************"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                    <input
                      type="url"
                      name="website"
                      value={profileData.website}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="https://yourbusiness.com"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                    <input
                      type="text"
                      name="experience"
                      value={profileData.experience}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="5 years"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {profileData.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-600">{profileData.email}</span>
                    </div>
                  )}
                  {profileData.phone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-600">{profileData.phone}</span>
                    </div>
                  )}
                  {profileData.website && (
                    <div className="flex items-center space-x-3">
                      <Globe className="w-5 h-5 text-gray-400" />
                      <a href={profileData.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        Visit Website
                      </a>
                    </div>
                  )}
                  {profileData.experience && (
                    <div className="flex items-center space-x-3">
                      <Award className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-600">{profileData.experience} experience</span>
                    </div>
                  )}
                  {!profileData.email && !profileData.phone && !profileData.website && !profileData.experience && (
                    <p className="text-gray-500 text-center py-4">No contact information available</p>
                  )}
                </div>
              )}
            </div>

            {/* Business Description */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">About</h2>
              {editing.profile ? (
                <textarea
                  name="description"
                  value={profileData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Tell clients about your business and services..."
                />
              ) : (
                <p className="text-gray-600">
                  {profileData.description || 'No description available yet.'}
                </p>
              )}
            </div>
          </div>

          {/* Right Content - Services and Posts */}
          <div className="lg:col-span-2 space-y-6">
            {/* Services Section */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Services Offered</h2>
                {isOwnProfile && (
                  <button
                    onClick={() => router.push('/provider/dashboard?tab=services')}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Service</span>
                  </button>
                )}
              </div>

              {currentServices && currentServices.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentServices.map((service) => (
                    <div key={service.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <h3 className="font-semibold text-gray-900">{service.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                      <div className="flex items-center justify-between mt-3">
                        <span className="text-lg font-bold text-blue-600">${service.price}</span>
                        <span className="text-sm text-gray-500">{service.duration} min</span>
                      </div>
                      {service.category && (
                        <div className="mt-2">
                          <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                            {service.category}
                          </span>
                        </div>
                      )}
                      {!isOwnProfile && (
                        <div className="mt-3">
                          <button
                            onClick={() => handleBookService(service)}
                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                          >
                            Book This Service
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No services yet</h3>
                  <p className="text-gray-500 mb-4">
                    {isOwnProfile
                      ? 'Add your first service to start attracting clients'
                      : 'This provider hasn\'t added any services yet'
                    }
                  </p>
                  {isOwnProfile && (
                    <button
                      onClick={() => router.push('/provider/dashboard?tab=services')}
                      className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                    >
                      Add Your First Service
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Posts Section */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Recent Posts</h2>
                {isOwnProfile && (
                  <button
                    onClick={() => router.push('/profile')}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    Manage Posts
                  </button>
                )}
              </div>

              <div className="space-y-6">
                {providerPosts.length > 0 ? (
                  providerPosts.map((post) => (
                    <PostCard
                      key={post.id}
                      post={post}
                      showPrivacyIndicator={isOwnProfile}
                    />
                  ))
                ) : (
                  <div className="text-center py-12">
                    <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                    <p className="text-gray-500 mb-4">
                      {isOwnProfile
                        ? 'Share updates about your business and connect with pet owners'
                        : 'This provider hasn\'t shared any posts yet'
                      }
                    </p>
                    {isOwnProfile && (
                      <button
                        onClick={() => router.push('/profile')}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                      >
                        Create Your First Post
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Banner Modal */}
        {showBannerModal && profileData.bannerPhoto && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="relative max-w-4xl max-h-full">
              <img
                src={profileData.bannerPhoto}
                alt="Business banner"
                className="max-w-full max-h-full object-contain"
              />
              <button
                onClick={() => setShowBannerModal(false)}
                className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-full p-2 hover:bg-opacity-100"
              >
                <X className="w-6 h-6 text-gray-600" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
