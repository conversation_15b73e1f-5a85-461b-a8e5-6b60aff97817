'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Users,
  Star,
  Download,
  Filter,
  RefreshCw,
  Eye,
  PieChart,
  LineChart,
  Activity,
  Target,
  Award,
  Clock,
  MapPin,
  Phone,
  Mail,
  Heart,
  Zap,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';

interface AnalyticsData {
  revenue: {
    current: number;
    previous: number;
    change: number;
    trend: 'up' | 'down' | 'stable';
  };
  bookings: {
    current: number;
    previous: number;
    change: number;
    trend: 'up' | 'down' | 'stable';
  };
  customers: {
    current: number;
    previous: number;
    change: number;
    trend: 'up' | 'down' | 'stable';
  };
  rating: {
    current: number;
    previous: number;
    change: number;
    trend: 'up' | 'down' | 'stable';
  };
}

interface ServicePerformance {
  name: string;
  bookings: number;
  revenue: number;
  avgRating: number;
  growth: number;
}

interface MonthlyData {
  month: string;
  revenue: number;
  bookings: number;
  customers: number;
}

const mockAnalytics: AnalyticsData = {
  revenue: { current: 8450, previous: 7520, change: 12.4, trend: 'up' },
  bookings: { current: 127, previous: 117, change: 8.5, trend: 'up' },
  customers: { current: 89, previous: 77, change: 15.6, trend: 'up' },
  rating: { current: 4.8, previous: 4.6, change: 4.3, trend: 'up' }
};

const mockServicePerformance: ServicePerformance[] = [
  { name: 'Full Service Grooming', bookings: 45, revenue: 3825, avgRating: 4.8, growth: 15.2 },
  { name: 'Veterinary Checkup', bookings: 28, revenue: 3360, avgRating: 4.9, growth: 8.7 },
  { name: 'Basic Grooming', bookings: 32, revenue: 1760, avgRating: 4.6, growth: 12.1 },
  { name: 'Dental Cleaning', bookings: 18, revenue: 2700, avgRating: 4.7, growth: -2.3 },
  { name: 'Pet Sitting', bookings: 12, revenue: 540, avgRating: 4.5, growth: -15.8 }
];

const mockMonthlyData: MonthlyData[] = [
  { month: 'Jul', revenue: 6200, bookings: 95, customers: 65 },
  { month: 'Aug', revenue: 6800, bookings: 102, customers: 68 },
  { month: 'Sep', revenue: 7200, bookings: 108, customers: 72 },
  { month: 'Oct', revenue: 7520, bookings: 117, customers: 77 },
  { month: 'Nov', revenue: 8100, bookings: 122, customers: 82 },
  { month: 'Dec', revenue: 8450, bookings: 127, customers: 89 }
];

export default function AnalyticsPage() {
  const { user } = useAuth();
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <ArrowUp className="w-4 h-4 text-green-500" />;
      case 'down': return <ArrowDown className="w-4 h-4 text-red-500" />;
      default: return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-500';
      case 'down': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Analytics & Reports</h1>
            <p className="text-cool-600">Track your business performance and growth</p>
          </div>
          
          <div className="flex items-center gap-4">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            <button className="btn-secondary">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
            <button className="btn-primary">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-primary-100 rounded-xl">
                <DollarSign className="w-6 h-6 text-primary-600" />
              </div>
              <div className="flex items-center gap-1">
                {getTrendIcon(mockAnalytics.revenue.trend)}
                <span className={`text-sm font-medium ${getTrendColor(mockAnalytics.revenue.trend)}`}>
                  {mockAnalytics.revenue.change > 0 ? '+' : ''}{mockAnalytics.revenue.change}%
                </span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">${mockAnalytics.revenue.current.toLocaleString()}</h3>
            <p className="text-cool-600 text-sm">Total Revenue</p>
            <p className="text-xs text-cool-500 mt-1">vs ${mockAnalytics.revenue.previous.toLocaleString()} last period</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-secondary-100 rounded-xl">
                <Calendar className="w-6 h-6 text-secondary-600" />
              </div>
              <div className="flex items-center gap-1">
                {getTrendIcon(mockAnalytics.bookings.trend)}
                <span className={`text-sm font-medium ${getTrendColor(mockAnalytics.bookings.trend)}`}>
                  {mockAnalytics.bookings.change > 0 ? '+' : ''}{mockAnalytics.bookings.change}%
                </span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{mockAnalytics.bookings.current}</h3>
            <p className="text-cool-600 text-sm">Total Bookings</p>
            <p className="text-xs text-cool-500 mt-1">vs {mockAnalytics.bookings.previous} last period</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-accent-100 rounded-xl">
                <Users className="w-6 h-6 text-accent-600" />
              </div>
              <div className="flex items-center gap-1">
                {getTrendIcon(mockAnalytics.customers.trend)}
                <span className={`text-sm font-medium ${getTrendColor(mockAnalytics.customers.trend)}`}>
                  {mockAnalytics.customers.change > 0 ? '+' : ''}{mockAnalytics.customers.change}%
                </span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{mockAnalytics.customers.current}</h3>
            <p className="text-cool-600 text-sm">Active Customers</p>
            <p className="text-xs text-cool-500 mt-1">vs {mockAnalytics.customers.previous} last period</p>
          </div>

          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-warm-100 rounded-xl">
                <Star className="w-6 h-6 text-warm-600" />
              </div>
              <div className="flex items-center gap-1">
                {getTrendIcon(mockAnalytics.rating.trend)}
                <span className={`text-sm font-medium ${getTrendColor(mockAnalytics.rating.trend)}`}>
                  {mockAnalytics.rating.change > 0 ? '+' : ''}{mockAnalytics.rating.change}%
                </span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">{mockAnalytics.rating.current}</h3>
            <p className="text-cool-600 text-sm">Average Rating</p>
            <p className="text-xs text-cool-500 mt-1">vs {mockAnalytics.rating.previous} last period</p>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Revenue Trend Chart */}
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-cool-800">Revenue Trend</h3>
              <div className="flex gap-2">
                <button
                  onClick={() => setSelectedMetric('revenue')}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    selectedMetric === 'revenue' ? 'bg-primary-500 text-white' : 'bg-white/50 text-cool-600'
                  }`}
                >
                  Revenue
                </button>
                <button
                  onClick={() => setSelectedMetric('bookings')}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    selectedMetric === 'bookings' ? 'bg-primary-500 text-white' : 'bg-white/50 text-cool-600'
                  }`}
                >
                  Bookings
                </button>
              </div>
            </div>
            <div className="h-64 flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg">
              <div className="text-center">
                <LineChart className="w-16 h-16 text-cool-400 mx-auto mb-4" />
                <p className="text-cool-600">Interactive chart visualization</p>
                <p className="text-sm text-cool-500">Chart.js integration coming soon</p>
              </div>
            </div>
          </div>

          {/* Service Distribution */}
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-cool-800">Service Distribution</h3>
              <button className="p-2 hover:bg-white/50 rounded-lg transition-colors duration-200">
                <Eye className="w-4 h-4 text-cool-600" />
              </button>
            </div>
            <div className="h-64 flex items-center justify-center bg-gradient-to-br from-secondary-50 to-accent-50 rounded-lg">
              <div className="text-center">
                <PieChart className="w-16 h-16 text-cool-400 mx-auto mb-4" />
                <p className="text-cool-600">Service breakdown chart</p>
                <p className="text-sm text-cool-500">Chart.js integration coming soon</p>
              </div>
            </div>
          </div>
        </div>

        {/* Service Performance Table */}
        <div className="glass-card rounded-xl mb-8">
          <div className="p-6 border-b border-white/20">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-cool-800">Service Performance</h3>
              <button className="btn-secondary">
                <Filter className="w-4 h-4 mr-2" />
                Filter Services
              </button>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Service</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Bookings</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Revenue</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Avg Rating</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Growth</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Performance</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/20">
                {mockServicePerformance.map((service, index) => (
                  <tr key={index} className="hover:bg-white/30 transition-colors duration-200">
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                          <Activity className="w-4 h-4 text-primary-600" />
                        </div>
                        <span className="font-medium text-cool-800">{service.name}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-cool-700">{service.bookings}</span>
                    </td>
                    <td className="px-6 py-4">
                      <span className="font-bold text-cool-800">${service.revenue.toLocaleString()}</span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-cool-700">{service.avgRating}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-1">
                        {service.growth > 0 ? (
                          <ArrowUp className="w-4 h-4 text-green-500" />
                        ) : (
                          <ArrowDown className="w-4 h-4 text-red-500" />
                        )}
                        <span className={`font-medium ${service.growth > 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {service.growth > 0 ? '+' : ''}{service.growth}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary-500 h-2 rounded-full" 
                          style={{ width: `${Math.min(100, (service.revenue / 4000) * 100)}%` }}
                        ></div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Additional Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Peak Hours */}
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Clock className="w-5 h-5 text-primary-600" />
              </div>
              <h3 className="font-bold text-cool-800">Peak Hours</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-cool-600">10:00 AM - 12:00 PM</span>
                <span className="font-bold text-cool-800">35%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-cool-600">2:00 PM - 4:00 PM</span>
                <span className="font-bold text-cool-800">28%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-cool-600">4:00 PM - 6:00 PM</span>
                <span className="font-bold text-cool-800">22%</span>
              </div>
            </div>
          </div>

          {/* Customer Satisfaction */}
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-secondary-100 rounded-lg">
                <Heart className="w-5 h-5 text-secondary-600" />
              </div>
              <h3 className="font-bold text-cool-800">Customer Satisfaction</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-cool-600">5 Stars</span>
                <span className="font-bold text-cool-800">68%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-cool-600">4 Stars</span>
                <span className="font-bold text-cool-800">22%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-cool-600">3 Stars</span>
                <span className="font-bold text-cool-800">8%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-cool-600">Below 3 Stars</span>
                <span className="font-bold text-cool-800">2%</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="glass-card rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-accent-100 rounded-lg">
                <Zap className="w-5 h-5 text-accent-600" />
              </div>
              <h3 className="font-bold text-cool-800">Quick Actions</h3>
            </div>
            <div className="space-y-3">
              <button className="w-full text-left p-3 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
                <p className="font-medium text-cool-800">Schedule Report</p>
                <p className="text-sm text-cool-600">Set up automated reports</p>
              </button>
              <button className="w-full text-left p-3 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
                <p className="font-medium text-cool-800">Export Data</p>
                <p className="text-sm text-cool-600">Download analytics data</p>
              </button>
              <button className="w-full text-left p-3 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
                <p className="font-medium text-cool-800">Set Goals</p>
                <p className="text-sm text-cool-600">Define performance targets</p>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
