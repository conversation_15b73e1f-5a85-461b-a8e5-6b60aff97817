'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export default function DebugPage() {
  const { user, firebaseUser, forceCreateUserDocument } = useAuth();
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);

  const createUserManually = async () => {
    if (!firebaseUser) {
      setStatus('❌ No Firebase user found. Please sign in first.');
      return;
    }

    setLoading(true);
    setStatus('🔧 Creating user document...');

    try {
      // Create a comprehensive user document
      const userData = {
        id: firebaseUser.uid,
        email: firebaseUser.email || '',
        name: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
        role: 'pet_owner', // Default to pet_owner, can be changed later
        verified: firebaseUser.emailVerified,
        joinedDate: new Date().toISOString(),
        fetchlyBalance: 0,
        rewardPoints: 0,
        profilePicture: firebaseUser.photoURL || '',
        bannerImage: '',
        bio: '',
        location: '',
        phone: '',
        isProfilePrivate: false,
        showEmail: false,
        showPhone: false,
        allowMessages: true,
        emailNotifications: true,
        pushNotifications: true
      };

      console.log('Creating user with data:', userData);

      const userRef = doc(db, COLLECTIONS.USERS, firebaseUser.uid);
      await setDoc(userRef, userData, { merge: true }); // Use merge to avoid overwriting existing data

      setStatus('✅ User document created successfully!');

      // Verify it was created
      const userSnap = await getDoc(userRef);
      if (userSnap.exists()) {
        console.log('User document verified:', userSnap.data());
        setStatus('✅ User document verified in Firestore! Redirecting...');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setStatus('❌ User document creation failed - not found after creation');
      }
    } catch (error: any) {
      console.error('Error creating user:', error);
      setStatus(`❌ Error: ${error.message || error}`);
    } finally {
      setLoading(false);
    }
  };

  const checkUserDocument = async () => {
    if (!firebaseUser) {
      setStatus('❌ No Firebase user found.');
      return;
    }

    setLoading(true);
    setStatus('🔍 Checking user document...');

    try {
      const userRef = doc(db, COLLECTIONS.USERS, firebaseUser.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        setStatus('✅ User document exists in Firestore!');
        console.log('User data:', userSnap.data());
      } else {
        setStatus('❌ User document does NOT exist in Firestore.');
      }
    } catch (error) {
      console.error('Error checking user:', error);
      setStatus(`❌ Error checking: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const changeUserRole = async (newRole: 'pet_owner' | 'provider') => {
    if (!firebaseUser) {
      setStatus('❌ No Firebase user found.');
      return;
    }

    setLoading(true);
    setStatus(`🔄 Changing role to ${newRole}...`);

    try {
      const userRef = doc(db, COLLECTIONS.USERS, firebaseUser.uid);
      await setDoc(userRef, { role: newRole }, { merge: true });

      setStatus(`✅ Role changed to ${newRole}! Refreshing page...`);
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error('Error changing role:', error);
      setStatus(`❌ Error changing role: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">🔧 Debug Page</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Firebase User Info</h2>
          {firebaseUser ? (
            <div className="space-y-2 text-sm">
              <p><strong>UID:</strong> {firebaseUser.uid}</p>
              <p><strong>Email:</strong> {firebaseUser.email}</p>
              <p><strong>Display Name:</strong> {firebaseUser.displayName || 'None'}</p>
              <p><strong>Email Verified:</strong> {firebaseUser.emailVerified ? 'Yes' : 'No'}</p>
            </div>
          ) : (
            <p className="text-red-600">No Firebase user found. Please sign in.</p>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Fetchly User Info</h2>
          {user ? (
            <div className="space-y-2 text-sm">
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Name:</strong> {user.name}</p>
              <p><strong>Role:</strong> <span className={`px-2 py-1 rounded text-xs font-medium ${
                user.role === 'provider' ? 'bg-blue-100 text-blue-800' :
                user.role === 'pet_owner' ? 'bg-green-100 text-green-800' :
                'bg-gray-100 text-gray-800'
              }`}>{user.role}</span></p>
              {user.role === 'provider' && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-blue-800 font-medium">🎉 Provider Account Detected!</p>
                  <p className="text-blue-600 text-sm mt-1">
                    You should have access to the Provider Dashboard with all business features.
                  </p>
                  <div className="flex gap-2 mt-2">
                    <a
                      href="/provider/dashboard"
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      Go to Provider Dashboard
                    </a>
                    <a
                      href="/provider/profile"
                      className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                    >
                      Go to Provider Profile
                    </a>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p className="text-red-600">No Fetchly user found.</p>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="space-y-4">
            <button
              onClick={checkUserDocument}
              disabled={loading}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Checking...' : 'Check User Document in Firestore'}
            </button>
            
            <button
              onClick={createUserManually}
              disabled={loading}
              className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create User Document Manually'}
            </button>
            
            <button
              onClick={async () => {
                setLoading(true);
                try {
                  const result = await forceCreateUserDocument();
                  setStatus(result.success ? '✅ Force create successful!' : `❌ ${result.error}`);
                } catch (error) {
                  setStatus(`❌ Error: ${error}`);
                } finally {
                  setLoading(false);
                }
              }}
              disabled={loading}
              className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Force Create User (AuthContext)'}
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Change User Role</h2>
          <p className="text-sm text-gray-600 mb-4">
            Use this to switch between Pet Owner and Provider roles for testing.
          </p>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => changeUserRole('pet_owner')}
              disabled={loading || user?.role === 'pet_owner'}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? 'Changing...' : 'Set as Pet Owner'}
            </button>

            <button
              onClick={() => changeUserRole('provider')}
              disabled={loading || user?.role === 'provider'}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Changing...' : 'Set as Provider'}
            </button>
          </div>
        </div>

        {status && (
          <div className="bg-gray-100 rounded-lg p-4">
            <h3 className="font-semibold mb-2">Status:</h3>
            <p className="text-sm">{status}</p>
          </div>
        )}
      </div>
    </div>
  );
}
