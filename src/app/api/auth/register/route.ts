import { NextRequest, NextResponse } from 'next/server';
import { createUser } from '@/lib/auth';
import { withMiddleware } from '@/lib/middleware';
import { validatePasswordStrength } from '@/lib/security';

// Input validation schema
const registerSchema = {
  email: { required: true, type: 'email' as const },
  password: { required: true, type: 'string' as const, minLength: 8 },
  name: { required: true, type: 'string' as const, minLength: 2, maxLength: 100 },
  role: { required: false, type: 'string' as const },
  phone: { required: false, type: 'string' as const },
  location: { required: false, type: 'string' as const }
};

async function registerHandler(request: NextRequest) {
  try {
    const { email, password, name, role, phone, location } = (request as any).validatedData;

    // Validate password strength
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Password does not meet security requirements',
        details: passwordValidation.errors
      }, { status: 400 });
    }

    // Validate role
    const validRoles = ['pet_owner', 'provider', 'admin'];
    if (role && !validRoles.includes(role)) {
      throw new Error('Invalid role specified');
    }

    // Create user
    const user = await createUser({
      email,
      password,
      name,
      role: role || 'pet_owner',
      phone,
      location
    });

    return NextResponse.json({
      success: true,
      user,
      message: 'Account created successfully. Please check your email for verification.'
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Registration failed'
    }, { status: 400 });
  }
}

export const POST = withMiddleware(registerHandler, {
  rateLimit: true,
  validation: registerSchema
});

// OPTIONS is handled automatically by withMiddleware
