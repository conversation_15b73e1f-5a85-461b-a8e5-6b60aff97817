import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth';
import { withMiddleware } from '@/lib/middleware';
import { logSecurityEvent } from '@/lib/security';

// Input validation schema
const loginSchema = {
  email: { required: true, type: 'email' as const },
  password: { required: true, type: 'string' as const, minLength: 6 }
};

async function loginHandler(request: NextRequest) {
  try {
    const { email, password } = (request as any).validatedData;
    const ip = request.ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    // Authenticate user
    const result = await authenticateUser(email, password);

    // Create response
    const response = NextResponse.json({
      success: true,
      user: result.user,
      token: result.token
    });

    // Set refresh token as httpOnly cookie
    response.cookies.set('refreshToken', result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Login error:', error);

    // Log failed authentication attempt
    const ip = request.ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    logSecurityEvent({
      type: 'auth_failure',
      ip,
      userAgent: request.headers.get('user-agent') || undefined,
      details: {
        path: request.nextUrl.pathname,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Login failed'
    }, { status: 400 });
  }
}

export const POST = withMiddleware(loginHandler, {
  rateLimit: true,
  validation: loginSchema
});

// OPTIONS is handled automatically by withMiddleware
