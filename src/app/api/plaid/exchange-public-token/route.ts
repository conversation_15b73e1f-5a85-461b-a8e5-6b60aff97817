import { NextRequest, NextResponse } from 'next/server';
import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid';
import { db } from '@/lib/firebase/config';
import { doc, setDoc, collection, addDoc } from 'firebase/firestore';

const configuration = new Configuration({
  basePath: PlaidEnvironments.sandbox,
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID,
      'PLAID-SECRET': process.env.PLAID_SECRET,
    },
  },
});

const plaidClient = new PlaidApi(configuration);

export async function POST(request: NextRequest) {
  try {
    const { publicToken, userId } = await request.json();

    if (!publicToken || !userId) {
      return NextResponse.json(
        { error: 'Public token and user ID are required' },
        { status: 400 }
      );
    }

    // Exchange public token for access token
    const exchangeResponse = await plaidClient.itemPublicTokenExchange({
      public_token: publicToken,
    });

    const accessToken = exchangeResponse.data.access_token;
    const itemId = exchangeResponse.data.item_id;

    // Get account information
    const accountsResponse = await plaidClient.accountsGet({
      access_token: accessToken,
    });

    const accounts = accountsResponse.data.accounts;

    // Store bank accounts in Firebase
    const bankAccounts = [];
    for (const account of accounts) {
      const bankAccount = {
        id: account.account_id,
        userId,
        name: account.name,
        mask: account.mask || '',
        type: account.type,
        subtype: account.subtype || '',
        institution_name: accountsResponse.data.item?.institution_id || 'Unknown',
        account_id: account.account_id,
        access_token: accessToken,
        item_id: itemId,
        is_primary: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Save to Firebase
      await addDoc(collection(db, 'bank_accounts'), bankAccount);
      bankAccounts.push(bankAccount);
    }

    return NextResponse.json(bankAccounts);
  } catch (error) {
    console.error('Error exchanging public token:', error);
    return NextResponse.json(
      { error: 'Failed to exchange public token' },
      { status: 500 }
    );
  }
}
