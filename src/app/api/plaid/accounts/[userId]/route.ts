import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase/config';
import { collection, query, where, getDocs } from 'firebase/firestore';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get bank accounts from Firebase
    const q = query(
      collection(db, 'bank_accounts'),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(q);
    const bankAccounts = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));

    return NextResponse.json(bankAccounts);
  } catch (error) {
    console.error('Error getting bank accounts:', error);
    return NextResponse.json(
      { error: 'Failed to get bank accounts' },
      { status: 500 }
    );
  }
}
