import { NextRequest, NextResponse } from 'next/server';
import { ProviderService } from '@/lib/stripe/provider-service';
import { adminAuth } from '@/lib/firebase-admin';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Check if Firebase Admin SDK is initialized
    if (!adminAuth) {
      return NextResponse.json({
        error: 'Payment system not configured. Please contact support.'
      }, { status: 503 });
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is a provider
    const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, decodedToken.uid));
    if (!userDoc.exists() || userDoc.data().role !== 'provider') {
      return NextResponse.json({ error: 'Access denied. Provider role required.' }, { status: 403 });
    }

    // Get provider data
    const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, decodedToken.uid));
    if (!providerDoc.exists()) {
      return NextResponse.json({ error: 'Provider profile not found' }, { status: 404 });
    }

    const provider = providerDoc.data();

    // Check if already has Stripe account
    if (provider.stripeAccountId) {
      // Check onboarding status
      const statusResult = await ProviderService.checkOnboardingStatus(provider.stripeAccountId);
      
      if (statusResult.success && statusResult.isOnboarded) {
        return NextResponse.json({
          success: true,
          message: 'Already onboarded',
          isOnboarded: true,
          accountId: provider.stripeAccountId,
        });
      } else {
        // Create new onboarding link
        const linkResult = await ProviderService.createOnboardingLink(provider.stripeAccountId);
        
        if (linkResult.success) {
          return NextResponse.json({
            success: true,
            onboardingUrl: linkResult.url,
            accountId: provider.stripeAccountId,
            isOnboarded: false,
          });
        } else {
          return NextResponse.json(
            { error: linkResult.error },
            { status: 400 }
          );
        }
      }
    }

    // Create new Stripe Connect account
    const accountResult = await ProviderService.createConnectAccount({
      providerId: decodedToken.uid,
      email: decodedToken.email || provider.email,
      businessName: provider.businessName || provider.ownerName,
      country: 'US', // You can make this dynamic based on provider location
    });

    if (!accountResult.success) {
      return NextResponse.json(
        { error: accountResult.error },
        { status: 400 }
      );
    }

    // Create onboarding link
    const linkResult = await ProviderService.createOnboardingLink(accountResult.accountId);

    if (!linkResult.success) {
      return NextResponse.json(
        { error: linkResult.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      onboardingUrl: linkResult.url,
      accountId: accountResult.accountId,
      isOnboarded: false,
      message: 'Stripe Connect account created successfully',
    });

  } catch (error: any) {
    console.error('Error creating provider onboarding:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check if Firebase Admin SDK is initialized
    if (!adminAuth) {
      return NextResponse.json({
        error: 'Payment system not configured. Please contact support.'
      }, { status: 503 });
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get provider data
    const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, decodedToken.uid));
    if (!providerDoc.exists()) {
      return NextResponse.json({ error: 'Provider profile not found' }, { status: 404 });
    }

    const provider = providerDoc.data();

    if (!provider.stripeAccountId) {
      return NextResponse.json({
        success: true,
        isOnboarded: false,
        message: 'No Stripe account found',
      });
    }

    // Check onboarding status
    const statusResult = await ProviderService.checkOnboardingStatus(provider.stripeAccountId);

    if (!statusResult.success) {
      return NextResponse.json(
        { error: statusResult.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      isOnboarded: statusResult.isOnboarded,
      accountId: provider.stripeAccountId,
      status: statusResult.status,
    });

  } catch (error: any) {
    console.error('Error checking onboarding status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
