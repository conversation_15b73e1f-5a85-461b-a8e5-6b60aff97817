import { NextRequest, NextResponse } from 'next/server';
import { stripe, logProductionEvent } from '@/lib/stripe/server-config';
import { verifyIdToken, adminDb } from '@/lib/firebase-admin';
import { COLLECTIONS } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      decodedToken = await verifyIdToken(token);
    } catch (error) {
      logProductionEvent('provider_onboard_auth_failed', { error: error.message }, 'error');
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    if (!adminDb) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    // Check if user is a provider
    const userDoc = await adminDb.collection(COLLECTIONS.USERS).doc(decodedToken.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'provider') {
      return NextResponse.json({ error: 'Access denied. Provider role required.' }, { status: 403 });
    }

    // Get provider data
    const providerDoc = await adminDb.collection(COLLECTIONS.PROVIDERS).doc(decodedToken.uid).get();
    if (!providerDoc.exists) {
      return NextResponse.json({ error: 'Provider profile not found' }, { status: 404 });
    }

    const provider = providerDoc.data();

    // Check if already has Stripe account
    if (provider?.stripeAccountId) {
      try {
        // Check onboarding status
        const account = await stripe.accounts.retrieve(provider.stripeAccountId);

        const isOnboarded = account.details_submitted &&
                           account.charges_enabled &&
                           account.payouts_enabled;

        if (isOnboarded) {
          logProductionEvent('provider_already_onboarded', {
            providerId: decodedToken.uid,
            accountId: provider.stripeAccountId
          });

          return NextResponse.json({
            success: true,
            message: 'Already onboarded',
            isOnboarded: true,
            accountId: provider.stripeAccountId,
            status: {
              detailsSubmitted: account.details_submitted,
              chargesEnabled: account.charges_enabled,
              payoutsEnabled: account.payouts_enabled,
            }
          });
        } else {
          // Create new onboarding link
          const accountLink = await stripe.accountLinks.create({
            account: provider.stripeAccountId,
            refresh_url: `${request.headers.get('origin')}/provider/dashboard?setup=refresh`,
            return_url: `${request.headers.get('origin')}/provider/dashboard?setup=complete`,
            type: 'account_onboarding',
          });

          return NextResponse.json({
            success: true,
            onboardingUrl: accountLink.url,
            accountId: provider.stripeAccountId,
            isOnboarded: false,
          });
        }
      } catch (error: any) {
        logProductionEvent('stripe_account_check_failed', {
          providerId: decodedToken.uid,
          error: error.message
        }, 'error');

        return NextResponse.json(
          { error: 'Failed to check Stripe account status' },
          { status: 400 }
        );
      }
    }

    // Create new Stripe Connect account
    try {
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US', // You can make this dynamic based on provider location
        email: decodedToken.email || provider?.email,
        business_profile: {
          name: provider?.businessName || provider?.ownerName || 'Pet Service Provider',
          product_description: 'Pet care services',
          support_email: decodedToken.email || provider?.email,
        },
        metadata: {
          providerId: decodedToken.uid,
          fetchlyProvider: 'true',
        },
      });

      // Save Stripe account ID to provider document
      await adminDb.collection(COLLECTIONS.PROVIDERS).doc(decodedToken.uid).update({
        stripeAccountId: account.id,
        stripeOnboardingStarted: new Date(),
        updatedAt: new Date(),
      });

      // Create onboarding link
      const accountLink = await stripe.accountLinks.create({
        account: account.id,
        refresh_url: `${request.headers.get('origin')}/provider/dashboard?setup=refresh`,
        return_url: `${request.headers.get('origin')}/provider/dashboard?setup=complete`,
        type: 'account_onboarding',
      });

      logProductionEvent('stripe_account_created', {
        providerId: decodedToken.uid,
        accountId: account.id
      });

      return NextResponse.json({
        success: true,
        onboardingUrl: accountLink.url,
        accountId: account.id,
        isOnboarded: false,
        message: 'Stripe Connect account created successfully',
      });
    } catch (error: any) {
      logProductionEvent('stripe_account_creation_failed', {
        providerId: decodedToken.uid,
        error: error.message
      }, 'error');

      return NextResponse.json(
        { error: 'Failed to create Stripe account' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Error creating provider onboarding:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      decodedToken = await verifyIdToken(token);
    } catch (error) {
      logProductionEvent('provider_status_auth_failed', { error: error.message }, 'error');
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    if (!adminDb) {
      return NextResponse.json({ error: 'Database not available' }, { status: 503 });
    }

    // Get provider data
    const providerDoc = await adminDb.collection(COLLECTIONS.PROVIDERS).doc(decodedToken.uid).get();
    if (!providerDoc.exists) {
      return NextResponse.json({ error: 'Provider profile not found' }, { status: 404 });
    }

    const provider = providerDoc.data();

    if (!provider?.stripeAccountId) {
      return NextResponse.json({
        success: true,
        isOnboarded: false,
        message: 'No Stripe account found',
      });
    }

    try {
      // Check onboarding status
      const account = await stripe.accounts.retrieve(provider.stripeAccountId);

      const isOnboarded = account.details_submitted &&
                         account.charges_enabled &&
                         account.payouts_enabled;

      return NextResponse.json({
        success: true,
        isOnboarded,
        accountId: provider.stripeAccountId,
        status: {
          detailsSubmitted: account.details_submitted,
          chargesEnabled: account.charges_enabled,
          payoutsEnabled: account.payouts_enabled,
        },
      });
    } catch (error: any) {
      logProductionEvent('stripe_status_check_failed', {
        providerId: decodedToken.uid,
        error: error.message
      }, 'error');

      return NextResponse.json(
        { error: 'Failed to check Stripe account status' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Error checking onboarding status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
