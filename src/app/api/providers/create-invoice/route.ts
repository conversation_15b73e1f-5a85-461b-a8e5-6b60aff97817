import { NextRequest, NextResponse } from 'next/server';
import { ProviderService } from '@/lib/stripe/provider-service';
import { adminAuth } from '@/lib/firebase/admin-sdk';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { COLLECTIONS } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Check if Firebase Admin SDK is initialized
    if (!adminAuth) {
      return NextResponse.json({
        error: 'Payment system not configured. Please contact support.'
      }, { status: 503 });
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];

    // Verify the Firebase token
    let decodedToken;
    try {
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user is a provider
    const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, decodedToken.uid));
    if (!userDoc.exists() || userDoc.data().role !== 'provider') {
      return NextResponse.json({ error: 'Access denied. Provider role required.' }, { status: 403 });
    }

    // Get provider data
    const providerDoc = await getDoc(doc(db, COLLECTIONS.PROVIDERS, decodedToken.uid));
    if (!providerDoc.exists()) {
      return NextResponse.json({ error: 'Provider profile not found' }, { status: 404 });
    }

    const provider = providerDoc.data();

    // Check if provider is onboarded
    if (!provider.stripeAccountId || provider.stripeOnboardingStatus !== 'completed') {
      return NextResponse.json({ 
        error: 'Please complete Stripe onboarding first',
        needsOnboarding: true 
      }, { status: 400 });
    }

    const body = await request.json();
    const { amount, description, customerEmail, successUrl, cancelUrl } = body;

    // Validate required fields
    if (!amount || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, description' },
        { status: 400 }
      );
    }

    // Validate amount
    if (amount < 1 || amount > 10000) {
      return NextResponse.json(
        { error: 'Amount must be between $1 and $10,000' },
        { status: 400 }
      );
    }

    // Create payment link
    const result = await ProviderService.createPaymentLink({
      providerId: decodedToken.uid,
      amount,
      description,
      successUrl,
      cancelUrl,
      metadata: {
        customerEmail: customerEmail || '',
        createdBy: decodedToken.uid,
      },
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      paymentUrl: result.url,
      paymentLinkId: result.paymentLink?.id,
      message: 'Invoice created successfully',
    });

  } catch (error: any) {
    console.error('Error creating invoice:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
