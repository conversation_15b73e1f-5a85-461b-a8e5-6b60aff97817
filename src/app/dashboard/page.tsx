'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  User, Wallet, Gift, Calendar, Bell, Settings, PawPrint,
  TrendingUp, Award, CreditCard, MessageCircle, Plus, ArrowUpRight
} from 'lucide-react';

import QuickActions from '@/components/QuickActions';
import PostCard from '@/components/PostCard';
import WalletManager from '@/components/payments/WalletManager';

export default function DashboardPage() {
  const { user } = useAuth();
  const { stats, userPosts, loading } = useData();
  const router = useRouter();
  const [showWalletModal, setShowWalletModal] = useState(false);

  // Redirect based on authentication and user role
  useEffect(() => {
    if (!user) {
      router.push('/auth/signin');
      return;
    }

    // Redirect providers to their dedicated dashboard
    if (user.role === 'provider') {
      router.push('/provider/dashboard');
      return;
    }
  }, [user, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Dashboard Header with Gradient */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Welcome back, {user?.name?.split(' ')[0] || 'Pet Parent'}!</h1>
              <p className="text-blue-100 mt-2">Manage your pets, track rewards, and connect with the community</p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="bg-white/10 hover:bg-white/20 border border-white/30 px-4 py-2 rounded-lg flex items-center space-x-2 text-white backdrop-blur-sm">
                <Bell className="w-5 h-5" />
                <span className="hidden md:block font-medium">Notifications</span>
              </button>
              <button
                onClick={() => router.push('/profile')}
                className="bg-white/10 hover:bg-white/20 border border-white/30 px-4 py-2 rounded-lg flex items-center space-x-2 text-white backdrop-blur-sm"
              >
                <User className="w-5 h-5" />
                <span className="hidden md:block font-medium">Profile</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <PawPrint className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">My Pets</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalPets}</p>
                  </div>
                </div>
              </motion.div>

              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <Calendar className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Appointments</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.upcomingAppointments}</p>
                  </div>
                </div>
              </motion.div>

              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-lg shadow-sm p-6"
              >
                <div className="flex items-center">
                  <div className="p-3 bg-yellow-100 rounded-lg">
                    <Gift className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Reward Points</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.rewardPoints.toLocaleString()}</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => setShowWalletModal(true)}
              >
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg">
                        <Wallet className="w-6 h-6 text-white" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Fetchly Wallet</p>
                        <p className="text-2xl font-bold text-gray-900">${stats.walletBalance.toFixed(2)}</p>
                        <p className="text-xs text-gray-500 mt-1">Click to manage</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowWalletModal(true);
                        }}
                        className="p-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-lg hover:from-green-700 hover:to-blue-700 transition-all"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                      <ArrowUpRight className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>



            {/* Feed */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">My Recent Posts</h3>
              {userPosts.length > 0 ? (
                userPosts.slice(0, 5).map((post) => (
                  <PostCard key={post.id} post={post} showPrivacyIndicator={true} />
                ))
              ) : (
                <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                  <div className="text-gray-400 mb-4">
                    <MessageCircle className="w-16 h-16 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                  <p className="text-gray-500 mb-4">Share your first post to get started!</p>
                </div>
              )}

              {userPosts.length > 5 && (
                <div className="text-center">
                  <button
                    onClick={() => router.push('/profile')}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    View all {userPosts.length} posts on your profile
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div>
            <QuickActions currentPage="dashboard" />
          </div>
        </div>
      </div>

      {/* Wallet Modal */}
      {showWalletModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-800">Fetchly Wallet</h2>
                <button
                  onClick={() => setShowWalletModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  ×
                </button>
              </div>

              <WalletManager className="" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
