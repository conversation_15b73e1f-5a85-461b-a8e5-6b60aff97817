'use client';

import { useState, useEffect } from 'react';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';

export default function DebugProvidersPage() {
  const { user } = useAuth();
  const [users, setUsers] = useState<any[]>([]);
  const [providers, setProviders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load all users
      const usersSnapshot = await getDocs(collection(db, 'users'));
      const usersData = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setUsers(usersData);

      // Load all providers
      const providersSnapshot = await getDocs(collection(db, 'providers'));
      const providersData = providersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setProviders(providersData);

      console.log('Users loaded:', usersData);
      console.log('Providers loaded:', providersData);

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  const providerUsers = users.filter(u => u.role === 'provider');

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Provider Debug Page</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Users with Provider Role */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Users with Provider Role ({providerUsers.length})</h2>
          <div className="space-y-3">
            {providerUsers.map(user => (
              <div key={user.id} className="border rounded p-3">
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-gray-600">{user.email}</div>
                <div className="text-xs text-gray-500">ID: {user.id}</div>
                <div className="mt-2 space-x-2">
                  <Link 
                    href={`/provider/profile?id=${user.id}`}
                    className="text-blue-600 hover:underline text-sm"
                  >
                    View Provider Profile
                  </Link>
                  <Link 
                    href={`/profile?id=${user.id}`}
                    className="text-green-600 hover:underline text-sm"
                  >
                    View User Profile
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Provider Documents */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Provider Documents ({providers.length})</h2>
          <div className="space-y-3">
            {providers.map(provider => (
              <div key={provider.id} className="border rounded p-3">
                <div className="font-medium">{provider.businessName || provider.ownerName}</div>
                <div className="text-sm text-gray-600">{provider.email}</div>
                <div className="text-xs text-gray-500">Provider ID: {provider.id}</div>
                <div className="text-xs text-gray-500">User ID: {provider.userId}</div>
                <div className="text-xs text-gray-500">Status: {provider.status}</div>
                <div className="mt-2">
                  <Link 
                    href={`/provider/profile?id=${provider.userId}`}
                    className="text-blue-600 hover:underline text-sm"
                  >
                    View Provider Profile
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Current User Info */}
      <div className="mt-8 bg-yellow-50 rounded-lg p-6">
        <h2 className="text-xl font-bold mb-4">Current User</h2>
        {user ? (
          <div>
            <div><strong>Name:</strong> {user.name}</div>
            <div><strong>Email:</strong> {user.email}</div>
            <div><strong>Role:</strong> {user.role}</div>
            <div><strong>ID:</strong> {user.id}</div>
            <div className="mt-2">
              <Link 
                href={`/provider/profile?id=${user.id}`}
                className="text-blue-600 hover:underline"
              >
                View My Provider Profile
              </Link>
            </div>
          </div>
        ) : (
          <div>Not logged in</div>
        )}
      </div>

      {/* Test Links */}
      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <h2 className="text-xl font-bold mb-4">Test Links</h2>
        <div className="space-y-2">
          <div>
            <Link href="/community" className="text-blue-600 hover:underline">
              Go to Community (to test profile clicks)
            </Link>
          </div>
          <div>
            <Link href="/provider/dashboard" className="text-blue-600 hover:underline">
              Go to Provider Dashboard
            </Link>
          </div>
          <div>
            <Link href="/profile" className="text-blue-600 hover:underline">
              Go to Profile
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
