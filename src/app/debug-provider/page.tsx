'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProvider } from '@/contexts/ProviderContext';
import { getProviderByUserId, createProvider } from '@/lib/firebase/providers';
import toast from 'react-hot-toast';

export default function DebugProviderPage() {
  const { user } = useAuth();
  const { provider, isLoading, error, refreshProviderData } = useProvider();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isCreating, setIsCreating] = useState(false);

  const checkProviderStatus = async () => {
    if (!user) {
      setDebugInfo({ error: 'No user logged in' });
      return;
    }

    try {
      console.log('Checking provider status for user:', user.id);
      
      // Direct database check
      const providerData = await getProviderByUserId(user.id);
      
      setDebugInfo({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        providerFromDB: providerData,
        providerFromContext: provider,
        contextLoading: isLoading,
        contextError: error
      });
      
      console.log('Debug info:', {
        user,
        providerFromDB: providerData,
        providerFromContext: provider
      });
      
    } catch (err) {
      console.error('Error checking provider status:', err);
      setDebugInfo({ error: err instanceof Error ? err.message : 'Unknown error' });
    }
  };

  const createProviderProfile = async () => {
    if (!user) {
      toast.error('No user logged in');
      return;
    }

    setIsCreating(true);
    try {
      console.log('Creating provider profile for user:', user.id);
      
      const providerId = await createProvider({
        userId: user.id,
        businessName: user.name + "'s Business",
        ownerName: user.name,
        email: user.email,
        phone: user.phone || '',
        serviceType: 'General Pet Services',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        description: 'Welcome to my pet services business!',
        experience: '0-1 years',
        specialties: [],
        status: 'pending',
        verified: false,
        featured: false,
        businessHours: {
          monday: { open: '09:00', close: '17:00', closed: false },
          tuesday: { open: '09:00', close: '17:00', closed: false },
          wednesday: { open: '09:00', close: '17:00', closed: false },
          thursday: { open: '09:00', close: '17:00', closed: false },
          friday: { open: '09:00', close: '17:00', closed: false },
          saturday: { open: '10:00', close: '16:00', closed: false },
          sunday: { open: '10:00', close: '16:00', closed: true }
        }
      });
      
      console.log('Provider profile created with ID:', providerId);
      toast.success('Provider profile created successfully!');
      
      // Refresh data
      await refreshProviderData();
      await checkProviderStatus();
      
    } catch (err) {
      console.error('Error creating provider profile:', err);
      toast.error('Failed to create provider profile');
    } finally {
      setIsCreating(false);
    }
  };

  useEffect(() => {
    if (user) {
      checkProviderStatus();
    }
  }, [user, provider]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Debug Provider Profile</h1>
          <p className="text-gray-600">Please log in to debug provider profile issues.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">Provider Profile Debug</h1>
          
          <div className="space-y-6">
            <div className="flex gap-4">
              <button
                onClick={checkProviderStatus}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Check Provider Status
              </button>
              
              <button
                onClick={createProviderProfile}
                disabled={isCreating}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {isCreating ? 'Creating...' : 'Create Provider Profile'}
              </button>
              
              <button
                onClick={refreshProviderData}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                Refresh Context Data
              </button>
            </div>

            {debugInfo && (
              <div className="bg-gray-100 rounded-lg p-4">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Debug Information</h2>
                <pre className="text-sm text-gray-700 overflow-auto">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">User Info</h3>
                <div className="text-sm text-blue-700">
                  <p><strong>ID:</strong> {user.id}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Name:</strong> {user.name}</p>
                  <p><strong>Role:</strong> {user.role}</p>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-green-800 mb-2">Provider Context</h3>
                <div className="text-sm text-green-700">
                  <p><strong>Provider ID:</strong> {provider?.id || 'Not found'}</p>
                  <p><strong>Business Name:</strong> {provider?.businessName || 'Not set'}</p>
                  <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
                  <p><strong>Error:</strong> {error || 'None'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
