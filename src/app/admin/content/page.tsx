'use client';

import { useState } from 'react';
import { 
  FileText, 
  Edit3, 
  Save, 
  X, 
  Plus, 
  Trash2, 
  Eye,
  Globe,
  Image,
  Type,
  Layout
} from 'lucide-react';

interface ContentItem {
  id: string;
  type: 'page' | 'section' | 'banner' | 'text';
  title: string;
  location: string;
  content: string;
  lastModified: string;
  status: 'published' | 'draft' | 'pending';
}

const contentItems: ContentItem[] = [
  {
    id: '1',
    type: 'page',
    title: 'Homepage Hero Section',
    location: 'Homepage',
    content: 'Find the perfect care for your beloved pet',
    lastModified: '2 hours ago',
    status: 'published'
  },
  {
    id: '2',
    type: 'section',
    title: 'Service Types Description',
    location: 'Homepage',
    content: 'Professional pet care services you can trust',
    lastModified: '1 day ago',
    status: 'published'
  },
  {
    id: '3',
    type: 'banner',
    title: 'Emergency Care Banner',
    location: 'Emergency Page',
    content: '24/7 Emergency Pet Care - We\'re here when you need us most',
    lastModified: '3 days ago',
    status: 'draft'
  },
  {
    id: '4',
    type: 'text',
    title: 'About Us Mission',
    location: 'About Page',
    content: 'Connecting pet parents with trusted, verified pet care professionals.',
    lastModified: '1 week ago',
    status: 'published'
  }
];

export default function ContentManagement() {
  const [items, setItems] = useState<ContentItem[]>(contentItems);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [filter, setFilter] = useState<'all' | 'published' | 'draft' | 'pending'>('all');

  const handleEdit = (item: ContentItem) => {
    setEditingItem(item.id);
    setEditContent(item.content);
  };

  const handleSave = (id: string) => {
    setItems(items.map(item => 
      item.id === id 
        ? { ...item, content: editContent, lastModified: 'Just now', status: 'pending' as const }
        : item
    ));
    setEditingItem(null);
    setEditContent('');
  };

  const handleCancel = () => {
    setEditingItem(null);
    setEditContent('');
  };

  const handlePublish = (id: string) => {
    setItems(items.map(item => 
      item.id === id 
        ? { ...item, status: 'published' as const, lastModified: 'Just now' }
        : item
    ));
  };

  const handleDelete = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  const filteredItems = items.filter(item => 
    filter === 'all' || item.status === filter
  );

  const getTypeIcon = (type: ContentItem['type']) => {
    switch (type) {
      case 'page': return Globe;
      case 'section': return Layout;
      case 'banner': return Image;
      case 'text': return Type;
      default: return FileText;
    }
  };

  const getStatusColor = (status: ContentItem['status']) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-700';
      case 'draft': return 'bg-gray-100 text-gray-700';
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Content Management</h1>
          <p className="text-cool-600">Manage website content, pages, and sections</p>
        </div>
        <button className="btn-primary flex items-center gap-2">
          <Plus className="w-5 h-5" />
          Add Content
        </button>
      </div>

      {/* Filters */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex items-center gap-4">
          <span className="text-cool-700 font-medium">Filter by status:</span>
          <div className="flex gap-2">
            {(['all', 'published', 'draft', 'pending'] as const).map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  filter === status
                    ? 'bg-primary-500 text-white'
                    : 'bg-white/50 text-cool-700 hover:bg-white/70'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content Items */}
      <div className="space-y-4">
        {filteredItems.map((item) => {
          const TypeIcon = getTypeIcon(item.type);
          const isEditing = editingItem === item.id;

          return (
            <div key={item.id} className="glass-card rounded-2xl p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                    <TypeIcon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-bold text-cool-800">{item.title}</h3>
                    <p className="text-sm text-cool-600">{item.location}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                  <span className="text-xs text-cool-500">{item.lastModified}</span>
                </div>
              </div>

              {isEditing ? (
                <div className="space-y-4">
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="w-full p-4 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 min-h-[120px]"
                    placeholder="Enter content..."
                  />
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleSave(item.id)}
                      className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200"
                    >
                      <Save className="w-4 h-4" />
                      Save Changes
                    </button>
                    <button
                      onClick={handleCancel}
                      className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200"
                    >
                      <X className="w-4 h-4" />
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="p-4 bg-white/50 rounded-lg">
                    <p className="text-cool-800">{item.content}</p>
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleEdit(item)}
                      className="flex items-center gap-2 px-3 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors duration-200"
                    >
                      <Edit3 className="w-4 h-4" />
                      Edit
                    </button>
                    
                    {item.status === 'pending' && (
                      <button
                        onClick={() => handlePublish(item.id)}
                        className="flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200"
                      >
                        <Eye className="w-4 h-4" />
                        Publish
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="flex items-center gap-2 px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {filteredItems.length === 0 && (
        <div className="glass-card rounded-2xl p-12 text-center">
          <FileText className="w-16 h-16 text-cool-400 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-cool-800 mb-2">No content found</h3>
          <p className="text-cool-600 mb-6">No content items match the selected filter.</p>
          <button className="btn-primary">
            Add New Content
          </button>
        </div>
      )}
    </div>
  );
}
