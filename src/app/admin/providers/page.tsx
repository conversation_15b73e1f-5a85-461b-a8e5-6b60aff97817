'use client';

import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, updateDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  Building2,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  MapPin,
  ArrowUpRight,
  Download,
  Plus,
  Clock
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Provider {
  id: string;
  businessName: string;
  name: string;
  email: string;
  phone?: string;
  location: string;
  services: string[];
  status: 'pending' | 'approved' | 'rejected';
  createdAt?: any;
  rating?: number;
  totalBookings?: number;
  totalRevenue?: number;
  profilePhoto?: string;
  description?: string;
}

export default function AdminProvidersPage() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterService, setFilterService] = useState<string>('all');

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      setLoading(true);
      const providersQuery = query(
        collection(db, 'providers'),
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(providersQuery);
      const providersData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Provider[];

      setProviders(providersData);
    } catch (error) {
      console.error('Error loading providers:', error);
      toast.error('Failed to load providers');
    } finally {
      setLoading(false);
    }
  };

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.businessName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.email?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || provider.status === filterStatus;
    const matchesService = filterService === 'all' ||
                          provider.services?.some(service => service.toLowerCase().includes(filterService.toLowerCase()));
    return matchesSearch && matchesStatus && matchesService;
  });

  const updateProviderStatus = async (providerId: string, status: 'approved' | 'rejected') => {
    try {
      await updateDoc(doc(db, 'providers', providerId), { status });
      await loadProviders();
      toast.success(`Provider ${status} successfully`);
    } catch (error) {
      console.error('Error updating provider:', error);
      toast.error('Failed to update provider');
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-200 border-t-primary-600 mx-auto mb-4"></div>
          <p className="text-primary-600 font-medium">Loading providers...</p>
        </div>
      </div>
    );
  }

  const stats = [
    {
      name: 'Total Providers',
      value: providers.length.toLocaleString(),
      icon: Building2,
      change: '+12%',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600'
    },
    {
      name: 'Approved',
      value: providers.filter(p => p.status === 'approved').length.toLocaleString(),
      icon: CheckCircle,
      change: '+8%',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Pending',
      value: providers.filter(p => p.status === 'pending').length.toLocaleString(),
      icon: Clock,
      change: '+15%',
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600'
    },
    {
      name: 'Rejected',
      value: providers.filter(p => p.status === 'rejected').length.toLocaleString(),
      icon: XCircle,
      change: '-5%',
      bgColor: 'bg-red-50',
      iconColor: 'text-red-600'
    }
  ];

  return (
    <div className="space-y-8 p-6">
      {/* Header */}
      <div className="bg-white rounded-2xl shadow-xl p-8 border border-white/20">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Provider Management
            </h1>
            <p className="text-gray-600 mt-2">Manage service providers and their applications</p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
            <button className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
              <Plus className="h-4 w-4" />
              <span>Add Provider</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-2xl shadow-xl p-6 border border-white/20 hover:shadow-2xl transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                <stat.icon className={`h-8 w-8 ${stat.iconColor}`} />
              </div>
              <div className={`flex items-center space-x-1 text-sm font-semibold ${
                stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.change.startsWith('+') ? (
                  <ArrowUpRight className="h-4 w-4" />
                ) : (
                  <ArrowUpRight className="h-4 w-4 rotate-180" />
                )}
                <span>{stat.change}</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">{stat.name}</h3>
              <p className="text-3xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">
                {stat.value}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Search and Filters */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
            <input
              type="text"
              placeholder="Search providers by name, email, or services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            />
          </div>
          
          <div className="flex gap-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>

            <select
              value={filterService}
              onChange={(e) => setFilterService(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              <option value="all">All Services</option>
              <option value="grooming">Grooming</option>
              <option value="walking">Walking</option>
              <option value="sitting">Pet Sitting</option>
              <option value="training">Training</option>
              <option value="veterinary">Veterinary</option>
            </select>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/30">
          <div className="flex items-center justify-between">
            <p className="text-cool-700">
              <span className="font-bold">{filteredProviders.length}</span> providers found
            </p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Provider</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Type</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Rating</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Performance</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Revenue</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/30">
              {filteredProviders.map((provider) => (
                <tr key={provider.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium">
                          {provider.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-cool-800">{provider.name}</p>
                          {provider.verified && (
                            <Verified className="w-4 h-4 text-blue-500" />
                          )}
                          {provider.featured && (
                            <Award className="w-4 h-4 text-yellow-500" />
                          )}
                        </div>
                        <p className="text-sm text-cool-600">{provider.email}</p>
                        <div className="flex items-center gap-4 text-xs text-cool-500 mt-1">
                          <div className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            <span>{provider.phone}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            <span>{provider.location}</span>
                          </div>
                          {provider.website && (
                            <div className="flex items-center gap-1">
                              <Globe className="w-3 h-3" />
                              <span>Website</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className="px-3 py-1 bg-secondary-100 text-secondary-700 rounded-full text-sm font-medium">
                      {provider.type}
                    </span>
                    <div className="mt-2">
                      <div className="flex flex-wrap gap-1">
                        {provider.services.slice(0, 2).map((service, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                            {service}
                          </span>
                        ))}
                        {provider.services.length > 2 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                            +{provider.services.length - 2}
                          </span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium w-fit ${getStatusColor(provider.status)}`}>
                      {getStatusIcon(provider.status)}
                      <span className="capitalize">{provider.status}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="font-medium text-cool-800">{provider.rating}</span>
                      <span className="text-cool-600 text-sm">({provider.reviewCount})</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <p className="text-cool-800">
                        <span className="font-medium">{provider.totalBookings}</span> bookings
                      </p>
                      <p className="text-cool-600">{provider.completionRate}% completion</p>
                      <p className="text-cool-600">{provider.responseTime} response</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <p className="font-medium text-cool-800">
                        ${provider.totalRevenue.toLocaleString()}
                      </p>
                      <p className="text-cool-600">Total revenue</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleProviderAction(provider.id, 'view')}
                        className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleProviderAction(provider.id, 'edit')}
                        className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                        title="Edit Provider"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      {provider.status === 'pending' && (
                        <button
                          onClick={() => handleProviderAction(provider.id, 'approve')}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors duration-200"
                          title="Approve Provider"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      {provider.status === 'active' && (
                        <button
                          onClick={() => handleProviderAction(provider.id, 'suspend')}
                          className="p-2 text-yellow-600 hover:bg-yellow-100 rounded-lg transition-colors duration-200"
                          title="Suspend Provider"
                        >
                          <XCircle className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleProviderAction(provider.id, 'delete')}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200"
                        title="Delete Provider"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredProviders.length === 0 && (
          <div className="p-12 text-center">
            <Building2 className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No providers found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search criteria or filters.</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedType('All Types');
                setSelectedStatus('All Status');
              }}
              className="btn-primary"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
