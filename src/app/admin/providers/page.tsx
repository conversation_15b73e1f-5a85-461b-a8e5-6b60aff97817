'use client';

import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, updateDoc, doc, writeBatch, addDoc, deleteDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  Building2,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  MapPin,
  ArrowUpRight,
  Download,
  Plus,
  Clock,
  Star,
  Phone,
  Mail,
  Globe,
  Verified,
  Award,
  Trash2,
  Edit3,
  Settings,
  Users,
  Calendar,
  DollarSign,
  AlertTriangle,
  RefreshCw,
  Zap
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { getAllProviders, debugProviders } from '@/lib/firebase/providers';
import { quickAdminSetup, setupAdminUser } from '@/lib/firebase/admin';

interface Provider {
  id: string;
  businessName: string;
  name?: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  location?: string;
  serviceType?: string;
  services?: string[];
  specialties?: string[];
  status: 'pending' | 'approved' | 'rejected';
  createdAt?: any;
  rating?: number;
  reviewCount?: number;
  totalBookings?: number;
  totalRevenue?: number;
  profilePhoto?: string;
  description?: string;
  verified?: boolean;
  featured?: boolean;
  website?: string;
  businessHours?: any;
  activeServices?: any[];
  allServices?: any[];
  hasActiveServices?: boolean;
  completionRate?: number;
  responseTime?: string;
}

export default function AdminProvidersPage() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterService, setFilterService] = useState<string>('all');
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [bulkAction, setBulkAction] = useState('');
  const [selectedProviders, setSelectedProviders] = useState<string[]>([]);

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading all providers for admin...');

      // Use getAllProviders to get all providers regardless of status
      const allProviders = await getAllProviders();
      console.log(`📊 Loaded ${allProviders.length} providers`);

      setProviders(allProviders);
    } catch (error) {
      console.error('Error loading providers:', error);
      toast.error('Failed to load providers');
    } finally {
      setLoading(false);
    }
  };

  // Debug function to check database
  const runDebug = async () => {
    try {
      await debugProviders();
      toast.success('Debug info logged to console');
    } catch (error) {
      console.error('Debug error:', error);
      toast.error('Debug failed');
    }
  };

  // Admin setup function
  const setupAdmin = async () => {
    try {
      console.log('🔧 Setting up admin access...');
      const result = await quickAdminSetup();
      toast.success(`Admin setup complete! User ID: ${result.userId}`);
      console.log('🎉 Admin setup result:', result);
    } catch (error) {
      console.error('❌ Admin setup error:', error);
      toast.error('Admin setup failed');
    }
  };

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.businessName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.serviceType?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || provider.status === filterStatus;
    const matchesService = filterService === 'all' ||
                          provider.serviceType?.toLowerCase().includes(filterService.toLowerCase()) ||
                          provider.services?.some(service => service.toLowerCase().includes(filterService.toLowerCase()));
    return matchesSearch && matchesStatus && matchesService;
  });

  // Comprehensive provider management functions
  const updateProviderStatus = async (providerId: string, status: 'approved' | 'rejected') => {
    try {
      console.log(`🔄 Updating provider ${providerId} status to ${status}`);

      const providerRef = doc(db, 'providers', providerId);
      await updateDoc(providerRef, {
        status,
        updatedAt: new Date()
      });

      console.log(`✅ Provider ${providerId} status updated to ${status}`);
      await loadProviders();
      toast.success(`Provider ${status} successfully`);
    } catch (error) {
      console.error('❌ Error updating provider status:', error);
      toast.error(`Failed to ${status} provider: ${error.message}`);
    }
  };

  // Approve provider and create sample service
  const approveProviderWithService = async (providerId: string) => {
    try {
      console.log(`🔄 Approving provider ${providerId} and creating service...`);

      const provider = providers.find(p => p.id === providerId);
      if (!provider) {
        toast.error('Provider not found');
        return;
      }

      const batch = writeBatch(db);

      // Update provider status
      const providerRef = doc(db, 'providers', providerId);
      batch.update(providerRef, {
        status: 'approved',
        updatedAt: new Date()
      });

      console.log(`📝 Provider ${provider.businessName || provider.name} status set to approved`);

      // Create a sample service if none exists
      if (!provider.hasActiveServices) {
        const serviceRef = doc(collection(db, 'services'));
        const serviceData = {
          providerId: providerId,
          name: provider.serviceType || 'Pet Care Service',
          description: `Professional ${provider.serviceType || 'pet care'} service by ${provider.businessName || provider.name}`,
          category: provider.serviceType || 'Pet Sitting',
          price: 50,
          duration: 60,
          petTypes: ['dog', 'cat'],
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        batch.set(serviceRef, serviceData);
        console.log(`🛠️ Creating sample service: ${serviceData.name}`);
      } else {
        console.log(`ℹ️ Provider already has active services, skipping service creation`);
      }

      await batch.commit();
      console.log(`✅ Provider approved successfully!`);

      await loadProviders();
      toast.success(`Provider approved${!provider.hasActiveServices ? ' and service created' : ''}!`);
    } catch (error) {
      console.error('❌ Error approving provider:', error);
      toast.error(`Failed to approve provider: ${error.message}`);
    }
  };

  // Create sample service for provider
  const createSampleService = async (providerId: string) => {
    try {
      console.log(`🔄 Creating sample service for provider ${providerId}`);

      const provider = providers.find(p => p.id === providerId);
      if (!provider) {
        toast.error('Provider not found');
        return;
      }

      const serviceData = {
        providerId: providerId,
        name: provider.serviceType || 'Pet Care Service',
        description: `Professional ${provider.serviceType || 'pet care'} service by ${provider.businessName || provider.name}`,
        category: provider.serviceType || 'Pet Sitting',
        price: 50,
        duration: 60,
        petTypes: ['dog', 'cat'],
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      console.log(`📝 Service data:`, serviceData);

      const docRef = await addDoc(collection(db, 'services'), serviceData);
      console.log(`✅ Service created with ID: ${docRef.id}`);

      await loadProviders();
      toast.success(`Sample service "${serviceData.name}" created successfully!`);
    } catch (error) {
      console.error('❌ Error creating service:', error);
      toast.error(`Failed to create service: ${error.message}`);
    }
  };

  // Fix provider data (add missing fields)
  const fixProviderData = async (providerId: string) => {
    try {
      console.log(`🔄 Fixing provider data for ${providerId}`);

      const provider = providers.find(p => p.id === providerId);
      if (!provider) {
        toast.error('Provider not found');
        return;
      }

      const updates: any = {};
      const fixes: string[] = [];

      // Add missing status
      if (!provider.status) {
        updates.status = 'pending';
        fixes.push('status');
      }

      // Add missing fields
      if (!provider.businessName && provider.name) {
        updates.businessName = provider.name;
        fixes.push('businessName');
      } else if (!provider.businessName && !provider.name) {
        updates.businessName = 'Unnamed Business';
        fixes.push('businessName');
      }

      if (!provider.serviceType) {
        updates.serviceType = 'Pet Sitting';
        fixes.push('serviceType');
      }

      // Add missing email if not present
      if (!provider.email) {
        updates.email = '<EMAIL>';
        fixes.push('email');
      }

      if (Object.keys(updates).length > 0) {
        updates.updatedAt = new Date();
        console.log(`📝 Applying fixes:`, updates);

        await updateDoc(doc(db, 'providers', providerId), updates);
        console.log(`✅ Provider data fixed: ${fixes.join(', ')}`);

        await loadProviders();
        toast.success(`Provider data fixed! Added: ${fixes.join(', ')}`);
      } else {
        console.log(`ℹ️ Provider data is already complete`);
        toast.info('Provider data is already complete');
      }
    } catch (error) {
      console.error('❌ Error fixing provider data:', error);
      toast.error(`Failed to fix provider data: ${error.message}`);
    }
  };

  // Delete provider
  const deleteProvider = async (providerId: string) => {
    const provider = providers.find(p => p.id === providerId);
    const providerName = provider?.businessName || provider?.name || 'this provider';

    if (!confirm(`Are you sure you want to delete "${providerName}"? This action cannot be undone and will also delete all associated services.`)) {
      return;
    }

    try {
      console.log(`🔄 Deleting provider ${providerId} (${providerName})`);

      // Delete provider document
      await deleteDoc(doc(db, 'providers', providerId));
      console.log(`✅ Provider ${providerId} deleted successfully`);

      // Note: In a production app, you might want to also delete associated services
      // For now, we'll just delete the provider

      await loadProviders();
      toast.success(`Provider "${providerName}" deleted successfully`);
    } catch (error) {
      console.error('❌ Error deleting provider:', error);
      toast.error(`Failed to delete provider: ${error.message}`);
    }
  };

  // Bulk actions
  const handleBulkAction = async () => {
    if (!bulkAction || selectedProviders.length === 0) {
      toast.error('Please select providers and an action');
      return;
    }

    const actionText = bulkAction === 'approve' ? 'approve' : 'reject';
    if (!confirm(`Are you sure you want to ${actionText} ${selectedProviders.length} selected providers?`)) {
      return;
    }

    try {
      console.log(`🔄 Performing bulk ${actionText} on ${selectedProviders.length} providers`);

      const batch = writeBatch(db);

      selectedProviders.forEach(providerId => {
        const providerRef = doc(db, 'providers', providerId);
        if (bulkAction === 'approve') {
          batch.update(providerRef, { status: 'approved', updatedAt: new Date() });
        } else if (bulkAction === 'reject') {
          batch.update(providerRef, { status: 'rejected', updatedAt: new Date() });
        }
      });

      await batch.commit();
      console.log(`✅ Bulk ${actionText} completed successfully`);

      await loadProviders();
      setSelectedProviders([]);
      setBulkAction('');
      toast.success(`${selectedProviders.length} providers ${actionText}d successfully`);
    } catch (error) {
      console.error('❌ Error with bulk action:', error);
      toast.error(`Failed to perform bulk action: ${error.message}`);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'rejected': return <XCircle className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const handleProviderAction = async (providerId: string, action: string) => {
    console.log(`🎯 Handling action "${action}" for provider ${providerId}`);

    const provider = providers.find(p => p.id === providerId);
    if (!provider) {
      toast.error('Provider not found');
      return;
    }

    try {
      switch (action) {
        case 'view':
          console.log(`👁️ Viewing provider details for ${provider.businessName || provider.name}`);
          setSelectedProvider(provider);
          break;

        case 'edit':
          // TODO: Implement edit modal
          toast.info('Edit functionality coming soon');
          break;

        case 'approve':
          console.log(`✅ Approving provider ${provider.businessName || provider.name}`);
          await approveProviderWithService(providerId);
          break;

        case 'reject':
          console.log(`❌ Rejecting provider ${provider.businessName || provider.name}`);
          await updateProviderStatus(providerId, 'rejected');
          break;

        case 'delete':
          console.log(`🗑️ Deleting provider ${provider.businessName || provider.name}`);
          await deleteProvider(providerId);
          break;

        case 'fix':
          console.log(`🔧 Fixing data for provider ${provider.businessName || provider.name}`);
          await fixProviderData(providerId);
          break;

        case 'create-service':
          console.log(`🛠️ Creating service for provider ${provider.businessName || provider.name}`);
          await createSampleService(providerId);
          break;

        default:
          console.log('❓ Unknown action:', action);
          toast.error(`Unknown action: ${action}`);
      }
    } catch (error) {
      console.error(`❌ Error handling action "${action}":`, error);
      toast.error(`Failed to ${action} provider: ${error.message}`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4"></div>
          <p className="text-blue-600 font-medium text-lg">Loading providers...</p>
        </div>
      </div>
    );
  }

  const stats = [
    {
      name: 'Total Providers',
      value: providers.length.toLocaleString(),
      icon: Building2,
      change: '+12%',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600'
    },
    {
      name: 'Approved',
      value: providers.filter(p => p.status === 'approved').length.toLocaleString(),
      icon: CheckCircle,
      change: '+8%',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Pending',
      value: providers.filter(p => p.status === 'pending').length.toLocaleString(),
      icon: Clock,
      change: '+15%',
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600'
    },
    {
      name: 'Rejected',
      value: providers.filter(p => p.status === 'rejected').length.toLocaleString(),
      icon: XCircle,
      change: '-5%',
      bgColor: 'bg-red-50',
      iconColor: 'text-red-600'
    },
    {
      name: 'With Active Services',
      value: providers.filter(p => p.hasActiveServices).length.toLocaleString(),
      icon: Star,
      change: '+20%',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-600'
    },
    {
      name: 'Need Fixing',
      value: providers.filter(p => !p.status || !p.businessName || !p.serviceType).length.toLocaleString(),
      icon: AlertTriangle,
      change: '-10%',
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Modern Header */}
        <div className="backdrop-blur-xl bg-white/80 border border-blue-200/30 rounded-3xl p-8 shadow-2xl">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-2">
                <span className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-700 bg-clip-text text-transparent">
                  Provider Management
                </span>
              </h1>
              <p className="text-gray-600 text-lg">Manage service providers, approvals, and services</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={setupAdmin}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors"
                title="Setup Admin Access (Fix Permissions)"
              >
                <Zap className="h-4 w-4" />
                <span>Setup Admin</span>
              </button>
              <button
                onClick={runDebug}
                className="flex items-center space-x-2 px-4 py-2 border border-blue-200 text-blue-600 rounded-xl hover:bg-blue-50 transition-colors"
              >
                <Settings className="h-4 w-4" />
                <span>Debug DB</span>
              </button>
              <button
                onClick={loadProviders}
                className="flex items-center space-x-2 px-4 py-2 border border-blue-200 text-blue-600 rounded-xl hover:bg-blue-50 transition-colors"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors">
                <Download className="h-4 w-4" />
                <span>Export</span>
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="h-4 w-4" />
                <span>Add Provider</span>
              </button>
            </div>
          </div>
        </div>

        {/* Modern Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="backdrop-blur-xl bg-white/80 border border-blue-200/30 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group hover:scale-[1.02]">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                  <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
                </div>
                <div className={`flex items-center space-x-1 text-xs font-semibold ${
                  stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change.startsWith('+') ? (
                    <ArrowUpRight className="h-3 w-3" />
                  ) : (
                    <ArrowUpRight className="h-3 w-3 rotate-180" />
                  )}
                  <span>{stat.change}</span>
                </div>
              </div>
              <div>
                <h3 className="text-xs font-medium text-gray-500 mb-1">{stat.name}</h3>
                <p className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                  {stat.value}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Modern Search and Filters */}
        <div className="backdrop-blur-xl bg-white/80 border border-blue-200/30 rounded-2xl p-6 shadow-lg">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-500" />
              <input
                type="text"
                placeholder="Search providers by name, email, or services..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 rounded-xl border border-blue-200/50 bg-white/90 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md"
              />
            </div>

            <div className="flex gap-4">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-4 rounded-xl border border-blue-200/50 bg-white/90 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>

              <select
                value={filterService}
                onChange={(e) => setFilterService(e.target.value)}
                className="px-4 py-4 rounded-xl border border-blue-200/50 bg-white/90 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md"
              >
                <option value="all">All Services</option>
                <option value="grooming">Grooming</option>
                <option value="walking">Dog Walking</option>
                <option value="sitting">Pet Sitting</option>
                <option value="training">Training</option>
                <option value="veterinary">Veterinary</option>
                <option value="boarding">Pet Boarding</option>
                <option value="daycare">Pet Daycare</option>
              </select>

              {selectedProviders.length > 0 && (
                <div className="flex gap-2">
                  <select
                    value={bulkAction}
                    onChange={(e) => setBulkAction(e.target.value)}
                    className="px-4 py-4 rounded-xl border border-blue-200/50 bg-white/90 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                  >
                    <option value="">Bulk Actions</option>
                    <option value="approve">Approve Selected</option>
                    <option value="reject">Reject Selected</option>
                  </select>
                  <button
                    onClick={handleBulkAction}
                    disabled={!bulkAction}
                    className="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Apply
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Results Header */}
        <div className="backdrop-blur-xl bg-white/80 border border-blue-200/30 rounded-2xl p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-1">Provider Results</h2>
              <p className="text-gray-600">
                <span className="font-semibold text-blue-600">{filteredProviders.length}</span> providers found
                {selectedProviders.length > 0 && (
                  <span className="ml-4 text-blue-600">
                    ({selectedProviders.length} selected)
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={() => setSelectedProviders([])}
                className="text-gray-500 hover:text-gray-700 text-sm"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>

        {/* Modern Provider Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredProviders.map((provider) => (
            <div key={provider.id} className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group hover:scale-[1.02]">
              {/* Card Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={selectedProviders.includes(provider.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedProviders([...selectedProviders, provider.id]);
                      } else {
                        setSelectedProviders(selectedProviders.filter(id => id !== provider.id));
                      }
                    }}
                    className="rounded border-blue-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                    <span className="text-white font-bold text-sm">
                      {provider.businessName?.charAt(0) || provider.name?.charAt(0) || 'P'}
                    </span>
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-bold text-gray-900 group-hover:text-blue-700 transition-colors">
                        {provider.businessName || provider.name || 'Unnamed Provider'}
                      </h3>
                      {provider.verified && (
                        <Verified className="w-4 h-4 text-blue-500" />
                      )}
                      {provider.featured && (
                        <Award className="w-4 h-4 text-yellow-500" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{provider.email}</p>
                  </div>
                </div>

                <div className={`flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(provider.status || 'pending')}`}>
                  {getStatusIcon(provider.status || 'pending')}
                  <span className="capitalize">{provider.status || 'pending'}</span>
                </div>
              </div>
              {/* Provider Info */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Phone className="w-4 h-4 text-blue-500" />
                  <span>{provider.phone || 'No phone'}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="w-4 h-4 text-blue-500" />
                  <span>{provider.address || provider.location || provider.city || 'No location'}</span>
                </div>
                {provider.website && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Globe className="w-4 h-4 text-blue-500" />
                    <span>Website Available</span>
                  </div>
                )}
              </div>

              {/* Service Type & Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-xs text-gray-500 mb-1">Service Type</p>
                  <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                    {provider.serviceType || 'Not specified'}
                  </span>
                </div>
                <div>
                  <p className="text-xs text-gray-500 mb-1">Rating</p>
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="font-medium">{provider.rating || 'New'}</span>
                    {provider.reviewCount && (
                      <span className="text-gray-500 text-sm">({provider.reviewCount})</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Services Status */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-xs text-gray-500">Services</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    provider.hasActiveServices
                      ? 'bg-green-100 text-green-700'
                      : 'bg-red-100 text-red-700'
                  }`}>
                    {provider.hasActiveServices ? 'Has Services' : 'No Services'}
                  </span>
                </div>
                {provider.activeServices && provider.activeServices.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {provider.activeServices.slice(0, 3).map((service: any, index: number) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                        {service.name}
                      </span>
                    ))}
                    {provider.activeServices.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                        +{provider.activeServices.length - 3} more
                      </span>
                    )}
                  </div>
                )}
              </div>
              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleProviderAction(provider.id, 'view')}
                  className="flex items-center gap-1 px-3 py-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors text-sm"
                  title="View Details"
                >
                  <Eye className="w-4 h-4" />
                  <span>View</span>
                </button>

                {provider.status === 'pending' && (
                  <>
                    <button
                      onClick={() => handleProviderAction(provider.id, 'approve')}
                      className="flex items-center gap-1 px-3 py-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors text-sm"
                      title="Approve Provider"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>Approve</span>
                    </button>
                    <button
                      onClick={() => handleProviderAction(provider.id, 'reject')}
                      className="flex items-center gap-1 px-3 py-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors text-sm"
                      title="Reject Provider"
                    >
                      <XCircle className="w-4 h-4" />
                      <span>Reject</span>
                    </button>
                  </>
                )}

                {!provider.hasActiveServices && (
                  <button
                    onClick={() => handleProviderAction(provider.id, 'create-service')}
                    className="flex items-center gap-1 px-3 py-2 text-purple-600 hover:bg-purple-100 rounded-lg transition-colors text-sm"
                    title="Create Sample Service"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Service</span>
                  </button>
                )}

                {(!provider.status || !provider.businessName || !provider.serviceType) && (
                  <button
                    onClick={() => handleProviderAction(provider.id, 'fix')}
                    className="flex items-center gap-1 px-3 py-2 text-orange-600 hover:bg-orange-100 rounded-lg transition-colors text-sm"
                    title="Fix Missing Data"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Fix</span>
                  </button>
                )}

                <button
                  onClick={() => handleProviderAction(provider.id, 'delete')}
                  className="flex items-center gap-1 px-3 py-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors text-sm"
                  title="Delete Provider"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Delete</span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredProviders.length === 0 && (
          <div className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-2xl p-12 text-center shadow-lg">
            <Building2 className="w-16 h-16 text-blue-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-900 mb-2">No providers found</h3>
            <p className="text-gray-600 mb-6">
              {providers.length === 0
                ? "No providers in the database yet. Add some providers to get started."
                : "Try adjusting your search criteria or filters."
              }
            </p>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setFilterStatus('all');
                  setFilterService('all');
                }}
                className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
              >
                Clear Filters
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="px-6 py-3 border border-blue-600 text-blue-600 rounded-xl hover:bg-blue-50 transition-colors"
              >
                Add Provider
              </button>
            </div>
          </div>
        )}

        {/* Enhanced Provider Detail Modal */}
        {selectedProvider && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-2">Provider Details</h2>
                  <p className="text-gray-600">{selectedProvider.businessName || selectedProvider.name || 'Unnamed Provider'}</p>
                </div>
                <button
                  onClick={() => setSelectedProvider(null)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Basic Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                        <p className="text-gray-900 p-3 bg-gray-50 rounded-lg">{selectedProvider.businessName || 'Not provided'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <p className="text-gray-900 p-3 bg-gray-50 rounded-lg">{selectedProvider.email}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <p className="text-gray-900 p-3 bg-gray-50 rounded-lg">{selectedProvider.phone || 'Not provided'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <p className="text-gray-900 p-3 bg-gray-50 rounded-lg">
                          {selectedProvider.address || selectedProvider.location || selectedProvider.city || 'Not provided'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Service Information */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Information</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Service Type</label>
                        <p className="text-gray-900 p-3 bg-gray-50 rounded-lg">{selectedProvider.serviceType || 'Not specified'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedProvider.status || 'pending')}`}>
                            {getStatusIcon(selectedProvider.status || 'pending')}
                            {selectedProvider.status || 'pending'}
                          </span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Rating</label>
                        <div className="p-3 bg-gray-50 rounded-lg flex items-center gap-2">
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <span>{selectedProvider.rating || 'No rating yet'}</span>
                          {selectedProvider.reviewCount && (
                            <span className="text-gray-500">({selectedProvider.reviewCount} reviews)</span>
                          )}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Verification</label>
                        <div className="p-3 bg-gray-50 rounded-lg flex items-center gap-2">
                          {selectedProvider.verified ? (
                            <>
                              <Verified className="w-4 h-4 text-blue-500" />
                              <span className="text-green-600">Verified</span>
                            </>
                          ) : (
                            <span className="text-gray-500">Not verified</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Active Services */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Services</h3>
                {selectedProvider.activeServices && selectedProvider.activeServices.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedProvider.activeServices.map((service: any, index: number) => (
                      <div key={index} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 className="font-semibold text-gray-900">{service.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                        <div className="flex items-center justify-between mt-3">
                          <span className="text-lg font-bold text-blue-600">${service.price}</span>
                          <span className="text-sm text-gray-500">{service.duration} min</span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {service.petTypes?.map((petType: string, idx: number) => (
                            <span key={idx} className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                              {petType}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-8 bg-gray-50 rounded-lg text-center">
                    <p className="text-gray-500">No active services found</p>
                    <button
                      onClick={() => {
                        setSelectedProvider(null);
                        handleProviderAction(selectedProvider.id, 'create-service');
                      }}
                      className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Create Sample Service
                    </button>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="mt-8 flex flex-wrap gap-3 pt-6 border-t border-gray-200">
                {selectedProvider.status === 'pending' && (
                  <>
                    <button
                      onClick={() => {
                        setSelectedProvider(null);
                        handleProviderAction(selectedProvider.id, 'approve');
                      }}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <CheckCircle className="w-4 h-4" />
                      Approve Provider
                    </button>
                    <button
                      onClick={() => {
                        setSelectedProvider(null);
                        handleProviderAction(selectedProvider.id, 'reject');
                      }}
                      className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      <XCircle className="w-4 h-4" />
                      Reject Provider
                    </button>
                  </>
                )}

                {!selectedProvider.hasActiveServices && (
                  <button
                    onClick={() => {
                      setSelectedProvider(null);
                      handleProviderAction(selectedProvider.id, 'create-service');
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    Add Service
                  </button>
                )}

                {(!selectedProvider.status || !selectedProvider.businessName || !selectedProvider.serviceType) && (
                  <button
                    onClick={() => {
                      setSelectedProvider(null);
                      handleProviderAction(selectedProvider.id, 'fix');
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                    Fix Data
                  </button>
                )}

                <button
                  onClick={() => {
                    setSelectedProvider(null);
                    handleProviderAction(selectedProvider.id, 'delete');
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete Provider
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
