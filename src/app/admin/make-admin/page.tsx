'use client';

import { useEffect, useState } from 'react';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase/config';

export default function MakeAdminPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isError, setIsError] = useState(false);

  const makeMeAdmin = async () => {
    try {
      setIsLoading(true);
      setMessage('');
      setIsError(false);

      const user = auth.currentUser;

      if (!user) {
        throw new Error('No user is currently signed in! Please sign in first.');
      }

      // Update the user's role in Firestore
      const userData = {
        role: 'admin',
        name: user.displayName || 'Admin User',
        email: user.email,
        updatedAt: new Date().toISOString()
      };

      console.log('🔧 Setting admin role for user:', user.uid, userData);

      await setDoc(
        doc(db, 'users', user.uid),
        userData,
        { merge: true }
      );

      // Force token refresh to update claims
      await user.getIdToken(true);

      console.log('✅ Admin role set successfully');

      setMessage('Success! You are now an admin. Redirecting to admin dashboard...');
      
      // Redirect to admin dashboard after a short delay
      setTimeout(() => {
        window.location.href = '/admin';
      }, 2000);
      
    } catch (error) {
      console.error('Error making user admin:', error);
      setMessage(error instanceof Error ? error.message : 'An unknown error occurred');
      setIsError(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold text-center mb-6">Admin Access</h1>
        
        {message ? (
          <div className={`p-4 rounded-md ${isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {message}
          </div>
        ) : (
          <>
            <p className="mb-6 text-gray-600 text-center">
              Click the button below to grant admin privileges to your account.
            </p>
            
            <div className="flex justify-center">
              <button
                onClick={makeMeAdmin}
                disabled={isLoading}
                className={`px-6 py-2 rounded-md text-white font-medium ${
                  isLoading 
                    ? 'bg-gray-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isLoading ? 'Processing...' : 'Make Me Admin'}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
