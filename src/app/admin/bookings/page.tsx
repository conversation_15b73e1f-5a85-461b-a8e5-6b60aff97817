'use client';

import { useState } from 'react';
import { 
  Calendar, 
  Search, 
  Filter, 
  Eye, 
  Edit3, 
  Trash2, 
  <PERSON><PERSON><PERSON>cle,
  XCircle,
  Clock,
  DollarSign,
  User,
  Building2,
  MapPin,
  Phone,
  Download,
  RefreshCw,
  AlertTriangle,
  MoreVertical
} from 'lucide-react';

interface Booking {
  id: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  providerName: string;
  providerType: string;
  service: string;
  date: string;
  time: string;
  duration: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  amount: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  location: string;
  notes?: string;
  createdAt: string;
}

const mockBookings: Booking[] = [
  {
    id: 'BK001',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    providerName: 'Dr. <PERSON>',
    providerType: 'Veterinary',
    service: 'Health Checkup',
    date: '2024-07-30',
    time: '10:00 AM',
    duration: '30 minutes',
    status: 'confirmed',
    amount: 80,
    paymentStatus: 'paid',
    location: 'Los Angeles, CA',
    notes: 'Annual checkup for Max (Golden Retriever)',
    createdAt: '2024-07-28'
  },
  {
    id: 'BK002',
    customerName: 'Mike Davis',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    providerName: 'Happy Paws Grooming',
    providerType: 'Grooming',
    service: 'Full Grooming',
    date: '2024-07-29',
    time: '2:00 PM',
    duration: '2 hours',
    status: 'in_progress',
    amount: 65,
    paymentStatus: 'paid',
    location: 'San Francisco, CA',
    notes: 'First time grooming for Luna (Poodle)',
    createdAt: '2024-07-27'
  },
  {
    id: 'BK003',
    customerName: 'Emily Wilson',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    providerName: 'Cozy Pet Lodge',
    providerType: 'Pet Hotel',
    service: 'Overnight Boarding',
    date: '2024-08-01',
    time: '6:00 PM',
    duration: '3 days',
    status: 'pending',
    amount: 180,
    paymentStatus: 'pending',
    location: 'New York, NY',
    notes: 'Weekend boarding for Charlie (Beagle)',
    createdAt: '2024-07-28'
  },
  {
    id: 'BK004',
    customerName: 'David Brown',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    providerName: 'Elite Pet Training',
    providerType: 'Training',
    service: 'Basic Training',
    date: '2024-07-31',
    time: '11:00 AM',
    duration: '1 hour',
    status: 'completed',
    amount: 75,
    paymentStatus: 'paid',
    location: 'Miami, FL',
    notes: 'Puppy training session for Bella (Labrador)',
    createdAt: '2024-07-25'
  },
  {
    id: 'BK005',
    customerName: 'Lisa Garcia',
    customerEmail: '<EMAIL>',
    customerPhone: '+****************',
    providerName: 'Pawsome Dog Walking',
    providerType: 'Dog Walking',
    service: 'Daily Walk',
    date: '2024-07-28',
    time: '3:00 PM',
    duration: '30 minutes',
    status: 'cancelled',
    amount: 25,
    paymentStatus: 'refunded',
    location: 'Chicago, IL',
    notes: 'Cancelled due to weather',
    createdAt: '2024-07-28'
  }
];

const bookingStatuses = ['All Status', 'Pending', 'Confirmed', 'In Progress', 'Completed', 'Cancelled'];
const paymentStatuses = ['All Payments', 'Pending', 'Paid', 'Refunded'];
const serviceTypes = ['All Services', 'Veterinary', 'Grooming', 'Pet Hotel', 'Training', 'Dog Walking'];

export default function BookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>(mockBookings);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [selectedPayment, setSelectedPayment] = useState('All Payments');
  const [selectedService, setSelectedService] = useState('All Services');
  const [showFilters, setShowFilters] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      case 'confirmed': return 'bg-blue-100 text-blue-700';
      case 'in_progress': return 'bg-purple-100 text-purple-700';
      case 'completed': return 'bg-green-100 text-green-700';
      case 'cancelled': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getPaymentColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      case 'paid': return 'bg-green-100 text-green-700';
      case 'refunded': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'confirmed': return <CheckCircle className="w-4 h-4" />;
      case 'in_progress': return <AlertTriangle className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         booking.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         booking.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         booking.id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'All Status' || 
                         booking.status.toLowerCase() === selectedStatus.toLowerCase().replace(' ', '_');
    const matchesPayment = selectedPayment === 'All Payments' || 
                          booking.paymentStatus.toLowerCase() === selectedPayment.toLowerCase();
    const matchesService = selectedService === 'All Services' || booking.providerType === selectedService;
    
    return matchesSearch && matchesStatus && matchesPayment && matchesService;
  });

  const handleBookingAction = (bookingId: string, action: string) => {
    console.log(`${action} booking ${bookingId}`);
    // Implement booking actions here
  };

  const totalRevenue = bookings.reduce((sum, booking) => 
    booking.paymentStatus === 'paid' ? sum + booking.amount : sum, 0
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Bookings Management</h1>
          <p className="text-cool-600">Monitor and manage all service bookings</p>
        </div>
        
        <div className="flex gap-3">
          <button className="btn-outline flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button className="btn-outline flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <Calendar className="w-6 h-6 text-primary-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{bookings.length}</h3>
          <p className="text-cool-600">Total Bookings</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-yellow-100 rounded-xl">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {bookings.filter(b => b.status === 'pending').length}
          </h3>
          <p className="text-cool-600">Pending</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <CheckCircle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {bookings.filter(b => b.status === 'confirmed').length}
          </h3>
          <p className="text-cool-600">Confirmed</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {bookings.filter(b => b.status === 'completed').length}
          </h3>
          <p className="text-cool-600">Completed</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-warm-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-warm-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">${totalRevenue}</h3>
          <p className="text-cool-600">Total Revenue</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
            <input
              type="text"
              placeholder="Search bookings by customer, provider, service, or booking ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            />
          </div>
          
          <div className="flex gap-4">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {bookingStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <select
              value={selectedPayment}
              onChange={(e) => setSelectedPayment(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {paymentStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <select
              value={selectedService}
              onChange={(e) => setSelectedService(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {serviceTypes.map(service => (
                <option key={service} value={service}>{service}</option>
              ))}
            </select>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-outline flex items-center gap-2"
            >
              <Filter className="w-5 h-5" />
              Filters
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/30">
          <div className="flex items-center justify-between">
            <p className="text-cool-700">
              <span className="font-bold">{filteredBookings.length}</span> bookings found
            </p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Booking ID</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Customer</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Provider</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Service</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Date & Time</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Payment</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/30">
              {filteredBookings.map((booking) => (
                <tr key={booking.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-cool-800">{booking.id}</p>
                      <p className="text-sm text-cool-600">{booking.createdAt}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-cool-800">{booking.customerName}</p>
                      <p className="text-sm text-cool-600">{booking.customerEmail}</p>
                      <p className="text-sm text-cool-600">{booking.customerPhone}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-cool-800">{booking.providerName}</p>
                      <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded-full text-xs">
                        {booking.providerType}
                      </span>
                      <div className="flex items-center gap-1 text-sm text-cool-600 mt-1">
                        <MapPin className="w-3 h-3" />
                        <span>{booking.location}</span>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-cool-800">{booking.service}</p>
                      <p className="text-sm text-cool-600">{booking.duration}</p>
                      {booking.notes && (
                        <p className="text-xs text-cool-500 mt-1">{booking.notes}</p>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-cool-800">{booking.date}</p>
                      <p className="text-sm text-cool-600">{booking.time}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium w-fit ${getStatusColor(booking.status)}`}>
                      {getStatusIcon(booking.status)}
                      <span className="capitalize">{booking.status.replace('_', ' ')}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-bold text-cool-800">${booking.amount}</p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPaymentColor(booking.paymentStatus)}`}>
                        {booking.paymentStatus}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleBookingAction(booking.id, 'view')}
                        className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleBookingAction(booking.id, 'edit')}
                        className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                        title="Edit Booking"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      {booking.status === 'pending' && (
                        <button
                          onClick={() => handleBookingAction(booking.id, 'confirm')}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors duration-200"
                          title="Confirm Booking"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleBookingAction(booking.id, 'cancel')}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200"
                        title="Cancel Booking"
                      >
                        <XCircle className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredBookings.length === 0 && (
          <div className="p-12 text-center">
            <Calendar className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No bookings found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search criteria or filters.</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedStatus('All Status');
                setSelectedPayment('All Payments');
                setSelectedService('All Services');
              }}
              className="btn-primary"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
