'use client';

import { useState } from 'react';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase/config';
import { useRouter } from 'next/navigation';

export default function AdminSetup() {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isError, setIsError] = useState(false);
  const router = useRouter();

  const createAdminUser = async () => {
    setIsLoading(true);
    setMessage('');
    setIsError(false);

    try {
      // Admin credentials from .env.local
      const adminEmail = '<EMAIL>';
      const adminPassword = 'P0l@r!$2025';
      const adminName = 'Fetchly Administrator';

      console.log('🔧 Creating admin user...');

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
      const user = userCredential.user;

      console.log('✅ Admin user created in Auth:', user.uid);

      // Create user document in Firestore with admin role
      const userData = {
        email: adminEmail,
        name: adminName,
        role: 'admin',
        joinedDate: new Date().toISOString(),
        fetchlyBalance: 0,
        rewardPoints: 0,
        avatar: '/favicon.png',
        banner: '/fetchlylogo.png',
        verified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await setDoc(doc(db, 'users', user.uid), userData);

      console.log('✅ Admin user document created in Firestore');

      setMessage('✅ Admin user created successfully! Redirecting to admin dashboard...');
      
      // Redirect to admin dashboard after a short delay
      setTimeout(() => {
        router.push('/admin');
      }, 2000);

    } catch (error: any) {
      console.error('❌ Error creating admin user:', error);
      
      if (error.code === 'auth/email-already-in-use') {
        setMessage('⚠️ Admin user already exists. Trying to sign in and set admin role...');
        
        try {
          // User exists, sign in and set admin role
          const userCredential = await signInWithEmailAndPassword(auth, '<EMAIL>', 'P0l@r!$2025');
          const user = userCredential.user;

          // Update user document with admin role
          const userData = {
            email: '<EMAIL>',
            name: 'Fetchly Administrator',
            role: 'admin',
            updatedAt: new Date().toISOString()
          };

          await setDoc(doc(db, 'users', user.uid), userData, { merge: true });

          setMessage('✅ Admin role set successfully! Redirecting to admin dashboard...');
          
          setTimeout(() => {
            router.push('/admin');
          }, 2000);

        } catch (signInError: any) {
          console.error('❌ Error signing in existing user:', signInError);
          setMessage(`❌ Error: ${signInError.message}`);
          setIsError(true);
        }
      } else {
        setMessage(`❌ Error creating admin user: ${error.message}`);
        setIsError(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-md">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Setup</h1>
          <p className="text-gray-600">Create your admin user account</p>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Admin Credentials</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Name:</strong> Fetchly Administrator</p>
              <p><strong>Role:</strong> Admin</p>
            </div>
          </div>

          <button
            onClick={createAdminUser}
            disabled={isLoading}
            className="w-full py-3 px-4 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors"
          >
            {isLoading ? 'Creating Admin User...' : 'Create Admin User'}
          </button>

          {message && (
            <div className={`p-4 rounded-lg ${isError ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {message}
            </div>
          )}

          <div className="text-center">
            <p className="text-sm text-gray-500">
              After creation, you can access the admin dashboard at{' '}
              <code className="bg-gray-100 px-1 rounded">/admin</code>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
