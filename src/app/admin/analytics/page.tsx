'use client';

import { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  DollarSign, 
  Calendar, 
  Star,
  Eye,
  Download,
  Filter,
  RefreshCw,
  Building2,
  Heart,
  MessageSquare,
  Clock,
  Target,
  Activity
} from 'lucide-react';

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(false);

  const timeRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' }
  ];

  const refreshData = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1000);
  };

  const topProviders = [
    { name: 'Dr. <PERSON>', revenue: 8450, bookings: 89, rating: 4.8 },
    { name: 'Happy Paws Grooming', revenue: 6750, bookings: 127, rating: 4.9 },
    { name: 'Cozy Pet Lodge', revenue: 9240, bookings: 156, rating: 4.7 },
    { name: 'Elite Pet Training', revenue: 4680, bookings: 78, rating: 4.9 },
    { name: 'Pawsome Dog Walking', revenue: 5075, bookings: 203, rating: 4.6 }
  ];

  const serviceStats = [
    { service: 'Grooming', bookings: 342, revenue: 17100, growth: 15 },
    { service: 'Veterinary', bookings: 289, revenue: 23120, growth: 8 },
    { service: 'Pet Hotel', bookings: 234, revenue: 14040, growth: 22 },
    { service: 'Training', bookings: 156, revenue: 9360, growth: 12 },
    { service: 'Dog Walking', bookings: 213, revenue: 5325, growth: -3 }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Analytics Dashboard</h1>
          <p className="text-cool-600">Track platform performance and user engagement</p>
        </div>
        
        <div className="flex gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
          >
            {timeRanges.map(range => (
              <option key={range.value} value={range.value}>{range.label}</option>
            ))}
          </select>
          
          <button
            onClick={refreshData}
            disabled={isLoading}
            className="btn-outline flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          
          <button className="btn-primary flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <Users className="w-6 h-6 text-primary-600" />
            </div>
            <div className="flex items-center gap-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+12%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">2,847</h3>
          <p className="text-cool-600">Total Users</p>
          <p className="text-xs text-cool-500 mt-2">+342 new this month</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-secondary-100 rounded-xl">
              <Calendar className="w-6 h-6 text-secondary-600" />
            </div>
            <div className="flex items-center gap-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+8%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">1,234</h3>
          <p className="text-cool-600">Total Bookings</p>
          <p className="text-xs text-cool-500 mt-2">+98 this month</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-warm-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-warm-600" />
            </div>
            <div className="flex items-center gap-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+15%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">$89,432</h3>
          <p className="text-cool-600">Total Revenue</p>
          <p className="text-xs text-cool-500 mt-2">+$12,543 this month</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-100 rounded-xl">
              <Star className="w-6 h-6 text-accent-600" />
            </div>
            <div className="flex items-center gap-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+0.2</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">4.8</h3>
          <p className="text-cool-600">Avg Rating</p>
          <p className="text-xs text-cool-500 mt-2">From 1,847 reviews</p>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-cool-800">Revenue Trends</h2>
            <div className="flex items-center gap-2 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+15% vs last period</span>
            </div>
          </div>
          
          <div className="h-64 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="w-16 h-16 text-primary-400 mx-auto mb-4" />
              <p className="text-cool-600">Revenue chart visualization</p>
              <p className="text-cool-500 text-sm">Chart component would be integrated here</p>
            </div>
          </div>
        </div>

        {/* User Growth Chart */}
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-cool-800">User Growth</h2>
            <div className="flex items-center gap-2 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+12% vs last period</span>
            </div>
          </div>
          
          <div className="h-64 bg-gradient-to-r from-secondary-50 to-accent-50 rounded-xl flex items-center justify-center">
            <div className="text-center">
              <Users className="w-16 h-16 text-secondary-400 mx-auto mb-4" />
              <p className="text-cool-600">User growth chart visualization</p>
              <p className="text-cool-500 text-sm">Chart component would be integrated here</p>
            </div>
          </div>
        </div>
      </div>

      {/* Service Performance & Top Providers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Service Performance */}
        <div className="glass-card rounded-2xl p-6">
          <h2 className="text-xl font-bold text-cool-800 mb-6">Service Performance</h2>
          <div className="space-y-4">
            {serviceStats.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                <div>
                  <h3 className="font-medium text-cool-800">{service.service}</h3>
                  <p className="text-sm text-cool-600">{service.bookings} bookings</p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-cool-800">${service.revenue.toLocaleString()}</p>
                  <div className={`flex items-center gap-1 text-sm ${
                    service.growth >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {service.growth >= 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                    <span>{service.growth >= 0 ? '+' : ''}{service.growth}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Providers */}
        <div className="glass-card rounded-2xl p-6">
          <h2 className="text-xl font-bold text-cool-800 mb-6">Top Providers</h2>
          <div className="space-y-4">
            {topProviders.map((provider, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-sm">
                      {provider.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium text-cool-800">{provider.name}</h3>
                    <div className="flex items-center gap-2 text-sm text-cool-600">
                      <Star className="w-3 h-3 text-yellow-500" />
                      <span>{provider.rating}</span>
                      <span>•</span>
                      <span>{provider.bookings} bookings</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-cool-800">${provider.revenue.toLocaleString()}</p>
                  <p className="text-sm text-cool-600">Revenue</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <Activity className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-cool-800">Platform Activity</h3>
              <p className="text-cool-600 text-sm">Real-time engagement</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-cool-600">Active Users</span>
              <span className="font-medium text-cool-800">1,247</span>
            </div>
            <div className="flex justify-between">
              <span className="text-cool-600">Online Providers</span>
              <span className="font-medium text-cool-800">89</span>
            </div>
            <div className="flex justify-between">
              <span className="text-cool-600">Live Bookings</span>
              <span className="font-medium text-cool-800">23</span>
            </div>
          </div>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <Target className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-cool-800">Conversion Rates</h3>
              <p className="text-cool-600 text-sm">Performance metrics</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-cool-600">Visitor to User</span>
              <span className="font-medium text-cool-800">12.4%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-cool-600">User to Booking</span>
              <span className="font-medium text-cool-800">8.7%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-cool-600">Booking Completion</span>
              <span className="font-medium text-cool-800">94.2%</span>
            </div>
          </div>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-purple-100 rounded-xl">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-cool-800">Response Times</h3>
              <p className="text-cool-600 text-sm">Average response metrics</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-cool-600">Provider Response</span>
              <span className="font-medium text-cool-800">1.2 hrs</span>
            </div>
            <div className="flex justify-between">
              <span className="text-cool-600">Support Response</span>
              <span className="font-medium text-cool-800">15 mins</span>
            </div>
            <div className="flex justify-between">
              <span className="text-cool-600">Booking Confirmation</span>
              <span className="font-medium text-cool-800">3 mins</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
