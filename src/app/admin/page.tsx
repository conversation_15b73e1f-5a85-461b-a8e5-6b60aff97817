'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useAdmin } from '@/contexts/AdminContext';
import { toast } from 'sonner';
import { 
  Users, Building2, Calendar, DollarSign, MessageSquare, 
  Star, BarChart3, Shield, Settings, AlertTriangle, 
  RefreshCw, Zap, ArrowUpRight, ArrowDownRight, Activity, Eye, CheckCircle 
} from 'lucide-react';
import { collection, query, orderBy, limit, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Types
interface DashboardData {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  totalProviders: number;
  activeProviders: number;
  pendingProviders: number;
  totalBookings: number;
  completedBookings: number;
  pendingBookings: number;
  totalRevenue: number;
  monthlyRevenue: number;
  revenueChange: number;
  recentUsers: Array<{
    id: string;
    name: string;
    email: string;
    joined: Date;
    status: 'active' | 'inactive' | 'suspended';
  }>;
  recentProviders: Array<{
    id: string;
    name: string;
    email: string;
    joined: Date;
    status: 'active' | 'pending' | 'rejected';
    rating: number;
  }>;
  recentBookings: Array<{
    id: string;
    user: string;
    provider: string;
    date: Date;
    status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
    amount: number;
  }>;
  recentReviews: Array<{
    id: string;
    user: string;
    provider: string;
    rating: number;
    comment: string;
    date: Date;
  }>;
  systemHealth: {
    status: 'operational' | 'degraded' | 'outage';
    lastChecked: Date;
    responseTime: number;
    databaseStatus: 'online' | 'slow' | 'offline';
    storageUsage: number;
  };
}

// Helper function to format time
const formatTimeAgo = (timestamp: Date | { toDate: () => Date } | null): string => {
  if (!timestamp) return 'Just now';
  const date = timestamp instanceof Date ? timestamp : timestamp.toDate();
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
};

const statsConfig = [
  {
    title: 'Total Users',
    key: 'totalUsers',
    icon: Users,
    color: 'primary',
    format: (value: number) => value.toLocaleString()
  },
  {
    title: 'Active Providers',
    key: 'activeProviders',
    icon: Building2,
    color: 'secondary',
    format: (value: number) => value.toLocaleString()
  },
  {
    title: 'Monthly Bookings',
    key: 'monthlyBookings',
    icon: Calendar,
    color: 'accent',
    format: (value: number) => value.toLocaleString()
  },
  {
    title: 'Revenue',
    key: 'totalRevenue',
    icon: DollarSign,
    color: 'warm',
    format: (value: number) => `$${value.toLocaleString()}`
  }
];

export default function AdminPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const {
    totalUsers,
    activeProviders,
    monthlyBookings,
    totalRevenue,
    recentActivity,
    pendingApprovals,
    isLoading: adminLoading,
    error,
    refreshData
  } = useAdmin();
  
  const [isLoading, setIsLoading] = useState(adminLoading);

  useEffect(() => {
    const checkAuth = async () => {
      if (adminLoading) return;
      
      try {
        if (!isAuthenticated || !user) {
          // Redirect to admin signin
          router.push('/admin/signin');
          return;
        }
        
        if (user.role !== 'admin') {
          // Not an admin, redirect to regular dashboard
          router.push('/dashboard');
          return;
        }
        
        // If we get here, user is authenticated and is an admin
        setIsLoading(false);
      } catch (error) {
        console.error('Error checking authentication:', error);
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user, isAuthenticated, adminLoading, router]);

  // Show loading while checking auth or loading admin data
  if (isLoading || adminLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-mint-50 via-sky-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-aqua-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  // Dashboard data state
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    totalUsers: 0,
    activeUsers: 0,
    newUsers: 0,
    totalProviders: 0,
    activeProviders: 0,
    pendingProviders: 0,
    totalBookings: 0,
    completedBookings: 0,
    pendingBookings: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    revenueChange: 0,
    recentUsers: [] as any[],
    recentProviders: [] as any[],
    recentBookings: [] as any[],
    recentReviews: [] as any[],
    systemHealth: {
      status: 'operational',
      lastChecked: new Date(),
      responseTime: 0,
      databaseStatus: 'online',
      storageUsage: 0
    }
  });
  const [loadingDashboard, setLoadingDashboard] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (adminLoading) return;
      
      try {
        // Fetch users
        const usersSnapshot = await getDocs(collection(db, 'users'));
        const users = usersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        // Fetch providers
        const providersSnapshot = await getDocs(collection(db, 'providers'));
        const providers = providersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        // Fetch bookings
        const bookingsSnapshot = await getDocs(collection(db, 'bookings'));
        const bookings = bookingsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        // Fetch reviews
        const reviewsSnapshot = await getDocs(collection(db, 'reviews'));
        const reviews = reviewsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        setDashboardData({
          totalUsers: users.length,
          activeUsers: users.filter((user: any) => user.status === 'active').length,
          newUsers: users.filter((user: any) => {
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return new Date(user.joined) > weekAgo;
          }).length,
          totalProviders: providers.length,
          activeProviders: providers.filter((p: any) => p.status === 'active').length,
          pendingProviders: providers.filter((p: any) => p.status === 'pending').length,
          totalBookings: bookings.length,
          completedBookings: bookings.filter((b: any) => b.status === 'completed').length,
          pendingBookings: bookings.filter((b: any) => b.status === 'pending').length,
          totalRevenue: bookings.reduce((sum: number, b: any) => sum + (b.amount || 0), 0),
          monthlyRevenue: bookings
            .filter((b: any) => {
              const monthAgo = new Date();
              monthAgo.setMonth(monthAgo.getMonth() - 1);
              return new Date(b.date) > monthAgo;
            })
            .reduce((sum: number, b: any) => sum + (b.amount || 0), 0),
          revenueChange: 0, // You might want to calculate this based on previous period
          recentUsers: users
            .sort((a: any, b: any) => new Date(b.joined).getTime() - new Date(a.joined).getTime())
            .slice(0, 5)
            .map((user: any) => ({
              id: user.id,
              name: user.name || 'Unknown User',
              email: user.email || 'No email',
              joined: user.joined ? new Date(user.joined) : new Date(),
              status: user.status || 'inactive',
            })),
          recentProviders: providers
            .sort((a: any, b: any) => new Date(b.joined).getTime() - new Date(a.joined).getTime())
            .slice(0, 5)
            .map((provider: any) => ({
              id: provider.id,
              name: provider.businessName || 'Unknown Provider',
              email: provider.contactEmail || 'No email',
              joined: provider.joined ? new Date(provider.joined) : new Date(),
              status: provider.status || 'pending',
              rating: provider.rating || 0,
            })),
          recentBookings: bookings
            .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .slice(0, 5)
            .map((booking: any) => ({
              id: booking.id,
              user: booking.userName || 'Unknown User',
              provider: booking.providerName || 'Unknown Provider',
              date: booking.date ? new Date(booking.date) : new Date(),
              status: booking.status || 'pending',
              amount: booking.amount || 0,
            })),
          recentReviews: reviews
            .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .slice(0, 5)
            .map((review: any) => ({
              id: review.id,
              user: review.userName || 'Anonymous',
              provider: review.providerName || 'Unknown Provider',
              rating: review.rating || 0,
              comment: review.comment || 'No comment',
              date: review.date ? new Date(review.date) : new Date(),
            })),
          systemHealth: {
            status: 'operational',
            lastChecked: new Date(),
            responseTime: 0,
            databaseStatus: 'online',
            storageUsage: 0,
          },
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setLoadingDashboard(false);
      }
    };

    if (isAuthenticated && user?.role === 'admin') {
      fetchData();
    }
  }, [adminLoading, refreshData, isAuthenticated, user?.role]);

  const stats = [
    {
      name: 'Total Users',
      value: totalUsers.toLocaleString(),
      icon: Users,
      change: '+12%',
      changeType: 'positive',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600'
    },
    {
      name: 'Active Providers',
      value: activeProviders.toLocaleString(),
      icon: Building2,
      change: '+8%',
      changeType: 'positive',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Total Posts',
      value: dashboardData.totalPosts.toLocaleString(),
      icon: MessageSquare,
      change: '+23%',
      changeType: 'positive',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-600'
    },
    {
      name: 'Total Bookings',
      value: dashboardData.totalBookings.toLocaleString(),
      icon: Calendar,
      change: '+15%',
      changeType: 'positive',
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600'
    },
    {
      name: 'Total Payments',
      value: dashboardData.totalPayments.toLocaleString(),
      icon: DollarSign,
      change: '+18%',
      changeType: 'positive',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Total Reviews',
      value: dashboardData.totalReviews.toLocaleString(),
      icon: Star,
      change: '+10%',
      changeType: 'positive',
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-600'
    }
  ];

  const quickActions = [
    { name: 'Manage Users', href: '/admin/users', icon: Users, color: 'bg-blue-500' },
    { name: 'Review Providers', href: '/admin/providers', icon: Building2, color: 'bg-green-500' },
    { name: 'View Bookings', href: '/admin/bookings', icon: Calendar, color: 'bg-purple-500' },
    { name: 'Analytics', href: '/admin/analytics', icon: BarChart3, color: 'bg-indigo-500' },
    { name: 'Security', href: '/admin/security', icon: Shield, color: 'bg-red-500' },
    { name: 'Settings', href: '/admin/settings', icon: Settings, color: 'bg-gray-500' }
  ];

  const handleApprove = async (approvalId: string) => {
    try {
      await refreshData();
      toast.success('Approval processed successfully');
    } catch (error) {
      console.error('Error approving:', error);
      toast.error('Failed to process approval');
    }
  };

  const handleReview = (approvalId: string) => {
    console.log('Review approval:', approvalId);
  };
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-200 border-t-primary-600 mx-auto mb-4"></div>
          <p className="text-primary-600 font-medium text-lg">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="text-center bg-white rounded-2xl shadow-xl p-8 max-w-md">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Dashboard Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6 bg-gradient-to-br from-primary-50 to-secondary-50 min-h-screen">
      {/* Welcome Header */}
      <div className="bg-white rounded-2xl shadow-xl p-8 border border-white/20">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Welcome back, {user?.name || 'Admin'}! 👋
            </h1>
            <p className="text-gray-600 mt-2 text-lg">Here's what's happening with Fetchly today</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={refreshData}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 font-medium"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <div className="flex items-center space-x-2 bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-xl">
              <Zap className="h-5 w-5" />
              <span className="font-medium">Admin Dashboard</span>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-2xl shadow-xl p-6 border border-white/20 hover:shadow-2xl transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                <stat.icon className={`h-8 w-8 ${stat.iconColor}`} />
              </div>
              <div className={`flex items-center space-x-1 text-sm font-semibold ${
                stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.changeType === 'positive' ? (
                  <ArrowUpRight className="h-4 w-4" />
                ) : (
                  <ArrowDownRight className="h-4 w-4" />
                )}
                <span>{stat.change}</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">{stat.name}</h3>
              <p className="text-3xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">
                {stat.value}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Users */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Users</h3>
              <Link href="/admin/users" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {dashboardData.recentUsers.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentUsers.slice(0, 5).map((user: any, index: number) => (
                  <div key={user.id || index} className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {user.name?.charAt(0) || 'U'}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{user.name || 'Unknown User'}</p>
                      <p className="text-sm text-gray-500 truncate">{user.email}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'admin' ? 'bg-red-100 text-red-800' :
                      user.role === 'provider' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {user.role === 'pet_owner' ? 'Pet Owner' :
                       user.role === 'provider' ? 'Provider' : 'Admin'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent users</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Providers */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Providers</h3>
              <Link href="/admin/providers" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {dashboardData.recentProviders.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentProviders.slice(0, 5).map((provider: any, index: number) => (
                  <div key={provider.id || index} className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                      <Building2 className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{provider.businessName || provider.name || 'Unknown Provider'}</p>
                      <p className="text-sm text-gray-500 truncate">{provider.location || 'No location'}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      provider.status === 'approved' ? 'bg-green-100 text-green-800' :
                      provider.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {provider.status || 'Pending'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent providers</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Bookings */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Bookings</h3>
              <Link href="/admin/bookings" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {dashboardData.recentBookings.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentBookings.slice(0, 5).map((booking: any, index: number) => (
                  <div key={booking.id || index} className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{booking.serviceName || 'Service Booking'}</p>
                      <p className="text-sm text-gray-500 truncate">{booking.customerName || 'Unknown Customer'}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                      booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {booking.status || 'Pending'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent bookings</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-2xl shadow-xl p-8 border border-white/20">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              href={action.href}
              className="flex items-center space-x-3 p-4 rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group"
            >
              <div className={`p-2 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform`}>
                <action.icon className="h-5 w-5" />
              </div>
              <span className="font-medium text-gray-700 group-hover:text-primary-700">{action.name}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Activity & Approvals Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Activity</h3>
              <Link href="/admin/activity" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {recentActivity.length > 0 ? (
              <div className="space-y-4">
                {recentActivity.slice(0, 5).map((activity, index) => (
                  <div key={activity.id || index} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                        <Activity className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.message || 'New activity recorded'}</p>
                      <p className="text-sm text-gray-500">{formatTimeAgo(activity.timestamp)}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent activity</p>
              </div>
            )}
          </div>
        </div>

        {/* Pending Approvals */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Pending Approvals</h3>
              <Link href="/admin/approvals" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {pendingApprovals.length > 0 ? (
              <div className="space-y-4">
                {pendingApprovals.slice(0, 5).map((approval, index) => (
                  <div key={approval.id || index} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                          <AlertTriangle className="h-5 w-5 text-yellow-600" />
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{approval.name || 'Provider Application'}</p>
                        <p className="text-sm text-gray-500">{approval.type || 'Awaiting review'}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleApprove(approval.id)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      >
                        <CheckCircle className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleReview(approval.id)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      >
                        <Eye className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No pending approvals</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="glass-card rounded-2xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <button className="p-4 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Users className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Manage Users</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Building2 className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Providers</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Calendar className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Bookings</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-warm-500 to-warm-600 text-white hover:shadow-lg transition-all duration-300 group">
            <DollarSign className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Payments</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Star className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Reviews</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Activity className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Analytics</span>
          </button>
        </div>
      </div>

      {/* Analytics Overview */}
      <div className="bg-white rounded-2xl shadow-xl border border-white/20">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-gray-900">Analytics Overview</h3>
            <Link href="/admin/analytics" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
              View Details
            </Link>
          </div>
        </div>
        <div className="p-6">
          <div className="flex items-center justify-center h-64 bg-gradient-to-br from-primary-50 to-secondary-50 rounded-xl border-2 border-dashed border-primary-200">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-primary-400 mx-auto mb-4" />
              <p className="text-primary-600 font-medium text-lg">Analytics Dashboard</p>
              <p className="text-gray-500 mt-2">Detailed charts and metrics coming soon</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
