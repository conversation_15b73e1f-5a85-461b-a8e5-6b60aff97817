'use client';

import {
  Users,
  Building2,
  Calendar,
  DollarSign,
  TrendingUp,
  Activity,
  Star,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3,
  Shield,
  MessageSquare,
  Settings,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Eye,
  UserCheck,
  Bell,
  Search,
  Filter
} from 'lucide-react';
import { useAdmin } from '@/contexts/AdminContext';
import { useAuth } from '@/contexts/AuthContext';
import { useState, useEffect } from 'react';
import { collection, getDocs, query, where, orderBy, limit, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Link from 'next/link';
import { toast } from 'react-hot-toast';

// Helper function to format time
const formatTimeAgo = (timestamp: any) => {
  if (!timestamp) return 'Just now';
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
};

const statsConfig = [
  {
    title: 'Total Users',
    key: 'totalUsers',
    icon: Users,
    color: 'primary',
    format: (value: number) => value.toLocaleString()
  },
  {
    title: 'Active Providers',
    key: 'activeProviders',
    icon: Building2,
    color: 'secondary',
    format: (value: number) => value.toLocaleString()
  },
  {
    title: 'Monthly Bookings',
    key: 'monthlyBookings',
    icon: Calendar,
    color: 'accent',
    format: (value: number) => value.toLocaleString()
  },
  {
    title: 'Revenue',
    key: 'totalRevenue',
    icon: DollarSign,
    color: 'warm',
    format: (value: number) => `$${value.toLocaleString()}`
  }
];

export default function AdminDashboard() {
  const { user } = useAuth();
  const {
    totalUsers,
    activeProviders,
    monthlyBookings,
    totalRevenue,
    recentActivity,
    pendingApprovals,
    isLoading,
    error,
    refreshData
  } = useAdmin();

  // Additional real-time data states
  const [dashboardData, setDashboardData] = useState({
    totalPosts: 0,
    totalBookings: 0,
    totalPayments: 0,
    totalReviews: 0,
    recentUsers: [] as any[],
    recentProviders: [] as any[],
    recentBookings: [] as any[],
    recentReviews: [] as any[],
    systemHealth: 'good'
  });
  const [loadingDashboard, setLoadingDashboard] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoadingDashboard(true);

      // Load posts count
      const postsSnapshot = await getDocs(collection(db, 'posts'));
      const totalPosts = postsSnapshot.size;

      // Load bookings count
      const bookingsSnapshot = await getDocs(collection(db, 'bookings'));
      const totalBookings = bookingsSnapshot.size;

      // Load payments count
      const paymentsSnapshot = await getDocs(collection(db, 'payments'));
      const totalPayments = paymentsSnapshot.size;

      // Load reviews count
      const reviewsSnapshot = await getDocs(collection(db, 'reviews'));
      const totalReviews = reviewsSnapshot.size;

      // Load recent users (last 5)
      const recentUsersQuery = query(
        collection(db, 'users'),
        orderBy('joinedDate', 'desc'),
        limit(5)
      );
      const recentUsersSnapshot = await getDocs(recentUsersQuery);
      const recentUsers = recentUsersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Load recent providers (last 5)
      const recentProvidersQuery = query(
        collection(db, 'providers'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      const recentProvidersSnapshot = await getDocs(recentProvidersQuery);
      const recentProviders = recentProvidersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Load recent bookings (last 5)
      const recentBookingsQuery = query(
        collection(db, 'bookings'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      const recentBookingsSnapshot = await getDocs(recentBookingsQuery);
      const recentBookings = recentBookingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Load recent reviews (last 5)
      const recentReviewsQuery = query(
        collection(db, 'reviews'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      const recentReviewsSnapshot = await getDocs(recentReviewsQuery);
      const recentReviews = recentReviewsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setDashboardData({
        totalPosts,
        totalBookings,
        totalPayments,
        totalReviews,
        recentUsers,
        recentProviders,
        recentBookings,
        recentReviews,
        systemHealth: 'good'
      });

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoadingDashboard(false);
    }
  };

  const stats = [
    {
      name: 'Total Users',
      value: totalUsers.toLocaleString(),
      icon: Users,
      change: '+12%',
      changeType: 'positive',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600'
    },
    {
      name: 'Active Providers',
      value: activeProviders.toLocaleString(),
      icon: Building2,
      change: '+8%',
      changeType: 'positive',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Total Posts',
      value: dashboardData.totalPosts.toLocaleString(),
      icon: MessageSquare,
      change: '+23%',
      changeType: 'positive',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-600'
    },
    {
      name: 'Total Bookings',
      value: dashboardData.totalBookings.toLocaleString(),
      icon: Calendar,
      change: '+15%',
      changeType: 'positive',
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600'
    },
    {
      name: 'Total Payments',
      value: dashboardData.totalPayments.toLocaleString(),
      icon: DollarSign,
      change: '+18%',
      changeType: 'positive',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Total Reviews',
      value: dashboardData.totalReviews.toLocaleString(),
      icon: Star,
      change: '+10%',
      changeType: 'positive',
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-600'
    }
  ];

  const quickActions = [
    { name: 'Manage Users', href: '/admin/users', icon: Users, color: 'bg-blue-500' },
    { name: 'Review Providers', href: '/admin/providers', icon: Building2, color: 'bg-green-500' },
    { name: 'View Bookings', href: '/admin/bookings', icon: Calendar, color: 'bg-purple-500' },
    { name: 'Analytics', href: '/admin/analytics', icon: BarChart3, color: 'bg-indigo-500' },
    { name: 'Security', href: '/admin/security', icon: Shield, color: 'bg-red-500' },
    { name: 'Settings', href: '/admin/settings', icon: Settings, color: 'bg-gray-500' }
  ];

  const handleApprove = async (approvalId: string) => {
    try {
      await refreshData();
      toast.success('Approval processed successfully');
    } catch (error) {
      console.error('Error approving:', error);
      toast.error('Failed to process approval');
    }
  };

  const handleReview = (approvalId: string) => {
    console.log('Review approval:', approvalId);
  };
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary-200 border-t-primary-600 mx-auto mb-4"></div>
          <p className="text-primary-600 font-medium text-lg">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="text-center bg-white rounded-2xl shadow-xl p-8 max-w-md">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Dashboard Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6 bg-gradient-to-br from-primary-50 to-secondary-50 min-h-screen">
      {/* Welcome Header */}
      <div className="bg-white rounded-2xl shadow-xl p-8 border border-white/20">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
              Welcome back, {user?.name || 'Admin'}! 👋
            </h1>
            <p className="text-gray-600 mt-2 text-lg">Here's what's happening with Fetchly today</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={refreshData}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 font-medium"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <div className="flex items-center space-x-2 bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-xl">
              <Zap className="h-5 w-5" />
              <span className="font-medium">Admin Dashboard</span>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-2xl shadow-xl p-6 border border-white/20 hover:shadow-2xl transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                <stat.icon className={`h-8 w-8 ${stat.iconColor}`} />
              </div>
              <div className={`flex items-center space-x-1 text-sm font-semibold ${
                stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.changeType === 'positive' ? (
                  <ArrowUpRight className="h-4 w-4" />
                ) : (
                  <ArrowDownRight className="h-4 w-4" />
                )}
                <span>{stat.change}</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">{stat.name}</h3>
              <p className="text-3xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">
                {stat.value}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Users */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Users</h3>
              <Link href="/admin/users" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {dashboardData.recentUsers.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentUsers.slice(0, 5).map((user: any, index: number) => (
                  <div key={user.id || index} className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {user.name?.charAt(0) || 'U'}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{user.name || 'Unknown User'}</p>
                      <p className="text-sm text-gray-500 truncate">{user.email}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'admin' ? 'bg-red-100 text-red-800' :
                      user.role === 'provider' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {user.role === 'pet_owner' ? 'Pet Owner' :
                       user.role === 'provider' ? 'Provider' : 'Admin'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent users</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Providers */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Providers</h3>
              <Link href="/admin/providers" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {dashboardData.recentProviders.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentProviders.slice(0, 5).map((provider: any, index: number) => (
                  <div key={provider.id || index} className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                      <Building2 className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{provider.businessName || provider.name || 'Unknown Provider'}</p>
                      <p className="text-sm text-gray-500 truncate">{provider.location || 'No location'}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      provider.status === 'approved' ? 'bg-green-100 text-green-800' :
                      provider.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {provider.status || 'Pending'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent providers</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Bookings */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Bookings</h3>
              <Link href="/admin/bookings" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {dashboardData.recentBookings.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.recentBookings.slice(0, 5).map((booking: any, index: number) => (
                  <div key={booking.id || index} className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{booking.serviceName || 'Service Booking'}</p>
                      <p className="text-sm text-gray-500 truncate">{booking.customerName || 'Unknown Customer'}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                      booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {booking.status || 'Pending'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent bookings</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-2xl shadow-xl p-8 border border-white/20">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              href={action.href}
              className="flex items-center space-x-3 p-4 rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group"
            >
              <div className={`p-2 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform`}>
                <action.icon className="h-5 w-5" />
              </div>
              <span className="font-medium text-gray-700 group-hover:text-primary-700">{action.name}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Activity & Approvals Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Recent Activity</h3>
              <Link href="/admin/activity" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {recentActivity.length > 0 ? (
              <div className="space-y-4">
                {recentActivity.slice(0, 5).map((activity, index) => (
                  <div key={activity.id || index} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-colors">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                        <Activity className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.message || 'New activity recorded'}</p>
                      <p className="text-sm text-gray-500">{formatTimeAgo(activity.timestamp)}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent activity</p>
              </div>
            )}
          </div>
        </div>

        {/* Pending Approvals */}
        <div className="bg-white rounded-2xl shadow-xl border border-white/20">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">Pending Approvals</h3>
              <Link href="/admin/approvals" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                View All
              </Link>
            </div>
          </div>
          <div className="p-6">
            {pendingApprovals.length > 0 ? (
              <div className="space-y-4">
                {pendingApprovals.slice(0, 5).map((approval, index) => (
                  <div key={approval.id || index} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                          <AlertTriangle className="h-5 w-5 text-yellow-600" />
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{approval.name || 'Provider Application'}</p>
                        <p className="text-sm text-gray-500">{approval.type || 'Awaiting review'}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleApprove(approval.id)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      >
                        <CheckCircle className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleReview(approval.id)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      >
                        <Eye className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No pending approvals</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="glass-card rounded-2xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <button className="p-4 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Users className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Manage Users</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Building2 className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Providers</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Calendar className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Bookings</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-warm-500 to-warm-600 text-white hover:shadow-lg transition-all duration-300 group">
            <DollarSign className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Payments</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Star className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Reviews</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Activity className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Analytics</span>
          </button>
        </div>
      </div>

      {/* Analytics Overview */}
      <div className="bg-white rounded-2xl shadow-xl border border-white/20">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-gray-900">Analytics Overview</h3>
            <Link href="/admin/analytics" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
              View Details
            </Link>
          </div>
        </div>
        <div className="p-6">
          <div className="flex items-center justify-center h-64 bg-gradient-to-br from-primary-50 to-secondary-50 rounded-xl border-2 border-dashed border-primary-200">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-primary-400 mx-auto mb-4" />
              <p className="text-primary-600 font-medium text-lg">Analytics Dashboard</p>
              <p className="text-gray-500 mt-2">Detailed charts and metrics coming soon</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
