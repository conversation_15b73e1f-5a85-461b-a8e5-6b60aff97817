'use client';

import { useState, useEffect } from 'react';
import {
  Users,
  Building2,
  Calendar,
  DollarSign,
  TrendingUp,
  Activity,
  Star,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3,
  Shield,
  MessageSquare,
  Settings,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Eye,
  UserCheck,
  Bell,
  Search,
  Filter
} from 'lucide-react';
import { collection, getDocs, query, where, orderBy, limit, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';

// Helper function to format time
const formatTimeAgo = (timestamp: any) => {
  if (!timestamp) return 'Just now';
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
  return `${Math.floor(diffInMinutes / 1440)}d ago`;
};

export default function AdminDashboard() {
  const { user } = useAuth();
  
  // Real-time data states
  const [dashboardData, setDashboardData] = useState({
    totalUsers: 0,
    totalProviders: 0,
    totalPosts: 0,
    totalBookings: 0,
    totalPayments: 0,
    totalReviews: 0,
    totalRevenue: 0,
    recentUsers: [] as any[],
    recentProviders: [] as any[],
    recentBookings: [] as any[],
    recentReviews: [] as any[],
    recentActivity: [] as any[],
    pendingApprovals: [] as any[],
    systemHealth: 'good'
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Loading real admin dashboard data from Firebase...');

      // Load all collections in parallel
      const [
        usersSnapshot,
        providersSnapshot,
        postsSnapshot,
        bookingsSnapshot,
        paymentsSnapshot,
        reviewsSnapshot,
        servicesSnapshot
      ] = await Promise.all([
        getDocs(collection(db, 'users')),
        getDocs(collection(db, 'providers')),
        getDocs(collection(db, 'posts')),
        getDocs(collection(db, 'bookings')),
        getDocs(collection(db, 'payments')),
        getDocs(collection(db, 'reviews')),
        getDocs(collection(db, 'services'))
      ]);

      // Calculate totals
      const totalUsers = usersSnapshot.size;
      const totalProviders = providersSnapshot.size;
      const totalPosts = postsSnapshot.size;
      const totalBookings = bookingsSnapshot.size;
      const totalPayments = paymentsSnapshot.size;
      const totalReviews = reviewsSnapshot.size;

      // Calculate total revenue from payments
      let totalRevenue = 0;
      paymentsSnapshot.docs.forEach(doc => {
        const payment = doc.data();
        if (payment.amount && payment.status === 'completed') {
          totalRevenue += payment.amount;
        }
      });

      // Load recent data with proper queries
      const [
        recentUsersSnapshot,
        recentProvidersSnapshot,
        recentBookingsSnapshot,
        recentReviewsSnapshot
      ] = await Promise.all([
        getDocs(query(collection(db, 'users'), orderBy('joinedDate', 'desc'), limit(5))),
        getDocs(query(collection(db, 'providers'), orderBy('createdAt', 'desc'), limit(5))),
        getDocs(query(collection(db, 'bookings'), orderBy('createdAt', 'desc'), limit(5))),
        getDocs(query(collection(db, 'reviews'), orderBy('createdAt', 'desc'), limit(5)))
      ]);

      // Process recent data
      const recentUsers = recentUsersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const recentProviders = recentProvidersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const recentBookings = recentBookingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const recentReviews = recentReviewsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Get pending provider approvals
      const pendingProvidersSnapshot = await getDocs(
        query(collection(db, 'providers'), where('status', '==', 'pending'))
      );
      const pendingApprovals = pendingProvidersSnapshot.docs.map(doc => ({
        id: doc.id,
        type: 'Provider Application',
        name: doc.data().businessName || doc.data().name || 'Unknown Provider',
        ...doc.data()
      }));

      // Create recent activity from various sources
      const recentActivity = [
        ...recentUsers.slice(0, 2).map(user => ({
          id: `user-${user.id}`,
          message: `New user registered: ${user.name || user.email}`,
          timestamp: user.joinedDate,
          type: 'user'
        })),
        ...recentProviders.slice(0, 2).map(provider => ({
          id: `provider-${provider.id}`,
          message: `New provider application: ${provider.businessName || provider.name}`,
          timestamp: provider.createdAt,
          type: 'provider'
        })),
        ...recentBookings.slice(0, 2).map(booking => ({
          id: `booking-${booking.id}`,
          message: `New booking: ${booking.serviceName || 'Service booking'}`,
          timestamp: booking.createdAt,
          type: 'booking'
        }))
      ].sort((a, b) => {
        const aTime = a.timestamp?.toDate ? a.timestamp.toDate() : new Date(a.timestamp || 0);
        const bTime = b.timestamp?.toDate ? b.timestamp.toDate() : new Date(b.timestamp || 0);
        return bTime.getTime() - aTime.getTime();
      }).slice(0, 5);

      setDashboardData({
        totalUsers,
        totalProviders,
        totalPosts,
        totalBookings,
        totalPayments,
        totalReviews,
        totalRevenue,
        recentUsers,
        recentProviders,
        recentBookings,
        recentReviews,
        recentActivity,
        pendingApprovals,
        systemHealth: 'good'
      });

      console.log('✅ Admin dashboard data loaded:', {
        totalUsers,
        totalProviders,
        totalPosts,
        totalBookings,
        totalPayments,
        totalReviews,
        totalRevenue,
        pendingApprovals: pendingApprovals.length
      });

      toast.success('Dashboard data loaded successfully');

    } catch (error) {
      console.error('❌ Error loading admin dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const refreshData = () => {
    loadDashboardData();
  };

  const stats = [
    {
      name: 'Total Users',
      value: dashboardData.totalUsers.toLocaleString(),
      icon: Users,
      change: '+12%',
      changeType: 'positive',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600'
    },
    {
      name: 'Active Providers',
      value: dashboardData.totalProviders.toLocaleString(),
      icon: Building2,
      change: '+8%',
      changeType: 'positive',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Community Posts',
      value: dashboardData.totalPosts.toLocaleString(),
      icon: MessageSquare,
      change: '+23%',
      changeType: 'positive',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-600'
    },
    {
      name: 'Total Bookings',
      value: dashboardData.totalBookings.toLocaleString(),
      icon: Calendar,
      change: '+15%',
      changeType: 'positive',
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600'
    },
    {
      name: 'Total Revenue',
      value: `$${dashboardData.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      change: '+18%',
      changeType: 'positive',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      name: 'Total Reviews',
      value: dashboardData.totalReviews.toLocaleString(),
      icon: Star,
      change: '+10%',
      changeType: 'positive',
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-600'
    }
  ];

  const quickActions = [
    { name: 'Manage Users', href: '/admin/users', icon: Users, color: 'bg-blue-500' },
    { name: 'Review Providers', href: '/admin/providers', icon: Building2, color: 'bg-green-500' },
    { name: 'View Bookings', href: '/admin/bookings', icon: Calendar, color: 'bg-purple-500' },
    { name: 'Analytics', href: '/admin/analytics', icon: BarChart3, color: 'bg-indigo-500' },
    { name: 'Security', href: '/admin/security', icon: Shield, color: 'bg-red-500' },
    { name: 'Settings', href: '/admin/settings', icon: Settings, color: 'bg-gray-500' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-mint-50 via-sky-50 to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-aqua-200 border-t-aqua-600 mx-auto mb-4"></div>
          <p className="text-aqua-600 font-medium text-lg">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-mint-50 via-sky-50 to-blue-50">
        <div className="text-center backdrop-blur-xl bg-white/90 border border-red-200/30 rounded-3xl shadow-2xl p-8 max-w-md">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Dashboard Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={refreshData}
            className="px-6 py-3 bg-gradient-to-r from-aqua-500 to-sky-500 hover:from-aqua-600 hover:to-sky-600 text-white rounded-xl font-medium transition-all"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-mint-50 via-sky-50 to-blue-50">
      <div className="space-y-8 p-6">
        {/* Welcome Header */}
        <div className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-2xl p-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-aqua-600 to-sky-600 bg-clip-text text-transparent">
                Welcome back, {user?.name || 'Admin'}! 👋
              </h1>
              <p className="text-gray-600 mt-2 text-lg">Here's what's happening with Fetchly today</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={refreshData}
                className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-aqua-500 to-sky-500 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 font-medium"
              >
                <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <div className="flex items-center space-x-2 bg-gradient-to-r from-aqua-500 to-sky-500 text-white px-6 py-3 rounded-xl">
                <Zap className="h-5 w-5" />
                <span className="font-medium">Admin Dashboard</span>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-xl p-6 hover:shadow-2xl transition-all duration-300 group">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                  <stat.icon className={`h-8 w-8 ${stat.iconColor}`} />
                </div>
                <div className={`flex items-center space-x-1 text-sm font-semibold ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.changeType === 'positive' ? (
                    <ArrowUpRight className="h-4 w-4" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4" />
                  )}
                  <span>{stat.change}</span>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">{stat.name}</h3>
                <p className="text-3xl font-bold text-gray-900 group-hover:text-aqua-600 transition-colors">
                  {stat.value}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* System Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Users */}
          <div className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-xl">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-900">Recent Users</h3>
                <Link href="/admin/users" className="text-aqua-600 hover:text-aqua-700 font-medium text-sm">
                  View All
                </Link>
              </div>
            </div>
            <div className="p-6">
              {dashboardData.recentUsers.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.recentUsers.slice(0, 5).map((user: any, index: number) => (
                    <div key={user.id || index} className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-aqua-500 to-sky-500 flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {user.name?.charAt(0) || 'U'}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{user.name || 'Unknown User'}</p>
                        <p className="text-sm text-gray-500 truncate">{user.email}</p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.role === 'admin' ? 'bg-red-100 text-red-800' :
                        user.role === 'provider' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {user.role === 'pet_owner' ? 'Pet Owner' :
                         user.role === 'provider' ? 'Provider' : 'Admin'}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No recent users</p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Providers */}
          <div className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-xl">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-900">Recent Providers</h3>
                <Link href="/admin/providers" className="text-aqua-600 hover:text-aqua-700 font-medium text-sm">
                  View All
                </Link>
              </div>
            </div>
            <div className="p-6">
              {dashboardData.recentProviders.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.recentProviders.slice(0, 5).map((provider: any, index: number) => (
                    <div key={provider.id || index} className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                        <Building2 className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{provider.businessName || provider.name || 'Unknown Provider'}</p>
                        <p className="text-sm text-gray-500 truncate">{provider.city || 'No location'}</p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        provider.status === 'approved' ? 'bg-green-100 text-green-800' :
                        provider.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {provider.status || 'Pending'}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No recent providers</p>
                </div>
              )}
            </div>
          </div>

          {/* Recent Bookings */}
          <div className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-xl">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-900">Recent Bookings</h3>
                <Link href="/admin/bookings" className="text-aqua-600 hover:text-aqua-700 font-medium text-sm">
                  View All
                </Link>
              </div>
            </div>
            <div className="p-6">
              {dashboardData.recentBookings.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.recentBookings.slice(0, 5).map((booking: any, index: number) => (
                    <div key={booking.id || index} className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                        <Calendar className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{booking.serviceName || 'Service Booking'}</p>
                        <p className="text-sm text-gray-500 truncate">{booking.customerName || 'Unknown Customer'}</p>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {booking.status || 'Pending'}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No recent bookings</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-xl p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickActions.map((action) => (
              <Link
                key={action.name}
                href={action.href}
                className="flex items-center space-x-3 p-4 rounded-xl border border-gray-200 hover:border-aqua-300 hover:bg-aqua-50 transition-all duration-200 group"
              >
                <div className={`p-2 rounded-lg ${action.color} text-white group-hover:scale-110 transition-transform`}>
                  <action.icon className="h-5 w-5" />
                </div>
                <span className="font-medium text-gray-700 group-hover:text-aqua-700">{action.name}</span>
              </Link>
            ))}
          </div>
        </div>

        {/* Activity & Approvals Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Activity */}
          <div className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-xl">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-900">Recent Activity</h3>
                <span className="text-aqua-600 font-medium text-sm">Live Updates</span>
              </div>
            </div>
            <div className="p-6">
              {dashboardData.recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.recentActivity.slice(0, 5).map((activity, index) => (
                    <div key={activity.id || index} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 transition-colors">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-gradient-to-r from-aqua-500 to-sky-500 flex items-center justify-center">
                          <Activity className="h-5 w-5 text-white" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                        <p className="text-sm text-gray-500">{formatTimeAgo(activity.timestamp)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No recent activity</p>
                </div>
              )}
            </div>
          </div>

          {/* Pending Approvals */}
          <div className="backdrop-blur-xl bg-white/90 border border-white/20 rounded-3xl shadow-xl">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-900">Pending Approvals</h3>
                <Link href="/admin/providers" className="text-aqua-600 hover:text-aqua-700 font-medium text-sm">
                  View All
                </Link>
              </div>
            </div>
            <div className="p-6">
              {dashboardData.pendingApprovals.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.pendingApprovals.slice(0, 5).map((approval, index) => (
                    <div key={approval.id || index} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                            <AlertTriangle className="h-5 w-5 text-yellow-600" />
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{approval.name}</p>
                          <p className="text-sm text-gray-500">{approval.type}</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Link
                          href={`/admin/providers`}
                          className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        >
                          <CheckCircle className="h-5 w-5" />
                        </Link>
                        <Link
                          href={`/admin/providers`}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          <Eye className="h-5 w-5" />
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No pending approvals</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
