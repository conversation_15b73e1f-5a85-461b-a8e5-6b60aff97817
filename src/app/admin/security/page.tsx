'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Eye, 
  EyeOff, 
  Key, 
  Lock, 
  Unlock,
  UserX,
  Activity,
  Globe,
  Smartphone,
  Monitor,
  RefreshCw,
  Download,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

interface SecurityEvent {
  id: string;
  type: 'login' | 'failed_login' | 'password_change' | 'account_locked' | 'suspicious_activity';
  user: string;
  email: string;
  ipAddress: string;
  location: string;
  device: string;
  timestamp: string;
  status: 'resolved' | 'investigating' | 'blocked';
}

const mockSecurityEvents: SecurityEvent[] = [
  {
    id: 'SEC001',
    type: 'failed_login',
    user: '<PERSON>',
    email: '<EMAIL>',
    ipAddress: '*************',
    location: 'San Francisco, CA',
    device: 'Chrome on Windows',
    timestamp: '2024-07-28 14:30:00',
    status: 'resolved'
  },
  {
    id: 'SEC002',
    type: 'suspicious_activity',
    user: 'Unknown',
    email: '<EMAIL>',
    ipAddress: '************',
    location: 'Unknown Location',
    device: 'Bot/Crawler',
    timestamp: '2024-07-28 13:15:00',
    status: 'blocked'
  },
  {
    id: 'SEC003',
    type: 'login',
    user: 'Dr. <PERSON> Chen',
    email: '<EMAIL>',
    ipAddress: '*********',
    location: 'Los Angeles, CA',
    device: 'Safari on macOS',
    timestamp: '2024-07-28 12:45:00',
    status: 'resolved'
  },
  {
    id: 'SEC004',
    type: 'account_locked',
    user: 'Mike Davis',
    email: '<EMAIL>',
    ipAddress: '***********',
    location: 'New York, NY',
    device: 'Firefox on Linux',
    timestamp: '2024-07-28 11:20:00',
    status: 'investigating'
  }
];

export default function SecurityPage() {
  const [events, setEvents] = useState<SecurityEvent[]>(mockSecurityEvents);
  const [showApiKey, setShowApiKey] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);
  const [loginNotifications, setLoginNotifications] = useState(true);
  const [sessionTimeout, setSessionTimeout] = useState('30');

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'login': return 'bg-green-100 text-green-700';
      case 'failed_login': return 'bg-yellow-100 text-yellow-700';
      case 'password_change': return 'bg-blue-100 text-blue-700';
      case 'account_locked': return 'bg-red-100 text-red-700';
      case 'suspicious_activity': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved': return 'bg-green-100 text-green-700';
      case 'investigating': return 'bg-yellow-100 text-yellow-700';
      case 'blocked': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login': return <CheckCircle className="w-4 h-4" />;
      case 'failed_login': return <XCircle className="w-4 h-4" />;
      case 'password_change': return <Key className="w-4 h-4" />;
      case 'account_locked': return <Lock className="w-4 h-4" />;
      case 'suspicious_activity': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Security Management</h1>
          <p className="text-cool-600">Monitor security events and manage platform security settings</p>
        </div>
        
        <div className="flex gap-3">
          <button className="btn-outline flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button className="btn-outline flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Log
          </button>
        </div>
      </div>

      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <Shield className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">Secure</h3>
          <p className="text-cool-600">Platform Status</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-yellow-100 rounded-xl">
              <AlertTriangle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {events.filter(e => e.status === 'investigating').length}
          </h3>
          <p className="text-cool-600">Active Threats</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-red-100 rounded-xl">
              <UserX className="w-6 h-6 text-red-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {events.filter(e => e.status === 'blocked').length}
          </h3>
          <p className="text-cool-600">Blocked IPs</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <Activity className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{events.length}</h3>
          <p className="text-cool-600">Security Events</p>
        </div>
      </div>

      {/* Security Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Authentication Settings */}
        <div className="glass-card rounded-2xl p-6">
          <h2 className="text-xl font-bold text-cool-800 mb-6">Authentication Settings</h2>
          
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-cool-800">Two-Factor Authentication</h3>
                <p className="text-sm text-cool-600">Require 2FA for all admin accounts</p>
              </div>
              <button
                onClick={() => setTwoFactorEnabled(!twoFactorEnabled)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  twoFactorEnabled ? 'bg-primary-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    twoFactorEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-cool-800">Login Notifications</h3>
                <p className="text-sm text-cool-600">Email alerts for new logins</p>
              </div>
              <button
                onClick={() => setLoginNotifications(!loginNotifications)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  loginNotifications ? 'bg-primary-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    loginNotifications ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">
                Session Timeout (minutes)
              </label>
              <select
                value={sessionTimeout}
                onChange={(e) => setSessionTimeout(e.target.value)}
                className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="15">15 minutes</option>
                <option value="30">30 minutes</option>
                <option value="60">1 hour</option>
                <option value="120">2 hours</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">
                API Key
              </label>
              <div className="flex gap-2">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  value="sk_live_1234567890abcdef"
                  readOnly
                  className="flex-1 px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none"
                />
                <button
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="p-2 text-cool-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
                >
                  {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
                <button className="btn-outline">
                  <RefreshCw className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Access Control */}
        <div className="glass-card rounded-2xl p-6">
          <h2 className="text-xl font-bold text-cool-800 mb-6">Access Control</h2>
          
          <div className="space-y-4">
            <div className="p-4 bg-white/50 rounded-xl">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-cool-800">Admin Panel Access</h3>
                <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Active</span>
              </div>
              <p className="text-sm text-cool-600">Restricted to admin role users only</p>
            </div>

            <div className="p-4 bg-white/50 rounded-xl">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-cool-800">Provider Dashboard</h3>
                <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Active</span>
              </div>
              <p className="text-sm text-cool-600">Available to verified providers</p>
            </div>

            <div className="p-4 bg-white/50 rounded-xl">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-cool-800">API Access</h3>
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">Limited</span>
              </div>
              <p className="text-sm text-cool-600">Rate limited to 1000 requests/hour</p>
            </div>

            <div className="p-4 bg-white/50 rounded-xl">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-cool-800">File Uploads</h3>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-sm">Restricted</span>
              </div>
              <p className="text-sm text-cool-600">Max 10MB, images only</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Security Events */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/30">
          <h2 className="text-xl font-bold text-cool-800">Recent Security Events</h2>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Event</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">User</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Location</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Device</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Time</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/30">
              {events.map((event) => (
                <tr key={event.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium w-fit ${getEventTypeColor(event.type)}`}>
                      {getEventIcon(event.type)}
                      <span className="capitalize">{event.type.replace('_', ' ')}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-cool-800">{event.user}</p>
                      <p className="text-sm text-cool-600">{event.email}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="text-cool-800">{event.location}</p>
                      <p className="text-sm text-cool-600">{event.ipAddress}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      {event.device.includes('Chrome') && <Monitor className="w-4 h-4 text-cool-500" />}
                      {event.device.includes('Safari') && <Monitor className="w-4 h-4 text-cool-500" />}
                      {event.device.includes('Mobile') && <Smartphone className="w-4 h-4 text-cool-500" />}
                      {event.device.includes('Bot') && <Globe className="w-4 h-4 text-cool-500" />}
                      <span className="text-cool-800">{event.device}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="text-cool-800">{event.timestamp}</p>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(event.status)}`}>
                      {event.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
