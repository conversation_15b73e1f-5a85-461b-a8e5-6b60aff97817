'use client';

import { useState } from 'react';
import { 
  MessageSquare, 
  Search, 
  Filter, 
  Eye, 
  Edit3, 
  Trash2, 
  Star,
  CheckCircle,
  XCircle,
  Flag,
  ThumbsUp,
  ThumbsDown,
  Calendar,
  User,
  Building2
} from 'lucide-react';

interface Review {
  id: string;
  customerName: string;
  customerEmail: string;
  providerName: string;
  providerType: string;
  service: string;
  rating: number;
  title: string;
  comment: string;
  date: string;
  status: 'published' | 'pending' | 'flagged' | 'hidden';
  helpful: number;
  notHelpful: number;
  bookingId: string;
}

const mockReviews: Review[] = [
  {
    id: 'REV001',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    providerName: 'Dr. <PERSON>',
    providerType: 'Veterinary',
    service: 'Health Checkup',
    rating: 5,
    title: 'Excellent veterinary care!',
    comment: 'Dr. <PERSON> is amazing! He took great care of my dog and explained everything clearly. The clinic is clean and the staff is very friendly. Highly recommended!',
    date: '2024-07-20',
    status: 'published',
    helpful: 12,
    notHelpful: 0,
    bookingId: 'BK001'
  },
  {
    id: 'REV002',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    providerName: 'Happy Paws Grooming',
    providerType: 'Grooming',
    service: 'Full Grooming',
    rating: 5,
    title: 'Professional grooming service',
    comment: 'Happy Paws did an amazing job grooming my poodle. Luna looks fantastic and they were very gentle with her. Will definitely be back!',
    date: '2024-07-15',
    status: 'published',
    helpful: 8,
    notHelpful: 1,
    bookingId: 'BK002'
  },
  {
    id: 'REV003',
    customerName: 'Emily Wilson',
    customerEmail: '<EMAIL>',
    providerName: 'Cozy Pet Lodge',
    providerType: 'Pet Hotel',
    service: 'Overnight Boarding',
    rating: 4,
    title: 'Great boarding experience',
    comment: 'Charlie had a wonderful time at Cozy Pet Lodge. The facilities are clean and the staff sends regular updates. Only minor issue was the pickup time.',
    date: '2024-07-10',
    status: 'published',
    helpful: 6,
    notHelpful: 0,
    bookingId: 'BK003'
  },
  {
    id: 'REV004',
    customerName: 'David Brown',
    customerEmail: '<EMAIL>',
    providerName: 'Elite Pet Training',
    providerType: 'Training',
    service: 'Basic Training',
    rating: 5,
    title: 'Outstanding training results',
    comment: 'The training program exceeded my expectations. Bella learned so much in just a few sessions. The trainer was patient and knowledgeable.',
    date: '2024-07-05',
    status: 'published',
    helpful: 15,
    notHelpful: 0,
    bookingId: 'BK004'
  },
  {
    id: 'REV005',
    customerName: 'Anonymous User',
    customerEmail: '<EMAIL>',
    providerName: 'Pawsome Dog Walking',
    providerType: 'Dog Walking',
    service: 'Daily Walk',
    rating: 2,
    title: 'Disappointing service',
    comment: 'The walker was late multiple times and didn\'t follow the route I requested. Not satisfied with the service quality.',
    date: '2024-07-01',
    status: 'flagged',
    helpful: 3,
    notHelpful: 8,
    bookingId: 'BK005'
  }
];

const reviewStatuses = ['All Status', 'Published', 'Pending', 'Flagged', 'Hidden'];
const ratingFilters = ['All Ratings', '5 Stars', '4 Stars', '3 Stars', '2 Stars', '1 Star'];

export default function ReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>(mockReviews);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [selectedRating, setSelectedRating] = useState('All Ratings');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-700';
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      case 'flagged': return 'bg-red-100 text-red-700';
      case 'hidden': return 'bg-gray-100 text-gray-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <MessageSquare className="w-4 h-4" />;
      case 'flagged': return <Flag className="w-4 h-4" />;
      case 'hidden': return <XCircle className="w-4 h-4" />;
      default: return <MessageSquare className="w-4 h-4" />;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         review.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         review.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         review.comment.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'All Status' || 
                         review.status.toLowerCase() === selectedStatus.toLowerCase();
    const matchesRating = selectedRating === 'All Ratings' || 
                         review.rating === parseInt(selectedRating.split(' ')[0]);
    
    return matchesSearch && matchesStatus && matchesRating;
  });

  const handleReviewAction = (reviewId: string, action: string) => {
    console.log(`${action} review ${reviewId}`);
    // Implement review actions here
  };

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Reviews Management</h1>
          <p className="text-cool-600">Monitor and moderate customer reviews and feedback</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <MessageSquare className="w-6 h-6 text-primary-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{reviews.length}</h3>
          <p className="text-cool-600">Total Reviews</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-yellow-100 rounded-xl">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{averageRating.toFixed(1)}</h3>
          <p className="text-cool-600">Average Rating</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {reviews.filter(r => r.status === 'published').length}
          </h3>
          <p className="text-cool-600">Published</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-yellow-100 rounded-xl">
              <MessageSquare className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {reviews.filter(r => r.status === 'pending').length}
          </h3>
          <p className="text-cool-600">Pending</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-red-100 rounded-xl">
              <Flag className="w-6 h-6 text-red-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {reviews.filter(r => r.status === 'flagged').length}
          </h3>
          <p className="text-cool-600">Flagged</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
            <input
              type="text"
              placeholder="Search reviews by customer, provider, title, or content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            />
          </div>
          
          <div className="flex gap-4">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {reviewStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <select
              value={selectedRating}
              onChange={(e) => setSelectedRating(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {ratingFilters.map(rating => (
                <option key={rating} value={rating}>{rating}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/30">
          <div className="flex items-center justify-between">
            <p className="text-cool-700">
              <span className="font-bold">{filteredReviews.length}</span> reviews found
            </p>
          </div>
        </div>

        <div className="space-y-4 p-6">
          {filteredReviews.map((review) => (
            <div key={review.id} className="bg-white/50 rounded-xl p-6 hover:bg-white/70 transition-colors duration-200">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium">
                      {review.customerName.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="flex items-center gap-3 mb-1">
                      <h3 className="font-bold text-cool-800">{review.title}</h3>
                      <div className="flex items-center gap-1">
                        {renderStars(review.rating)}
                      </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-cool-600">
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span>{review.customerName}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Building2 className="w-3 h-3" />
                        <span>{review.providerName}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{review.date}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(review.status)}`}>
                    {getStatusIcon(review.status)}
                    <span className="capitalize">{review.status}</span>
                  </div>
                </div>
              </div>

              <p className="text-cool-700 mb-4 leading-relaxed">{review.comment}</p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6 text-sm text-cool-600">
                  <span className="px-2 py-1 bg-secondary-100 text-secondary-700 rounded text-xs">
                    {review.service} • {review.providerType}
                  </span>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <ThumbsUp className="w-3 h-3" />
                      <span>{review.helpful}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <ThumbsDown className="w-3 h-3" />
                      <span>{review.notHelpful}</span>
                    </div>
                  </div>
                  <span>Booking: {review.bookingId}</span>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => handleReviewAction(review.id, 'view')}
                    className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                    title="View Details"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleReviewAction(review.id, 'edit')}
                    className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                    title="Edit Review"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                  {review.status === 'pending' && (
                    <button
                      onClick={() => handleReviewAction(review.id, 'approve')}
                      className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors duration-200"
                      title="Approve Review"
                    >
                      <CheckCircle className="w-4 h-4" />
                    </button>
                  )}
                  {review.status === 'flagged' && (
                    <button
                      onClick={() => handleReviewAction(review.id, 'unflag')}
                      className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors duration-200"
                      title="Remove Flag"
                    >
                      <CheckCircle className="w-4 h-4" />
                    </button>
                  )}
                  <button
                    onClick={() => handleReviewAction(review.id, 'flag')}
                    className="p-2 text-yellow-600 hover:bg-yellow-100 rounded-lg transition-colors duration-200"
                    title="Flag Review"
                  >
                    <Flag className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleReviewAction(review.id, 'delete')}
                    className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200"
                    title="Delete Review"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredReviews.length === 0 && (
          <div className="p-12 text-center">
            <MessageSquare className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No reviews found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search criteria or filters.</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedStatus('All Status');
                setSelectedRating('All Ratings');
              }}
              className="btn-primary"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
