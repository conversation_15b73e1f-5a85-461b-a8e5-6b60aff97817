'use client';

import { useState } from 'react';
import { 
  DollarSign, 
  Search, 
  Filter, 
  Eye, 
  Download, 
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  CreditCard,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Calendar
} from 'lucide-react';

interface Payment {
  id: string;
  bookingId: string;
  customerName: string;
  providerName: string;
  amount: number;
  fee: number;
  netAmount: number;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  method: 'card' | 'paypal' | 'bank_transfer';
  date: string;
  transactionId: string;
}

const mockPayments: Payment[] = [
  {
    id: 'PAY001',
    bookingId: 'BK001',
    customerName: '<PERSON>',
    providerName: 'Dr. <PERSON>',
    amount: 80,
    fee: 2.4,
    netAmount: 77.6,
    status: 'completed',
    method: 'card',
    date: '2024-07-28',
    transactionId: 'txn_1234567890'
  },
  {
    id: 'PAY002',
    bookingId: 'BK002',
    customerName: '<PERSON>',
    providerName: '<PERSON> Paws Grooming',
    amount: 65,
    fee: 1.95,
    netAmount: 63.05,
    status: 'completed',
    method: 'paypal',
    date: '2024-07-27',
    transactionId: 'txn_0987654321'
  },
  {
    id: 'PAY003',
    bookingId: 'BK003',
    customerName: 'Emily Wilson',
    providerName: 'Cozy Pet Lodge',
    amount: 180,
    fee: 5.4,
    netAmount: 174.6,
    status: 'pending',
    method: 'card',
    date: '2024-07-28',
    transactionId: 'txn_1122334455'
  },
  {
    id: 'PAY004',
    bookingId: 'BK004',
    customerName: 'David Brown',
    providerName: 'Elite Pet Training',
    amount: 75,
    fee: 2.25,
    netAmount: 72.75,
    status: 'completed',
    method: 'bank_transfer',
    date: '2024-07-25',
    transactionId: 'txn_5566778899'
  },
  {
    id: 'PAY005',
    bookingId: 'BK005',
    customerName: 'Lisa Garcia',
    providerName: 'Pawsome Dog Walking',
    amount: 25,
    fee: 0.75,
    netAmount: 24.25,
    status: 'refunded',
    method: 'card',
    date: '2024-07-28',
    transactionId: 'txn_9988776655'
  }
];

const paymentStatuses = ['All Status', 'Pending', 'Completed', 'Failed', 'Refunded'];
const paymentMethods = ['All Methods', 'Card', 'PayPal', 'Bank Transfer'];

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>(mockPayments);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [selectedMethod, setSelectedMethod] = useState('All Methods');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      case 'completed': return 'bg-green-100 text-green-700';
      case 'failed': return 'bg-red-100 text-red-700';
      case 'refunded': return 'bg-gray-100 text-gray-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      case 'refunded': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'card': return <CreditCard className="w-4 h-4" />;
      case 'paypal': return <DollarSign className="w-4 h-4" />;
      case 'bank_transfer': return <DollarSign className="w-4 h-4" />;
      default: return <DollarSign className="w-4 h-4" />;
    }
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.providerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.transactionId.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'All Status' || 
                         payment.status.toLowerCase() === selectedStatus.toLowerCase();
    const matchesMethod = selectedMethod === 'All Methods' || 
                         payment.method.toLowerCase() === selectedMethod.toLowerCase().replace(' ', '_');
    
    return matchesSearch && matchesStatus && matchesMethod;
  });

  const totalRevenue = payments.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.amount, 0);
  const totalFees = payments.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.fee, 0);
  const netRevenue = payments.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.netAmount, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Payments Management</h1>
          <p className="text-cool-600">Monitor transactions, fees, and payment processing</p>
        </div>
        
        <div className="flex gap-3">
          <button className="btn-outline flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button className="btn-outline flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-primary-600" />
            </div>
            <div className="flex items-center gap-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+15%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">${totalRevenue}</h3>
          <p className="text-cool-600">Total Revenue</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-secondary-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-secondary-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">${netRevenue.toFixed(2)}</h3>
          <p className="text-cool-600">Net Revenue</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-warm-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-warm-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">${totalFees.toFixed(2)}</h3>
          <p className="text-cool-600">Processing Fees</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {payments.filter(p => p.status === 'completed').length}
          </h3>
          <p className="text-cool-600">Completed</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
            <input
              type="text"
              placeholder="Search by customer, provider, payment ID, or transaction ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            />
          </div>
          
          <div className="flex gap-4">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {paymentStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <select
              value={selectedMethod}
              onChange={(e) => setSelectedMethod(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {paymentMethods.map(method => (
                <option key={method} value={method}>{method}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/30">
          <div className="flex items-center justify-between">
            <p className="text-cool-700">
              <span className="font-bold">{filteredPayments.length}</span> payments found
            </p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Payment ID</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Customer</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Provider</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Amount</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Method</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Date</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/30">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-medium text-cool-800">{payment.id}</p>
                      <p className="text-sm text-cool-600">Booking: {payment.bookingId}</p>
                      <p className="text-xs text-cool-500">{payment.transactionId}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="font-medium text-cool-800">{payment.customerName}</p>
                  </td>
                  <td className="px-6 py-4">
                    <p className="font-medium text-cool-800">{payment.providerName}</p>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="font-bold text-cool-800">${payment.amount}</p>
                      <p className="text-sm text-cool-600">Fee: ${payment.fee}</p>
                      <p className="text-sm text-green-600">Net: ${payment.netAmount}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      {getMethodIcon(payment.method)}
                      <span className="capitalize text-cool-800">
                        {payment.method.replace('_', ' ')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium w-fit ${getStatusColor(payment.status)}`}>
                      {getStatusIcon(payment.status)}
                      <span className="capitalize">{payment.status}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="text-cool-800">{payment.date}</p>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button
                        className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                        title="Download Receipt"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredPayments.length === 0 && (
          <div className="p-12 text-center">
            <DollarSign className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No payments found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search criteria or filters.</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedStatus('All Status');
                setSelectedMethod('All Methods');
              }}
              className="btn-primary"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
