'use client';

import { useState } from 'react';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Globe, 
  Mail, 
  Bell, 
  CreditCard, 
  Shield, 
  Palette,
  Database,
  Cloud,
  Smartphone,
  Monitor,
  Users,
  Building2,
  MapPin,
  Phone,
  ExternalLink
} from 'lucide-react';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    // General Settings
    siteName: 'Fetchly',
    siteDescription: 'Your trusted pet service platform',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '123 Pet Street, San Francisco, CA 94102',
    
    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingEmails: true,
    
    // Payment Settings
    stripePublicKey: 'pk_test_1234567890',
    stripeSecretKey: 'sk_test_1234567890',
    paypalClientId: 'paypal_client_id',
    commissionRate: 15,
    
    // Platform Settings
    maintenanceMode: false,
    userRegistration: true,
    providerRegistration: true,
    autoApproveProviders: false,
    
    // Feature Flags
    enableReviews: true,
    enableChat: true,
    enableBookingReminders: true,
    enableGeoLocation: true
  });

  const tabs = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'payments', label: 'Payments', icon: CreditCard },
    { id: 'platform', label: 'Platform', icon: Globe },
    { id: 'features', label: 'Features', icon: Smartphone }
  ];

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    console.log('Saving settings:', settings);
    // Implement save functionality here
  };

  const renderToggle = (key: string, value: boolean) => (
    <button
      onClick={() => handleSettingChange(key, !value)}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
        value ? 'bg-primary-500' : 'bg-gray-300'
      }`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          value ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Platform Settings</h1>
          <p className="text-cool-600">Configure platform settings and preferences</p>
        </div>
        
        <div className="flex gap-3">
          <button className="btn-outline flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Reset
          </button>
          <button onClick={handleSave} className="btn-primary flex items-center gap-2">
            <Save className="w-4 h-4" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Settings Tabs */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="border-b border-white/30">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-cool-600 hover:text-cool-800'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Site Name
                  </label>
                  <input
                    type="text"
                    value={settings.siteName}
                    onChange={(e) => handleSettingChange('siteName', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    value={settings.contactEmail}
                    onChange={(e) => handleSettingChange('contactEmail', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    value={settings.contactPhone}
                    onChange={(e) => handleSettingChange('contactPhone', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Business Address
                  </label>
                  <input
                    type="text"
                    value={settings.address}
                    onChange={(e) => handleSettingChange('address', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">
                  Site Description
                </label>
                <textarea
                  value={settings.siteDescription}
                  onChange={(e) => handleSettingChange('siteDescription', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          )}

          {/* Notification Settings */}
          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Email Notifications</h3>
                    <p className="text-sm text-cool-600">Send email notifications for important events</p>
                  </div>
                  {renderToggle('emailNotifications', settings.emailNotifications)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">SMS Notifications</h3>
                    <p className="text-sm text-cool-600">Send SMS alerts for urgent notifications</p>
                  </div>
                  {renderToggle('smsNotifications', settings.smsNotifications)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Push Notifications</h3>
                    <p className="text-sm text-cool-600">Send push notifications to mobile apps</p>
                  </div>
                  {renderToggle('pushNotifications', settings.pushNotifications)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Marketing Emails</h3>
                    <p className="text-sm text-cool-600">Send promotional and marketing emails</p>
                  </div>
                  {renderToggle('marketingEmails', settings.marketingEmails)}
                </div>
              </div>
            </div>
          )}

          {/* Payment Settings */}
          {activeTab === 'payments' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Stripe Public Key
                  </label>
                  <input
                    type="text"
                    value={settings.stripePublicKey}
                    onChange={(e) => handleSettingChange('stripePublicKey', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Stripe Secret Key
                  </label>
                  <input
                    type="password"
                    value={settings.stripeSecretKey}
                    onChange={(e) => handleSettingChange('stripeSecretKey', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    PayPal Client ID
                  </label>
                  <input
                    type="text"
                    value={settings.paypalClientId}
                    onChange={(e) => handleSettingChange('paypalClientId', e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">
                    Commission Rate (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={settings.commissionRate}
                    onChange={(e) => handleSettingChange('commissionRate', parseInt(e.target.value))}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Platform Settings */}
          {activeTab === 'platform' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Maintenance Mode</h3>
                    <p className="text-sm text-cool-600">Put the platform in maintenance mode</p>
                  </div>
                  {renderToggle('maintenanceMode', settings.maintenanceMode)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">User Registration</h3>
                    <p className="text-sm text-cool-600">Allow new users to register</p>
                  </div>
                  {renderToggle('userRegistration', settings.userRegistration)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Provider Registration</h3>
                    <p className="text-sm text-cool-600">Allow new providers to register</p>
                  </div>
                  {renderToggle('providerRegistration', settings.providerRegistration)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Auto-Approve Providers</h3>
                    <p className="text-sm text-cool-600">Automatically approve new provider registrations</p>
                  </div>
                  {renderToggle('autoApproveProviders', settings.autoApproveProviders)}
                </div>
              </div>
            </div>
          )}

          {/* Feature Settings */}
          {activeTab === 'features' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Reviews System</h3>
                    <p className="text-sm text-cool-600">Enable customer reviews and ratings</p>
                  </div>
                  {renderToggle('enableReviews', settings.enableReviews)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Chat System</h3>
                    <p className="text-sm text-cool-600">Enable real-time chat between users and providers</p>
                  </div>
                  {renderToggle('enableChat', settings.enableChat)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Booking Reminders</h3>
                    <p className="text-sm text-cool-600">Send automatic booking reminders</p>
                  </div>
                  {renderToggle('enableBookingReminders', settings.enableBookingReminders)}
                </div>

                <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-cool-800">Geo-Location Services</h3>
                    <p className="text-sm text-cool-600">Enable location-based provider search</p>
                  </div>
                  {renderToggle('enableGeoLocation', settings.enableGeoLocation)}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* System Information */}
      <div className="glass-card rounded-2xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">System Information</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-3 mb-2">
              <Database className="w-5 h-5 text-primary-600" />
              <h3 className="font-medium text-cool-800">Database</h3>
            </div>
            <p className="text-sm text-cool-600">PostgreSQL 14.2</p>
            <p className="text-xs text-green-600">Connected</p>
          </div>

          <div className="p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-3 mb-2">
              <Cloud className="w-5 h-5 text-secondary-600" />
              <h3 className="font-medium text-cool-800">Storage</h3>
            </div>
            <p className="text-sm text-cool-600">AWS S3</p>
            <p className="text-xs text-green-600">Active</p>
          </div>

          <div className="p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-3 mb-2">
              <Shield className="w-5 h-5 text-warm-600" />
              <h3 className="font-medium text-cool-800">Security</h3>
            </div>
            <p className="text-sm text-cool-600">SSL Enabled</p>
            <p className="text-xs text-green-600">Secure</p>
          </div>
        </div>
      </div>
    </div>
  );
}
