'use client';

import { Users, Shield, Award, Target, Zap } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const teamMembers = [
  {
    name: "<PERSON>",
    role: "CEO & Founder",
    bio: "Passionate pet lover with 15+ years in tech and business development",
    image: "/api/placeholder/150/150"
  },
  {
    name: "Dr. <PERSON>",
    role: "Chief Veterinary Officer",
    bio: "Licensed veterinarian with expertise in pet health and wellness",
    image: "/api/placeholder/150/150"
  },
  {
    name: "<PERSON>",
    role: "Head of Operations",
    bio: "Operations expert ensuring seamless service delivery",
    image: "/api/placeholder/150/150"
  },
  {
    name: "<PERSON>",
    role: "Lead Developer",
    bio: "Full-stack developer creating innovative pet care solutions",
    image: "/api/placeholder/150/150"
  }
];

const values = [
  {
    icon: () => (
      <Image
        src="/fetchlylogo.png"
        alt="Fetchly Logo"
        width={32}
        height={32}
        className="w-8 h-8"
      />
    ),
    title: "Pet-First Approach",
    description: "Every decision we make prioritizes the health, safety, and happiness of pets"
  },
  {
    icon: Shield,
    title: "Trust & Safety",
    description: "Rigorous vetting processes and insurance coverage for complete peace of mind"
  },
  {
    icon: Users,
    title: "Community Focus",
    description: "Building connections between pet owners and trusted service providers"
  },
  {
    icon: Award,
    title: "Quality Excellence",
    description: "Maintaining the highest standards in all services and partnerships"
  }
];

const milestones = [
  {
    year: "2023",
    title: "Company Founded",
    description: "Fetchly was born from a passion to connect pet owners with trusted care providers"
  },
  {
    year: "2023",
    title: "First 100 Providers",
    description: "Onboarded our first group of verified pet care professionals"
  },
  {
    year: "2024",
    title: "10,000+ Happy Pets",
    description: "Reached our first major milestone of pets served through our platform"
  },
  {
    year: "2024",
    title: "National Expansion",
    description: "Expanded services to major cities across the country"
  }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-cool-800 mb-6">
              About <span className="text-gradient">Fetchly</span>
            </h1>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              We're on a mission to make pet care accessible, reliable, and stress-free for every pet parent
            </p>
          </div>

          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto text-center">
            <h2 className="text-2xl font-bold text-cool-800 mb-4">Our Story</h2>
            <p className="text-cool-700 text-lg leading-relaxed">
              Fetchly was founded by pet lovers who experienced firsthand the challenges of finding trustworthy, 
              quality pet care services. We believe every pet deserves the best care possible, and every pet parent 
              deserves peace of mind. Our platform connects you with verified, professional pet care providers 
              in your area, making it easier than ever to give your furry family members the care they need.
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            <div className="glass-card rounded-2xl p-8">
              <div className="flex items-center gap-3 mb-6">
                <Target className="w-8 h-8 text-primary-500" />
                <h2 className="text-3xl font-bold text-cool-800">Our Mission</h2>
              </div>
              <p className="text-cool-700 text-lg leading-relaxed">
                To revolutionize pet care by creating a trusted ecosystem where pet owners can easily find, 
                book, and manage all their pet's needs through one comprehensive platform. We're committed 
                to ensuring every pet receives professional, loving care while giving owners complete peace of mind.
              </p>
            </div>

            <div className="glass-card rounded-2xl p-8">
              <div className="flex items-center gap-3 mb-6">
                <Zap className="w-8 h-8 text-secondary-500" />
                <h2 className="text-3xl font-bold text-cool-800">Our Vision</h2>
              </div>
              <p className="text-cool-700 text-lg leading-relaxed">
                To become the world's most trusted pet care platform, where every pet owner has access to 
                high-quality, professional services in their community. We envision a future where pet care 
                is seamless, transparent, and tailored to each pet's unique needs.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Our Core Values
            </h2>
            <p className="text-xl text-cool-600">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <div key={index} className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-cool-800">{value.title}</h3>
                  <p className="text-cool-600">{value.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Meet Our Team
            </h2>
            <p className="text-xl text-cool-600">
              Passionate professionals dedicated to improving pet care
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {teamMembers.map((member, index) => (
              <div key={index} className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300">
                <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500"></div>
                <h3 className="text-xl font-bold text-cool-800 mb-1">{member.name}</h3>
                <p className="text-primary-500 font-medium mb-3">{member.role}</p>
                <p className="text-cool-600 text-sm">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Milestones */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Our Journey
            </h2>
            <p className="text-xl text-cool-600">
              Key milestones in our mission to transform pet care
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-start gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center text-white font-bold">
                      {milestone.year}
                    </div>
                  </div>
                  <div className="glass-card rounded-2xl p-6 flex-1">
                    <h3 className="text-xl font-bold text-cool-800 mb-2">{milestone.title}</h3>
                    <p className="text-cool-600">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Fetchly by the Numbers
              </h2>
              <p className="text-xl text-cool-600">
                Our impact on the pet care community
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">15K+</div>
                <div className="text-cool-600">Happy Pet Parents</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">500+</div>
                <div className="text-cool-600">Verified Providers</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">50K+</div>
                <div className="text-cool-600">Services Booked</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-gradient mb-2">4.9</div>
                <div className="text-cool-600">Average Rating</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-3xl mx-auto text-center relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-secondary-500/5 to-accent-500/5"></div>
            <div className="relative z-10">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Join the Fetchly Family
              </h2>
              <p className="text-xl text-cool-600 mb-8">
                Whether you're a pet parent or service provider, we'd love to have you as part of our community
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/signup" className="btn-primary">
                  Get Started as Pet Owner
                </Link>
                <Link href="/providers" className="btn-secondary">
                  Join as Service Provider
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
