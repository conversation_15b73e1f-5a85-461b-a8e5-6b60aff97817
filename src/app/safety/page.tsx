'use client';

import { Shield, Heart, Users, AlertTriangle, CheckCircle, Phone, Mail, Clock, Star, Lock, Eye, FileText } from 'lucide-react';
import Link from 'next/link';

export default function SafetyGuidelinesPage() {
  const safetyCategories = [
    {
      title: "Pet Safety Guidelines",
      icon: Heart,
      color: "from-pink-500 to-rose-500",
      guidelines: [
        {
          title: "Pre-Service Preparation",
          items: [
            "Ensure your pet is up-to-date on vaccinations",
            "Inform providers about any medical conditions or allergies",
            "Share your pet's behavioral patterns and triggers",
            "Provide emergency contact information",
            "Keep vaccination records accessible"
          ]
        },
        {
          title: "During Service",
          items: [
            "Stay available by phone during the service",
            "Provide clear instructions for your pet's care",
            "Ensure your pet has proper identification (collar, microchip)",
            "Leave emergency vet contact information",
            "Inform provider of any medications or special needs"
          ]
        },
        {
          title: "Emergency Protocols",
          items: [
            "Know your nearest emergency veterinary clinic",
            "Keep a first aid kit accessible",
            "Provide multiple emergency contact numbers",
            "Ensure providers know your pet's medical history",
            "Have your veterinarian's contact readily available"
          ]
        }
      ]
    },
    {
      title: "Platform Safety",
      icon: Shield,
      color: "from-blue-500 to-indigo-500",
      guidelines: [
        {
          title: "Provider Verification",
          items: [
            "All providers undergo comprehensive background checks",
            "License and certification verification required",
            "Insurance coverage confirmation mandatory",
            "Reference checks from previous clients",
            "Ongoing performance monitoring and reviews"
          ]
        },
        {
          title: "Secure Transactions",
          items: [
            "All payments processed through secure, encrypted channels",
            "No cash transactions - digital payments only",
            "Automatic payment protection and dispute resolution",
            "Transparent pricing with no hidden fees",
            "Refund protection for qualifying issues"
          ]
        },
        {
          title: "Data Protection",
          items: [
            "Personal information encrypted and securely stored",
            "Limited data sharing only with verified providers",
            "Regular security audits and updates",
            "GDPR compliant data handling practices",
            "User control over data sharing preferences"
          ]
        }
      ]
    },
    {
      title: "Community Guidelines",
      icon: Users,
      color: "from-green-500 to-emerald-500",
      guidelines: [
        {
          title: "Respectful Communication",
          items: [
            "Maintain professional and courteous communication",
            "Respect provider schedules and cancellation policies",
            "Provide honest and constructive feedback",
            "Report inappropriate behavior immediately",
            "Follow platform messaging guidelines"
          ]
        },
        {
          title: "Booking Etiquette",
          items: [
            "Book services in advance when possible",
            "Provide accurate pet information during booking",
            "Respect cancellation and rescheduling policies",
            "Be punctual for scheduled appointments",
            "Communicate any changes promptly"
          ]
        },
        {
          title: "Review Standards",
          items: [
            "Leave honest, detailed reviews after services",
            "Focus on service quality and professionalism",
            "Avoid personal attacks or inappropriate language",
            "Report fake or misleading reviews",
            "Help other pet owners make informed decisions"
          ]
        }
      ]
    }
  ];

  const emergencyContacts = [
    {
      title: "Platform Support",
      description: "24/7 customer support for urgent issues",
      contact: "<EMAIL>",
      phone: "+****************",
      icon: Phone
    },
    {
      title: "Emergency Reporting",
      description: "Report safety incidents or emergencies",
      contact: "<EMAIL>",
      phone: "+****************",
      icon: AlertTriangle
    },
    {
      title: "Trust & Safety Team",
      description: "Report provider concerns or safety violations",
      contact: "<EMAIL>",
      phone: "+****************",
      icon: Shield
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="p-4 bg-white/20 rounded-full">
                <Shield className="w-12 h-12" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Safety Guidelines
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Your safety and your pet's wellbeing are our top priorities. Follow these guidelines to ensure a safe and positive experience on Fetchly.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-16">
        {/* Safety Categories */}
        <div className="space-y-16">
          {safetyCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${category.color} text-white mb-4`}>
                  <category.icon className="w-8 h-8" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">{category.title}</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {category.guidelines.map((guideline, guidelineIndex) => (
                  <div key={guidelineIndex} className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <h3 className="text-xl font-bold text-gray-900 mb-6">{guideline.title}</h3>
                    <ul className="space-y-3">
                      {guideline.items.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Emergency Contacts */}
        <div className="max-w-6xl mx-auto mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Emergency Contacts</h2>
            <p className="text-xl text-gray-600">
              Need immediate assistance? Contact our support team 24/7
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {emergencyContacts.map((contact, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-600 mx-auto mb-6">
                  <contact.icon className="w-8 h-8" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{contact.title}</h3>
                <p className="text-gray-600 mb-6">{contact.description}</p>
                <div className="space-y-2">
                  <div className="flex items-center justify-center space-x-2">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <a href={`mailto:${contact.contact}`} className="text-blue-600 hover:text-blue-700">
                      {contact.contact}
                    </a>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <a href={`tel:${contact.phone}`} className="text-blue-600 hover:text-blue-700">
                      {contact.phone}
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Resources */}
        <div className="max-w-4xl mx-auto mt-20">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
            <h2 className="text-2xl font-bold mb-4">Additional Safety Resources</h2>
            <p className="text-blue-100 mb-8">
              Explore more resources to keep you and your pets safe
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href="/help" className="bg-white/20 hover:bg-white/30 rounded-lg p-4 transition-colors duration-300">
                <FileText className="w-6 h-6 mx-auto mb-2" />
                <span className="font-medium">Help Center</span>
              </Link>
              <Link href="/insurance" className="bg-white/20 hover:bg-white/30 rounded-lg p-4 transition-colors duration-300">
                <Shield className="w-6 h-6 mx-auto mb-2" />
                <span className="font-medium">Insurance Info</span>
              </Link>
              <Link href="/contact" className="bg-white/20 hover:bg-white/30 rounded-lg p-4 transition-colors duration-300">
                <Phone className="w-6 h-6 mx-auto mb-2" />
                <span className="font-medium">Contact Support</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
