'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { collection, query, where, getDocs, doc, getDoc, addDoc, deleteDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  MapPin, Heart, Share2, MessageCircle, ArrowLeft, PawPrint,
  Users, Star, Calendar, Camera, Trophy, Crown, Sparkles
} from 'lucide-react';
import toast from 'react-hot-toast';
import PostCard from '@/components/PostCard';

export default function PublicPetOwnerProfile({ params }: { params: Promise<{ id: string }> }) {
  const { user } = useAuth();
  const router = useRouter();
  const resolvedParams = use(params);
  const userId = resolvedParams.id;
  
  const [profileUser, setProfileUser] = useState<any>(null);
  const [pets, setPets] = useState<any[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [followerCount, setFollowerCount] = useState(0);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true);
        console.log('🔍 Loading public pet owner profile for userId:', userId);

        // Load user data
        const userDoc = await getDoc(doc(db, 'users', userId));
        if (!userDoc.exists()) {
          throw new Error('User not found');
        }

        const userData = userDoc.data();
        setProfileUser({
          id: userId,
          ...userData
        });

        // Load user's pets
        const petsQuery = query(
          collection(db, 'pets'),
          where('userId', '==', userId)
        );
        const petsSnapshot = await getDocs(petsQuery);
        const userPets = petsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setPets(userPets);

        // Load user's public posts
        const postsQuery = query(
          collection(db, 'posts'),
          where('userId', '==', userId),
          where('privacy', '==', 'public')
        );
        const postsSnapshot = await getDocs(postsQuery);
        const userPosts = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setPosts(userPosts);

        // Check if current user is following this profile
        if (user) {
          const followQuery = query(
            collection(db, 'followers'),
            where('followedUserId', '==', userId),
            where('followerUserId', '==', user.id)
          );
          const followSnapshot = await getDocs(followQuery);
          setIsFollowing(!followSnapshot.empty);
        }

        // Get follower count
        const followerQuery = query(
          collection(db, 'followers'),
          where('followedUserId', '==', userId)
        );
        const followerSnapshot = await getDocs(followerQuery);
        setFollowerCount(followerSnapshot.docs.length);

      } catch (error) {
        console.error('Error loading profile:', error);
        toast.error('Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, [userId, user]);

  const handleFollow = async () => {
    if (!user) {
      toast.error('Please sign in to follow users');
      return;
    }

    try {
      if (isFollowing) {
        // Unfollow
        const followQuery = query(
          collection(db, 'followers'),
          where('followedUserId', '==', userId),
          where('followerUserId', '==', user.id)
        );
        const followSnapshot = await getDocs(followQuery);
        if (!followSnapshot.empty) {
          await deleteDoc(followSnapshot.docs[0].ref);
          setIsFollowing(false);
          setFollowerCount(prev => prev - 1);
          toast.success('Unfollowed user');
        }
      } else {
        // Follow
        await addDoc(collection(db, 'followers'), {
          followedUserId: userId,
          followerUserId: user.id,
          createdAt: new Date()
        });
        setIsFollowing(true);
        setFollowerCount(prev => prev + 1);
        toast.success('Following user');
      }
    } catch (error) {
      console.error('Error following/unfollowing:', error);
      toast.error('Failed to update follow status');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!profileUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center p-6 max-w-md">
          <PawPrint className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Profile Not Found</h2>
          <p className="text-gray-600 mb-4">This user profile doesn't exist or isn't available.</p>
          <button
            onClick={() => router.push('/community')}
            className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-2 rounded-lg hover:from-green-600 hover:to-blue-600"
          >
            Back to Community
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating Header */}
      <div className="relative z-20 bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="group flex items-center gap-3 bg-white/90 backdrop-blur-sm rounded-2xl px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white border border-white/20"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600 group-hover:text-green-600 transition-colors duration-300" />
              <span className="text-gray-700 font-medium group-hover:text-green-700 transition-colors duration-300">Back</span>
            </button>
            
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={`group relative p-4 rounded-2xl backdrop-blur-xl transition-all duration-300 hover:scale-110 ${
                  isFavorite 
                    ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg shadow-red-500/25' 
                    : 'bg-white/90 text-gray-600 hover:bg-white shadow-lg border border-white/20'
                }`}
              >
                <Heart className={`w-6 h-6 transition-all duration-300 ${isFavorite ? 'fill-current scale-110' : 'group-hover:scale-110'}`} />
              </button>
              
              {user && user.id !== userId && (
                <button
                  onClick={handleFollow}
                  className={`group relative px-6 py-4 rounded-2xl backdrop-blur-xl transition-all duration-300 hover:scale-105 font-medium ${
                    isFollowing 
                      ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-lg shadow-green-500/25' 
                      : 'bg-white/90 text-gray-700 hover:bg-gradient-to-r hover:from-green-500 hover:to-blue-500 hover:text-white shadow-lg border border-white/20'
                  }`}
                >
                  <Users className="w-5 h-5 inline mr-2" />
                  {isFollowing ? 'Following' : 'Follow'}
                  <span className="ml-2 text-sm opacity-75">({followerCount})</span>
                </button>
              )}
              
              <button className="group p-4 rounded-2xl bg-white/90 backdrop-blur-xl text-gray-600 hover:bg-white shadow-lg border border-white/20 transition-all duration-300 hover:scale-110">
                <Share2 className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Banner Section */}
      <div className="relative z-10 -mt-4">
        <div className="max-w-7xl mx-auto px-6">
          {/* Stunning Banner Card */}
          <div className="relative overflow-hidden rounded-3xl shadow-2xl bg-gradient-to-br from-green-600 via-blue-600 to-purple-600">
            {/* Banner Image with Overlay */}
            <div className="relative h-96 overflow-hidden">
              {profileUser.banner && profileUser.banner !== '/fetchlylogo.png' ? (
                <div className="relative w-full h-full">
                  <img
                    src={profileUser.banner}
                    alt="Profile Banner"
                    className="w-full h-full object-cover"
                  />
                  {/* Light overlay for text readability only */}
                  <div className="absolute inset-0 bg-black/20"></div>
                </div>
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-green-600 via-blue-600 to-purple-600 relative">
                  {/* Animated Pet Pattern Overlay */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute top-10 left-10 w-20 h-20 bg-white/30 rounded-full animate-bounce delay-100 flex items-center justify-center">
                      <PawPrint className="w-8 h-8 text-white" />
                    </div>
                    <div className="absolute top-20 right-20 w-16 h-16 bg-white/20 rounded-full animate-bounce delay-300 flex items-center justify-center">
                      <Heart className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/25 rounded-full animate-bounce delay-500 flex items-center justify-center">
                      <Star className="w-10 h-10 text-white" />
                    </div>
                    <div className="absolute bottom-10 right-10 w-12 h-12 bg-white/30 rounded-full animate-bounce delay-700 flex items-center justify-center">
                      <Sparkles className="w-5 h-5 text-white" />
                    </div>
                  </div>
                </div>
              )}

              {/* Hero Content */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white max-w-4xl px-8">
                  {/* Subtle background only behind text */}
                  <div className="bg-black/30 backdrop-blur-sm rounded-3xl p-8">
                    <div className="mb-6">
                      <Crown className="w-20 h-20 mx-auto mb-4 text-yellow-400 animate-pulse" />
                    </div>
                    <h1 className="text-5xl md:text-6xl font-bold mb-4 text-white drop-shadow-2xl">
                      {profileUser.name}
                    </h1>
                    <p className="text-2xl md:text-3xl font-light mb-6 text-white/95 drop-shadow-lg">
                      Pet Parent & Animal Lover
                    </p>
                    <div className="flex items-center justify-center space-x-6 text-white/90">
                      <div className="flex items-center space-x-2">
                        <MapPin className="w-5 h-5" />
                        <span className="text-lg">{profileUser.city || profileUser.location || 'Pet Lover'}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <PawPrint className="w-5 h-5 fill-current text-yellow-400" />
                        <span className="text-lg font-semibold">{pets.length} Pet{pets.length !== 1 ? 's' : ''}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute top-8 right-8 bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2">
                <div className="flex items-center space-x-2 text-white">
                  <Trophy className="w-5 h-5 text-yellow-400" />
                  <span className="font-medium">Pet Parent</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Profile Card */}
      <div className="max-w-7xl mx-auto px-6 -mt-24 relative z-20">
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 mb-8">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
            {/* Stunning Profile Photo */}
            <div className="relative group">
              <div className="relative">
                <img
                  src={profileUser.avatar || '/favicon.png'}
                  alt={profileUser.name}
                  className="w-40 h-40 rounded-3xl object-cover border-4 border-white shadow-2xl group-hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    e.currentTarget.src = '/favicon.png';
                  }}
                />
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-green-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              {/* Animated Online Status */}
              <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 border-4 border-white rounded-full animate-pulse shadow-lg">
                <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-75"></div>
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1 text-center lg:text-left">
              <div className="flex flex-col lg:flex-row lg:items-center gap-4 mb-4">
                <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  {profileUser.name}
                </h1>
                <div className="flex items-center justify-center lg:justify-start gap-3">
                  <div className="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                    <PawPrint className="w-4 h-4" />
                    Pet Parent
                  </div>
                  <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                    <Heart className="w-4 h-4" />
                    Animal Lover
                  </div>
                </div>
              </div>

              <p className="text-2xl text-gray-600 mb-6 font-light">
                Proud parent of {pets.length} amazing pet{pets.length !== 1 ? 's' : ''}
              </p>

              {/* Stats Row */}
              <div className="flex flex-wrap items-center justify-center lg:justify-start gap-6 mb-6">
                <div className="flex items-center gap-2 bg-green-50 px-4 py-2 rounded-2xl">
                  <PawPrint className="w-5 h-5 text-green-500 fill-current" />
                  <span className="font-bold text-lg">{pets.length}</span>
                  <span className="text-gray-500">Pet{pets.length !== 1 ? 's' : ''}</span>
                </div>
                <div className="flex items-center gap-2 bg-blue-50 px-4 py-2 rounded-2xl">
                  <MessageCircle className="w-5 h-5 text-blue-500" />
                  <span className="font-bold text-lg">{posts.length}</span>
                  <span className="text-gray-500">Post{posts.length !== 1 ? 's' : ''}</span>
                </div>
                <div className="flex items-center gap-2 bg-purple-50 px-4 py-2 rounded-2xl">
                  <Users className="w-5 h-5 text-purple-500" />
                  <span className="font-bold text-lg">{followerCount}</span>
                  <span className="text-gray-500">Follower{followerCount !== 1 ? 's' : ''}</span>
                </div>
                {profileUser.location && (
                  <div className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-2xl">
                    <MapPin className="w-5 h-5 text-gray-500" />
                    <span className="font-medium">{profileUser.location}</span>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              {user && user.id !== userId && (
                <div className="flex flex-wrap gap-4">
                  <button
                    onClick={() => {
                      // Handle message functionality
                      toast.success('Message feature coming soon!');
                    }}
                    className="group bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center gap-3"
                  >
                    <MessageCircle className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                    Send Message
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Stunning Pets Section */}
            {pets.length > 0 && (
              <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl flex items-center justify-center">
                    <PawPrint className="w-6 h-6 text-white" />
                  </div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">My Pets</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {pets.map((pet, index) => (
                    <div key={pet.id} className="group relative bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 border border-gray-100 hover:border-green-200 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                      {/* Pet Number Badge */}
                      <div className="absolute -top-3 -left-3 w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                        {index + 1}
                      </div>

                      <div className="flex items-center gap-4 mb-4">
                        {pet.photos && pet.photos.length > 0 ? (
                          <img
                            src={pet.photos[0]}
                            alt={pet.name}
                            className="w-16 h-16 rounded-2xl object-cover border-2 border-white shadow-lg group-hover:scale-110 transition-transform duration-300"
                          />
                        ) : (
                          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-400 to-blue-400 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <PawPrint className="w-8 h-8 text-white" />
                          </div>
                        )}
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 group-hover:text-green-600 transition-colors duration-300">{pet.name}</h3>
                          <p className="text-gray-600">{pet.breed} • {pet.age} years old</p>
                        </div>
                      </div>

                      {pet.description && (
                        <p className="text-gray-600 mb-4 leading-relaxed">{pet.description}</p>
                      )}

                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Heart className="w-4 h-4 text-red-500" />
                          <span>Loved</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-500" />
                          <span>Special</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Posts Section */}
            {posts.length > 0 && (
              <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Recent Posts</h2>
                </div>

                <div className="space-y-6">
                  {posts.map((post) => (
                    <PostCard
                      key={post.id}
                      post={post}
                      showPrivacyIndicator={false}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Empty State */}
            {pets.length === 0 && posts.length === 0 && (
              <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-12 text-center">
                <PawPrint className="w-20 h-20 text-gray-300 mx-auto mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">No Content Yet</h3>
                <p className="text-gray-600 text-lg">
                  {profileUser.name} hasn't shared any pets or posts yet.
                </p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Quick Stats */}
            <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-6">
              <h3 className="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">Profile Stats</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Pets</span>
                  <span className="font-bold text-green-600">{pets.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Posts</span>
                  <span className="font-bold text-blue-600">{posts.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Followers</span>
                  <span className="font-bold text-purple-600">{followerCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Joined</span>
                  <span className="font-bold text-gray-600">
                    {profileUser.joinedDate ? new Date(profileUser.joinedDate).getFullYear() : 'Recently'}
                  </span>
                </div>
              </div>
            </div>

            {/* Pet Types */}
            {pets.length > 0 && (
              <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-6">
                <h3 className="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-6">Pet Family</h3>
                <div className="space-y-3">
                  {pets.map((pet) => (
                    <div key={pet.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-2xl hover:bg-green-50 transition-colors duration-300">
                      {pet.photos && pet.photos.length > 0 ? (
                        <img
                          src={pet.photos[0]}
                          alt={pet.name}
                          className="w-10 h-10 rounded-full object-cover border-2 border-white shadow-sm"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-400 to-blue-400 flex items-center justify-center">
                          <PawPrint className="w-5 h-5 text-white" />
                        </div>
                      )}
                      <div>
                        <p className="font-medium text-gray-900">{pet.name}</p>
                        <p className="text-sm text-gray-500">{pet.breed}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
