'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Timestamp } from 'firebase/firestore';
import { motion } from 'framer-motion';
import { 
  User, 
  Settings, 
  CreditCard, 
  Calendar, 
  Heart, 
  Star,
  PlusCircle,
  Edit,
  Camera,
  MapPin,
  Phone,
  Mail,
  Shield,
  Award,
  TrendingUp,
  Clock,
  DollarSign,
  PawPrint,
  Home,
  Users,
  Bell,
  MessageCircle,
  BookOpen,
  FileText,
  AlertTriangle,
  CheckCircle,
  Info,
  ExternalLink,
  Menu,
  Search
} from 'lucide-react';
import { Pet, Booking, Transaction, TransactionType, MembershipStatus } from '@/types/user';
import FetchlyBalance from '@/components/dashboard/FetchlyBalance';
import PetProfiles from '@/components/dashboard/PetProfiles';
import BookingManagement from '@/components/dashboard/BookingManagement';
import MembershipRewards from '@/components/dashboard/MembershipRewards';

// US Compliance Data
const US_COMPLIANCE_DATA = {
  rabiesRequirements: {
    title: "Rabies Vaccination Requirements",
    description: "All dogs, cats, and ferrets must be vaccinated against rabies according to state law",
    requirements: [
      "Initial vaccination at 12-16 weeks of age",
      "Booster vaccination 1 year after initial",
      "Subsequent boosters every 1-3 years depending on vaccine type",
      "Valid rabies certificate required for interstate travel"
    ]
  },
  healthCertificates: {
    title: "Health Certificates for Interstate Travel",
    description: "USDA-accredited veterinarian must issue health certificates",
    validity: "10 days for domestic travel, 30 days for international",
    requirements: [
      "Physical examination by licensed veterinarian",
      "Current rabies vaccination",
      "Treatment for internal and external parasites",
      "Certificate of Veterinary Inspection (CVI)"
    ]
  },
  stateRegulations: {
    title: "State-Specific Regulations",
    examples: [
      {
        state: "California",
        requirements: ["Rabies vaccination mandatory", "Microchip identification recommended", "Spay/neuter laws in many cities"]
      },
      {
        state: "Florida", 
        requirements: ["Rabies vaccination required", "County licensing mandatory", "Leash laws enforced"]
      },
      {
        state: "Texas",
        requirements: ["Rabies vaccination at 4 months", "Annual registration required", "Dangerous dog regulations"]
      },
      {
        state: "New York",
        requirements: ["Rabies vaccination mandatory", "Dog licensing required", "Confinement laws for unvaccinated pets"]
      }
    ]
  },
  businessLicensing: {
    title: "Pet Service Business Licensing",
    description: "Requirements for pet care facilities vary by state and locality",
    commonRequirements: [
      "Business license from local municipality",
      "State kennel/boarding facility license",
      "USDA Animal Welfare License (for certain facilities)",
      "Liability insurance coverage",
      "Facility inspections and compliance",
      "Staff training and certification"
    ]
  }
};

export default function FacebookStyleProfile() {
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [showCompliance, setShowCompliance] = useState(false);

  // Mock data for demonstration
  const mockUser = {
    id: 'demo-user',
    email: '<EMAIL>',
    displayName: 'John Doe',
    photoURL: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
    bannerURL: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=1200&h=400&fit=crop',
    location: 'San Francisco, CA',
    phone: '+****************',
    memberSince: 'January 2024',
    fetchlyBalance: 125.50,
    membershipStatus: 'pro' as MembershipStatus,
    rewardPoints: 2450,
    bio: 'Proud pet parent of two amazing dogs. Love exploring dog-friendly places around the Bay Area!'
  };

  const mockPets: Pet[] = [
    {
      id: 'pet-1',
      userId: 'demo-user',
      name: 'Buddy',
      type: 'Dog',
      breed: 'Golden Retriever',
      dateOfBirth: Timestamp.fromDate(new Date('2021-03-15')),
      weight: 65,
      color: 'Golden',
      gender: 'male',
      photo: 'https://images.unsplash.com/photo-**********-71594a27632d?w=400',
      vaccinations: [
        { 
          name: 'Rabies', 
          date: Timestamp.fromDate(new Date('2024-01-15')), 
          expirationDate: Timestamp.fromDate(new Date('2025-01-15')),
          veterinarian: 'Dr. Smith'
        }
      ],
      allergies: ['Chicken', 'Pollen'],
      medications: [],
      medicalNotes: 'Very friendly and energetic. Loves playing fetch.',
      vetInfo: {
        name: 'Dr. Smith',
        phone: '+****************',
        address: '123 Vet Street, Pet City, CA 90210',
        email: '<EMAIL>'
      },
      createdAt: Timestamp.fromDate(new Date('2024-01-01')),
      updatedAt: Timestamp.fromDate(new Date('2024-01-01'))
    }
  ];

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: Home },
    { id: 'pets', label: 'My Pets', icon: PawPrint },
    { id: 'bookings', label: 'Bookings', icon: Calendar },
    { id: 'wallet', label: 'Fetchly Balance', icon: CreditCard },
    { id: 'membership', label: 'Membership & Rewards', icon: Award },
    { id: 'compliance', label: 'Compliance Info', icon: Shield },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-blue-600">Fetchly</h1>
              <div className="hidden md:flex items-center space-x-2 bg-gray-100 rounded-full px-4 py-2">
                <Search className="w-4 h-4 text-gray-500" />
                <input 
                  type="text" 
                  placeholder="Search pets, services..." 
                  className="bg-transparent outline-none text-sm w-64"
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                <Bell className="w-5 h-5 text-gray-600" />
              </button>
              <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                <MessageCircle className="w-5 h-5 text-gray-600" />
              </button>
              <div className="flex items-center space-x-2">
                <img 
                  src={mockUser.photoURL} 
                  alt={mockUser.displayName}
                  className="w-8 h-8 rounded-full"
                />
                <span className="hidden md:block text-sm font-medium">{mockUser.displayName}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4 sticky top-24">
              <nav className="space-y-2">
                {sidebarItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeTab === item.id 
                          ? 'bg-blue-50 text-blue-600 font-medium' 
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Profile Header */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
              {/* Banner Image */}
              <div className="relative h-96 bg-gradient-to-r from-blue-500 to-purple-600">
                <img 
                  src={mockUser.bannerURL} 
                  alt="Profile Banner"
                  className="w-full h-full object-cover"
                />
                <button className="absolute bottom-4 right-4 bg-white/20 backdrop-blur-sm rounded-lg p-2 text-white hover:bg-white/30">
                  <Camera className="w-4 h-4" />
                </button>
              </div>
              
              {/* Profile Info */}
              <div className="relative px-6 pb-6">
                <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                  {/* Profile Picture */}
                  <div className="relative -mt-16 mb-4 sm:mb-0">
                    <img 
                      src={mockUser.photoURL} 
                      alt={mockUser.displayName}
                      className="w-32 h-32 rounded-full border-4 border-white shadow-lg"
                    />
                    <button className="absolute bottom-2 right-2 bg-gray-100 rounded-full p-2 shadow-md hover:bg-gray-200">
                      <Camera className="w-4 h-4 text-gray-600" />
                    </button>
                  </div>
                  
                  {/* User Info */}
                  <div className="flex-1">
                    <h1 className="text-3xl font-bold text-gray-900">{mockUser.displayName}</h1>
                    <p className="text-gray-600 mt-1">{mockUser.bio}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{mockUser.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>Member since {mockUser.memberSince}</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex space-x-3 mt-4 sm:mt-0">
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                      <Edit className="w-4 h-4" />
                      <span>Edit Profile</span>
                    </button>
                  </div>
                </div>
                
                {/* Pet Preview */}
                <div className="mt-6 pt-6 border-t">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">My Pets</h3>
                  <div className="flex space-x-4">
                    {mockPets.map((pet) => (
                      <div key={pet.id} className="text-center">
                        <img 
                          src={pet.photo} 
                          alt={pet.name}
                          className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                        />
                        <p className="text-sm font-medium text-gray-900 mt-1">{pet.name}</p>
                        <p className="text-xs text-gray-500">{pet.breed}</p>
                      </div>
                    ))}
                    <button className="flex flex-col items-center justify-center w-16 h-16 rounded-full border-2 border-dashed border-gray-300 text-gray-400 hover:border-gray-400 hover:text-gray-500">
                      <PlusCircle className="w-6 h-6" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Tab Content */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              {activeTab === 'overview' && <OverviewTab user={mockUser} pets={mockPets} />}
              {activeTab === 'pets' && <PetProfiles pets={mockPets} onPetsUpdate={() => {}} />}
              {activeTab === 'compliance' && <ComplianceTab />}
              {/* Add other tab components as needed */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ user, pets }: { user: any; pets: Pet[] }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100">Fetchly Balance</p>
              <p className="text-2xl font-bold">${user.fetchlyBalance}</p>
            </div>
            <CreditCard className="w-8 h-8 text-blue-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100">Reward Points</p>
              <p className="text-2xl font-bold">{user.rewardPoints}</p>
            </div>
            <Star className="w-8 h-8 text-purple-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100">My Pets</p>
              <p className="text-2xl font-bold">{pets.length}</p>
            </div>
            <PawPrint className="w-8 h-8 text-green-200" />
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <Calendar className="w-5 h-5 text-blue-500" />
            <div>
              <p className="font-medium">Grooming appointment scheduled</p>
              <p className="text-sm text-gray-500">Tomorrow at 2:00 PM</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Compliance Tab Component  
function ComplianceTab() {
  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-blue-500 mt-0.5" />
          <div>
            <h3 className="font-semibold text-blue-900">Important Compliance Information</h3>
            <p className="text-blue-700 text-sm mt-1">
              Stay informed about pet care regulations across all US states and territories
            </p>
          </div>
        </div>
      </div>

      {/* Rabies Requirements */}
      <div className="border rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Shield className="w-5 h-5 text-red-500" />
          <h3 className="text-lg font-semibold">{US_COMPLIANCE_DATA.rabiesRequirements.title}</h3>
        </div>
        <p className="text-gray-600 mb-4">{US_COMPLIANCE_DATA.rabiesRequirements.description}</p>
        <ul className="space-y-2">
          {US_COMPLIANCE_DATA.rabiesRequirements.requirements.map((req, index) => (
            <li key={index} className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
              <span className="text-sm">{req}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* Health Certificates */}
      <div className="border rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-4">
          <FileText className="w-5 h-5 text-blue-500" />
          <h3 className="text-lg font-semibold">{US_COMPLIANCE_DATA.healthCertificates.title}</h3>
        </div>
        <p className="text-gray-600 mb-2">{US_COMPLIANCE_DATA.healthCertificates.description}</p>
        <p className="text-sm text-gray-500 mb-4">Validity: {US_COMPLIANCE_DATA.healthCertificates.validity}</p>
        <ul className="space-y-2">
          {US_COMPLIANCE_DATA.healthCertificates.requirements.map((req, index) => (
            <li key={index} className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
              <span className="text-sm">{req}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
