'use client';

import { useState } from 'react';
import { Phone, MapPin, Clock, AlertTriangle, Heart, Zap, Shield, Navigation } from 'lucide-react';
import Link from 'next/link';

const emergencyServices = [
  {
    id: 1,
    name: "24/7 Emergency Vet Clinic",
    phone: "(*************",
    address: "123 Main St, Downtown",
    distance: "0.5 miles",
    status: "Open Now",
    specialties: ["Emergency Surgery", "Critical Care", "Trauma", "Poison Control"],
    rating: 4.9,
    responseTime: "< 15 minutes"
  },
  {
    id: 2,
    name: "Animal Emergency Hospital",
    phone: "(*************",
    address: "456 Oak Ave, Midtown",
    distance: "1.2 miles",
    status: "Open 24/7",
    specialties: ["Emergency Care", "X-Ray", "Laboratory", "ICU"],
    rating: 4.8,
    responseTime: "< 20 minutes"
  },
  {
    id: 3,
    name: "Pet Emergency Center",
    phone: "(*************",
    address: "789 Pine St, Westside",
    distance: "2.1 miles",
    status: "Open Now",
    specialties: ["Emergency Medicine", "Surgery", "Cardiology", "Neurology"],
    rating: 4.7,
    responseTime: "< 25 minutes"
  }
];

const emergencyTips = [
  {
    title: "Choking",
    description: "Open mouth, remove visible objects, perform rescue breathing if needed",
    icon: AlertTriangle,
    color: "warm"
  },
  {
    title: "Poisoning",
    description: "Contact poison control immediately, do not induce vomiting unless instructed",
    icon: Shield,
    color: "secondary"
  },
  {
    title: "Bleeding",
    description: "Apply direct pressure with clean cloth, elevate if possible",
    icon: Heart,
    color: "primary"
  },
  {
    title: "Seizures",
    description: "Keep pet safe from injury, time the seizure, stay calm",
    icon: Zap,
    color: "accent"
  }
];

export default function EmergencyPage() {
  const [userLocation, setUserLocation] = useState('');
  const [emergencyType, setEmergencyType] = useState('');

  const handleEmergencyCall = (phone: string) => {
    window.location.href = `tel:${phone}`;
  };

  const getDirections = (address: string) => {
    const encodedAddress = encodeURIComponent(address);
    window.open(`https://maps.google.com?q=${encodedAddress}`, '_blank');
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-r from-green-600 to-blue-600">
      {/* Emergency Alert Banner */}
      <div className="bg-gradient-to-r from-red-600 to-red-700 text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center gap-3 text-center">
            <AlertTriangle className="w-6 h-6" />
            <span className="font-bold">EMERGENCY HOTLINE: 1-800-PET-HELP (24/7)</span>
            <AlertTriangle className="w-6 h-6" />
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="py-12 relative overflow-hidden">
        <div className="absolute top-10 left-10 w-24 h-24 bg-gradient-to-r from-green-400/30 to-blue-400/30 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-r from-blue-400/30 to-green-400/30 rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-5xl font-bold mb-4 text-white">
              <span className="text-yellow-300">Emergency</span> Pet Care
            </h1>
            <p className="text-xl text-green-100 max-w-2xl mx-auto mb-8">
              Get immediate help for your pet in critical situations. Available 24/7.
            </p>
            
            {/* Quick Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <button
                onClick={() => handleEmergencyCall('1-800-PET-HELP')}
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-xl font-bold text-lg hover:shadow-lg hover:scale-105 transition-all duration-300 flex items-center justify-center gap-3"
              >
                <Phone className="w-6 h-6" />
                Call Emergency Hotline
              </button>
              <Link
                href="#find-clinic"
                className="bg-white text-green-600 px-8 py-4 rounded-xl hover:bg-gray-100 transition-all duration-300 font-semibold flex items-center justify-center gap-3"
              >
                <MapPin className="w-5 h-5" />
                Find Nearest Clinic
              </Link>
            </div>
          </div>

          {/* Emergency Assessment */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 max-w-2xl mx-auto border border-white/20">
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">
              Quick Emergency Assessment
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Emergency Type</label>
                <select
                  value={emergencyType}
                  onChange={(e) => setEmergencyType(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 text-gray-800"
                >
                  <option value="">Select emergency type</option>
                  <option value="choking">Choking</option>
                  <option value="poisoning">Poisoning</option>
                  <option value="bleeding">Severe Bleeding</option>
                  <option value="seizure">Seizure</option>
                  <option value="trauma">Trauma/Injury</option>
                  <option value="breathing">Breathing Problems</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-cool-700 mb-2">Your Location</label>
                <input
                  type="text"
                  placeholder="Enter your location"
                  value={userLocation}
                  onChange={(e) => setUserLocation(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-warm-500 focus:border-warm-500 transition-all duration-300"
                />
              </div>
            </div>
            <button className="btn-primary w-full mt-4">
              Get Emergency Guidance
            </button>
          </div>
        </div>
      </section>

      {/* Emergency Clinics */}
      <section id="find-clinic" className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-cool-800">
              24/7 Emergency Clinics Near You
            </h2>
            <p className="text-xl text-cool-600">
              Find the closest emergency veterinary care
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {emergencyServices.map((clinic) => (
              <div key={clinic.id} className="glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300 border-l-4 border-warm-500">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-cool-800 mb-2">{clinic.name}</h3>
                    <div className="flex items-center gap-2 text-sm text-cool-600 mb-1">
                      <MapPin className="w-4 h-4" />
                      <span>{clinic.address}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-cool-600">
                      <Navigation className="w-4 h-4" />
                      <span>{clinic.distance}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="px-3 py-1 bg-accent-500 text-white text-sm font-medium rounded-full mb-2">
                      {clinic.status}
                    </div>
                    <div className="text-sm text-cool-600">
                      ⭐ {clinic.rating}
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center gap-2 text-sm text-cool-600 mb-2">
                    <Clock className="w-4 h-4" />
                    <span>Response time: {clinic.responseTime}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {clinic.specialties.slice(0, 3).map((specialty, index) => (
                      <span key={index} className="px-2 py-1 bg-white/50 text-cool-600 text-xs rounded-full">
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => handleEmergencyCall(clinic.phone)}
                    className="bg-gradient-to-r from-warm-500 to-warm-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center gap-2 flex-1"
                  >
                    <Phone className="w-4 h-4" />
                    Call Now
                  </button>
                  <button
                    onClick={() => getDirections(clinic.address)}
                    className="btn-secondary flex items-center gap-2 flex-1"
                  >
                    <Navigation className="w-4 h-4" />
                    Directions
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Tips */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-cool-800">
              Emergency First Aid Tips
            </h2>
            <p className="text-xl text-cool-600">
              Quick actions that could save your pet's life
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {emergencyTips.map((tip, index) => {
              const Icon = tip.icon;
              return (
                <div key={index} className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-${tip.color}-500 to-${tip.color}-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-cool-800">{tip.title}</h3>
                  <p className="text-cool-600 text-sm">{tip.description}</p>
                </div>
              );
            })}
          </div>

          <div className="text-center mt-12">
            <div className="glass-card rounded-2xl p-6 max-w-2xl mx-auto">
              <h3 className="text-xl font-bold text-cool-800 mb-4">
                ⚠️ Important Reminder
              </h3>
              <p className="text-cool-600">
                These tips are for emergency situations only. Always contact a veterinary professional 
                immediately for proper medical care. Do not attempt treatments beyond your expertise.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Preparedness */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Emergency Preparedness Kit
              </h2>
              <p className="text-xl text-cool-600">
                Be prepared for emergencies with these essential items
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-3">
                <h4 className="font-bold text-cool-800">Medical Supplies</h4>
                <ul className="space-y-2 text-cool-600">
                  <li>• Gauze pads and bandages</li>
                  <li>• Antiseptic wipes</li>
                  <li>• Digital thermometer</li>
                  <li>• Hydrogen peroxide</li>
                  <li>• Emergency medications</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-bold text-cool-800">Important Information</h4>
                <ul className="space-y-2 text-cool-600">
                  <li>• Vet contact numbers</li>
                  <li>• Pet medical records</li>
                  <li>• Emergency clinic locations</li>
                  <li>• Pet insurance info</li>
                  <li>• Poison control number</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-bold text-cool-800">Emergency Tools</h4>
                <ul className="space-y-2 text-cool-600">
                  <li>• Pet carrier or crate</li>
                  <li>• Leash and collar</li>
                  <li>• Flashlight</li>
                  <li>• Blankets</li>
                  <li>• Emergency food/water</li>
                </ul>
              </div>
            </div>

            <div className="text-center mt-8">
              <Link href="/emergency/kit" className="btn-primary">
                Download Emergency Kit Checklist
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
