'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Search,
  MapPin,
  Star,
  Filter,
  Phone,
  Globe,
  Calendar,
  DollarSign,
  Clock,
  Award,
  Verified,
  ChevronDown,
  SlidersHorizontal,
  Heart,
  Share2,
  MessageCircle
} from 'lucide-react';
import Image from 'next/image';
import { getActiveProviders, getEmergencyProviders, searchProviders, type SearchFilters } from '@/lib/firebase/providers';
import { toast } from 'react-hot-toast';

interface Provider {
  id: string;
  businessName: string;
  ownerName: string;
  serviceType: string;
  rating: number;
  reviewCount: number;
  city: string;
  state: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  description: string;
  specialties: string[];
  verified: boolean;
  featured: boolean;
  profilePhoto?: string;
  responseTime: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  activeServices?: any[];
  hasActiveServices?: boolean;
  distance?: number;
  businessHours?: any;
}

// Remove hardcoded data - we'll fetch from Firebase

const serviceTypes = ['All Services', 'Grooming', 'Veterinary', 'Pet Hotel', 'Dog Walking', 'Training', 'Pet Sitting'];
const sortOptions = ['Recommended', 'Highest Rated', 'Nearest', 'Lowest Price', 'Most Reviews'];

export default function SearchPage() {
  const searchParams = useSearchParams();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All Services');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedPetType, setSelectedPetType] = useState('');
  const [sortBy, setSortBy] = useState('Recommended');
  const [showFilters, setShowFilters] = useState(false);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    minRating: 0,
    maxDistance: 20,
    priceRange: 'any',
    availability: 'any',
    verified: false
  });

  // Initialize search from URL parameters
  useEffect(() => {
    const location = searchParams.get('location') || '';
    const service = searchParams.get('service') || 'All Services';
    const date = searchParams.get('date') || '';
    const petType = searchParams.get('petType') || '';

    setSelectedLocation(location);
    setSelectedService(service);
    setSelectedDate(date);
    setSelectedPetType(petType);

    // Perform initial search with URL parameters
    if (location || service !== 'All Services' || date || petType) {
      performSearch({ location, service, date, petType });
    } else {
      loadProviders();
    }
  }, [searchParams]);

  const loadProviders = async () => {
    try {
      setLoading(true);
      const activeProviders = await getActiveProviders();
      setProviders(activeProviders.map((p: any) => ({ ...p, id: p.id ?? '' })));
    } catch (error) {
      console.error('Error loading providers:', error);
      toast.error('Failed to load providers');
    } finally {
      setLoading(false);
    }
  };

  // Enhanced search function using the new searchProviders function
  const performSearch = async (searchFilters?: Partial<SearchFilters>) => {
    try {
      setLoading(true);

      const filters: SearchFilters = {
        location: searchFilters?.location || selectedLocation,
        service: searchFilters?.service || selectedService,
        date: searchFilters?.date || selectedDate,
        petType: searchFilters?.petType || selectedPetType,
        maxDistance: 20
      };

      // Check for emergency keywords
      const emergencyKeywords = ['emergency', 'vet', 'veterinary', 'hospital', 'clinic'];
      const isEmergencySearch = searchQuery && emergencyKeywords.some(keyword =>
        searchQuery.toLowerCase().includes(keyword)
      );

      let results;
      if (isEmergencySearch) {
        results = await getEmergencyProviders();
      } else {
        results = await searchProviders(filters);
      }

      setProviders(results.map((p: any) => ({ ...p, id: p.id ?? '' })));
      toast.success(`Found ${results.length} providers`);
    } catch (error) {
      console.error('Error searching providers:', error);
      toast.error('Search failed');
    } finally {
      setLoading(false);
    }
  };

  // Handle search with current form values
  const handleSearch = async () => {
    await performSearch();
  };

  const toggleFavorite = (providerId: string) => {
    setFavorites(prev =>
      prev.includes(providerId)
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = !searchQuery ||
      provider.businessName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.serviceType.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.specialties.some(specialty => specialty.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesService = selectedService === 'All Services' || provider.serviceType === selectedService;
    const matchesRating = provider.rating >= filters.minRating;
    const matchesVerified = !filters.verified || provider.verified;

    return matchesSearch && matchesService && matchesRating && matchesVerified;
  });

  return (
    <>
      <div className="min-h-screen pt-20 bg-gradient-to-br from-gray-50 via-primary-50 to-accent-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 rounded-full bg-primary-400 animate-float"></div>
        <div className="absolute top-32 right-20 w-16 h-16 rounded-full bg-accent-400 animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 rounded-full bg-secondary-400 animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Find Pet Care <span className="bg-gradient-to-r from-primary-500 to-accent-500 bg-clip-text text-transparent">Providers</span>
          </h1>
          <p className="text-gray-700 text-lg">Discover trusted professionals with active services for your beloved pets</p>
        </div>

        {/* Enhanced Search Bar */}
        <div className="backdrop-blur-md bg-white/95 border border-primary-200/50 rounded-3xl p-6 mb-8 shadow-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
            {/* Location */}
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
              <input
                type="text"
                placeholder="Location"
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-xl border border-primary-200/50 focus:border-primary-400 focus:ring-2 focus:ring-primary-200 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 placeholder-gray-500"
              />
            </div>

            {/* Service */}
            <select
              value={selectedService}
              onChange={(e) => setSelectedService(e.target.value)}
              className="px-4 py-3 rounded-xl border border-primary-200/50 focus:border-primary-400 focus:ring-2 focus:ring-primary-200 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
            >
              {serviceTypes.map(service => (
                <option key={service} value={service}>{service}</option>
              ))}
            </select>

            {/* Date */}
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-xl border border-primary-200/50 focus:border-primary-400 focus:ring-2 focus:ring-primary-200 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
              />
            </div>

            {/* Pet Type */}
            <select
              value={selectedPetType}
              onChange={(e) => setSelectedPetType(e.target.value)}
              className="px-4 py-3 rounded-xl border border-primary-200/50 focus:border-primary-400 focus:ring-2 focus:ring-primary-200 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
            >
              <option value="">All Pets</option>
              <option value="dog">Dogs</option>
              <option value="cat">Cats</option>
              <option value="bird">Birds</option>
              <option value="rabbit">Rabbits</option>
              <option value="fish">Fish</option>
              <option value="reptile">Reptiles</option>
              <option value="other">Other</option>
            </select>

            {/* Search Button */}
            <button
              onClick={handleSearch}
              disabled={loading}
              className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 flex items-center justify-center gap-2"
            >
              <Search className="w-5 h-5" />
              {loading ? 'Searching...' : 'Search'}
            </button>
          </div>

          {/* Additional Search Bar */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
            <input
              type="text"
              placeholder="Search services, emergency care, veterinary..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full pl-12 pr-4 py-3 rounded-xl border border-primary-200/50 focus:border-primary-400 focus:ring-2 focus:ring-primary-200 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 placeholder-gray-500"
            />
          </div>

              <button
                onClick={() => setShowFilters(!showFilters)}
                className="border border-primary-300 text-primary-600 hover:bg-primary-50 px-4 py-4 rounded-2xl font-medium transition-all duration-300 flex items-center gap-2"
              >
                <SlidersHorizontal className="w-5 h-5" />
                Filters
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-white/30">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Min Rating</label>
                  <select
                    value={filters.minRating}
                    onChange={(e) => setFilters({...filters, minRating: Number(e.target.value)})}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value={0}>Any Rating</option>
                    <option value={4}>4+ Stars</option>
                    <option value={4.5}>4.5+ Stars</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Max Distance</label>
                  <select
                    value={filters.maxDistance}
                    onChange={(e) => setFilters({...filters, maxDistance: Number(e.target.value)})}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value={50}>Any Distance</option>
                    <option value={1}>Within 1 mile</option>
                    <option value={5}>Within 5 miles</option>
                    <option value={10}>Within 10 miles</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Availability</label>
                  <select
                    value={filters.availability}
                    onChange={(e) => setFilters({...filters, availability: e.target.value})}
                    className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="any">Any Time</option>
                    <option value="today">Available Today</option>
                    <option value="week">This Week</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={filters.verified}
                      onChange={(e) => setFilters({...filters, verified: e.target.checked})}
                      className="w-4 h-4 text-primary-500 border-2 border-white/30 rounded focus:ring-primary-500"
                    />
                    <span className="text-sm text-cool-700">Verified Only</span>
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Header */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-cool-700">
            <span className="font-bold">{filteredProviders.length}</span> providers found
          </p>

          <div className="flex items-center gap-2">
            <span className="text-cool-700">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {sortOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Provider Cards */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredProviders.map((provider) => (
              <div key={provider.id} className="backdrop-blur-md bg-white/95 border border-primary-200/30 rounded-3xl p-6 hover:shadow-2xl hover:bg-white/98 transition-all duration-300 group">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary-400 to-accent-400 flex items-center justify-center shadow-lg">
                      {provider.profilePhoto ? (
                        <Image
                          src={provider.profilePhoto}
                          alt={provider.businessName}
                          width={64}
                          height={64}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-white font-bold text-lg">
                          {provider.businessName.split(' ').map(n => n[0]).join('')}
                        </span>
                      )}
                    </div>

                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary-700 transition-colors">
                          {provider.businessName}
                        </h3>
                        {provider.verified && (
                          <Verified className="w-5 h-5 text-primary-500" />
                        )}
                        {provider.featured && (
                          <Award className="w-5 h-5 text-secondary-500" />
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-700">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-secondary-500 fill-current" />
                          <span className="font-medium">{provider.rating || 'New'}</span>
                          {provider.reviewCount > 0 && (
                            <span>({provider.reviewCount} reviews)</span>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4 text-primary-500" />
                          <span>{provider.city}, {provider.state}</span>
                          {provider.distance && (
                            <span className="text-primary-600 font-medium">• {provider.distance} mi</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => toggleFavorite(provider.id)}
                      className={`p-2 rounded-full transition-all duration-200 ${
                        favorites.includes(provider.id)
                          ? 'bg-accent-100 text-accent-500 shadow-md'
                          : 'bg-cream-100 text-warm-500 hover:bg-accent-100 hover:text-accent-500 hover:shadow-md'
                      }`}
                    >
                      <Heart className={`w-5 h-5 ${favorites.includes(provider.id) ? 'fill-current' : ''}`} />
                    </button>
                    <button className="p-2 rounded-full bg-cream-100 text-warm-500 hover:bg-teal-100 hover:text-teal-600 transition-all duration-200">
                      <Share2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-800 mb-4 leading-relaxed">{provider.description}</p>

                {/* Service Type & Specialties */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    <span className="px-4 py-2 bg-gradient-to-r from-primary-100 to-primary-200 text-primary-700 rounded-full text-sm font-medium">
                      {provider.serviceType}
                    </span>
                    {provider.specialties?.slice(0, 2).map((specialty, index) => (
                      <span key={index} className="px-3 py-1 bg-secondary-100 text-secondary-700 rounded-full text-sm">
                        {specialty}
                      </span>
                    ))}
                    {provider.specialties?.length > 2 && (
                      <span className="px-3 py-1 bg-cream-200 text-warm-700 rounded-full text-sm">
                        +{provider.specialties.length - 2} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Active Services Count & Business Hours */}
                <div className="mb-4 space-y-3">
                  {provider.activeServices && provider.activeServices.length > 0 && (
                    <div className="p-3 bg-gradient-to-r from-primary-50 to-accent-50 rounded-2xl border border-primary-100">
                      <div className="flex items-center gap-2 text-sm text-primary-700">
                        <Award className="w-4 h-4" />
                        <span className="font-medium">{provider.activeServices.length} Active Services Available</span>
                      </div>
                    </div>
                  )}

                  {/* Business Hours */}
                  {provider.businessHours && (
                    <div className="p-3 bg-gradient-to-r from-cool-50 to-gray-50 rounded-2xl border border-cool-100">
                      <div className="flex items-center gap-2 text-sm text-cool-700">
                        <Clock className="w-4 h-4" />
                        <span className="font-medium">
                          {(() => {
                            const today = new Date().toLocaleString('en-US', { weekday: 'short' }).toLowerCase(); // Get day abbreviation
                            const todayHours = provider.businessHours[today] || provider.businessHours['monday'];
                            if (todayHours?.closed) {
                              return 'Closed today';
                            }
                            return `Open today: ${todayHours?.open || '9:00 AM'} - ${todayHours?.close || '5:00 PM'}`;
                          })()}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Contact Details */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-primary-500" />
                    <span className="text-gray-800">{provider.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-accent-500" />
                    <span className="text-gray-800">Responds in {provider.responseTime || '< 1 hour'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-secondary-500" />
                    <span className="text-gray-800">{provider.address}</span>
                  </div>
                  {provider.website && (
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4 text-warm-500" />
                      <span className="text-gray-800">Website Available</span>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex gap-3">
                  <button
                    onClick={() => window.location.href = `/appointments?providerId=${provider.id}`}
                    className="flex-1 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white py-3 px-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                  >
                    <Calendar className="w-4 h-4" />
                    Book Appointment
                  </button>
                  <button
                    onClick={() => window.location.href = `/provider/public/${provider.id}`}
                    className="border border-primary-300 text-primary-600 hover:bg-primary-50 py-3 px-4 rounded-2xl font-medium transition-all duration-300 flex items-center gap-2"
                  >
                    <MessageCircle className="w-4 h-4" />
                    View Profile
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {!loading && filteredProviders.length === 0 && (
          <div className="backdrop-blur-md bg-white/95 border border-primary-200/30 rounded-3xl p-12 text-center">
            <Search className="w-16 h-16 text-primary-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-900 mb-2">No active providers found</h3>
            <p className="text-gray-700 mb-6">
              We couldn't find any providers with active services matching your criteria.
              Try adjusting your search or check back later.
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedService('All Services');
                setFilters({
                  minRating: 0,
                  maxDistance: 50,
                  priceRange: 'any',
                  availability: 'any',
                  verified: false
                });
                loadProviders();
              }}
              className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Clear All Filters
            </button>
          </div>
        )}
    </>
  );
}