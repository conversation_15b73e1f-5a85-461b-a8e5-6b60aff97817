'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  Search,
  MapPin,
  Star,
  Phone,
  Calendar,
  Clock,
  Award,
  Verified,
  SlidersHorizontal,
  Heart,
  Share2,
  MessageCircle,
  Mail
} from 'lucide-react';
import Image from 'next/image';
import { getActiveProviders, getEmergencyProviders, searchProviders, type SearchFilters } from '@/lib/firebase/providers';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';

interface Provider {
  id: string;
  businessName: string;
  ownerName: string;
  serviceType: string;
  rating: number;
  reviewCount: number;
  city: string;
  state: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  description: string;
  specialties: string[];
  verified: boolean;
  featured: boolean;
  profilePhoto?: string;
  responseTime: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  activeServices?: any[];
  hasActiveServices?: boolean;
  distance?: number;
  businessHours?: any;
}

const serviceTypes = ['All Services', 'Grooming', 'Veterinary', 'Pet Hotel', 'Dog Walking', 'Training', 'Pet Sitting'];
const sortOptions = ['Recommended', 'Highest Rated', 'Nearest', 'Lowest Price', 'Most Reviews'];

export default function SearchPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // State
  const [providers, setProviders] = useState<Provider[]>([]);
  const [filteredProviders, setFilteredProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Search filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All Services');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [sortBy, setSortBy] = useState('Recommended');
  const [showFilters, setShowFilters] = useState(false);

  // Load providers from Firebase
  const loadProviders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔍 Loading providers from Firebase...');
      
      // Get only approved providers with active services
      const activeProviders = await getActiveProviders();
      console.log(`📊 Found ${activeProviders.length} active providers`);

      setProviders(activeProviders.map((p: any) => ({ ...p, id: p.id ?? '' })));
      setFilteredProviders(activeProviders.map((p: any) => ({ ...p, id: p.id ?? '' })));
      
      if (activeProviders.length === 0) {
        toast.error('No providers found. Please check your Firebase data.');
      } else {
        toast.success(`Found ${activeProviders.length} providers`);
      }
    } catch (error) {
      console.error('Error loading providers:', error);
      toast.error('Failed to load providers');
      setError('Failed to load providers. Please try again.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Initialize from URL params and load data
  useEffect(() => {
    const query = searchParams.get('q') || '';
    const service = searchParams.get('service') || 'All Services';
    const location = searchParams.get('location') || '';

    setSearchQuery(query);
    setSelectedService(service);
    setSelectedLocation(location);

    // Load providers from Firebase
    loadProviders();
  }, [searchParams, loadProviders]);

  // Filter providers based on search criteria
  useEffect(() => {
    let filtered = [...providers];

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(provider =>
        provider.businessName.toLowerCase().includes(query) ||
        provider.serviceType.toLowerCase().includes(query) ||
        provider.description.toLowerCase().includes(query) ||
        provider.specialties.some(specialty => specialty.toLowerCase().includes(query)) ||
        provider.city.toLowerCase().includes(query) ||
        provider.state.toLowerCase().includes(query)
      );
    }

    // Filter by service type
    if (selectedService !== 'All Services') {
      filtered = filtered.filter(provider =>
        provider.serviceType === selectedService
      );
    }

    // Filter by location
    if (selectedLocation.trim()) {
      const location = selectedLocation.toLowerCase();
      filtered = filtered.filter(provider =>
        provider.city.toLowerCase().includes(location) ||
        provider.state.toLowerCase().includes(location) ||
        provider.address.toLowerCase().includes(location)
      );
    }

    // Sort providers
    switch (sortBy) {
      case 'Highest Rated':
        filtered.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'Most Reviews':
        filtered.sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
        break;
      case 'Lowest Price':
        // Would need price data from services
        break;
      default: // Recommended
        filtered.sort((a, b) => {
          // Featured providers first, then by rating
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return (b.rating || 0) - (a.rating || 0);
        });
    }

    setFilteredProviders(filtered);
  }, [providers, searchQuery, selectedService, selectedLocation, sortBy]);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Update URL with search params
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedService !== 'All Services') params.set('service', selectedService);
    if (selectedLocation) params.set('location', selectedLocation);
    
    router.push(`/search?${params.toString()}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-mint-50 via-sky-50 to-blue-50">
      {/* Hero Section with Search */}
      <div className="relative bg-gradient-to-br from-mint-400 via-sky-400 to-blue-500 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Find Perfect Pet Care
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Connect with verified pet service professionals in your area
            </p>
          </div>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="max-w-4xl mx-auto">
            <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl p-6 shadow-2xl">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search Input */}
                <div className="md:col-span-2 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search services, providers, or locations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800 placeholder-gray-500"
                  />
                </div>

                {/* Service Type */}
                <div>
                  <select
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="w-full py-4 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                  >
                    {serviceTypes.map(service => (
                      <option key={service} value={service}>{service}</option>
                    ))}
                  </select>
                </div>

                {/* Search Button */}
                <div>
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-aqua-500 to-sky-500 hover:from-aqua-600 hover:to-sky-600 text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50"
                  >
                    {loading ? 'Searching...' : 'Search'}
                  </button>
                </div>
              </div>

              {/* Additional Filters */}
              <div className="mt-4 flex flex-wrap gap-4">
                <input
                  type="text"
                  placeholder="Location (city, state, zip)"
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="flex-1 min-w-[200px] py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800 placeholder-gray-500"
                />
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Results Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {loading ? 'Searching...' : `${filteredProviders.length} Providers Found`}
            </h2>
            {searchQuery && (
              <p className="text-gray-600 mt-1">
                Results for "{searchQuery}"
              </p>
            )}
          </div>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors"
          >
            <SlidersHorizontal className="w-4 h-4" />
            Filters
          </button>
        </div>

        {/* Error State */}
        {error && (
          <div className="backdrop-blur-xl bg-red-50/90 border border-red-200/30 rounded-3xl p-6 mb-8">
            <p className="text-red-600 text-center">{error}</p>
            <button
              onClick={loadProviders}
              className="mt-4 mx-auto block px-6 py-2 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Loading State */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-aqua-500"></div>
          </div>
        ) : (
          <>
            {/* Provider Cards */}
            {filteredProviders.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredProviders.map((provider) => {
                  // Determine which card to show
                  const showSimpleCard = !user || !isAuthenticated;
                  
                  return (
                    <ProviderCard 
                      key={provider.id} 
                      provider={provider} 
                      showSimpleCard={showSimpleCard}
                    />
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="backdrop-blur-xl bg-white/90 border border-gray-200/30 rounded-3xl p-12 max-w-md mx-auto">
                  <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No providers found</h3>
                  <p className="text-gray-600 mb-6">
                    Try adjusting your search criteria or check if providers are added to your Firebase database.
                  </p>
                  <button
                    onClick={loadProviders}
                    className="px-6 py-3 bg-gradient-to-r from-aqua-500 to-sky-500 text-white rounded-xl hover:from-aqua-600 hover:to-sky-600 transition-all"
                  >
                    Refresh Search
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

// Provider Card Component
function ProviderCard({ provider, showSimpleCard }: { provider: Provider; showSimpleCard: boolean }) {
  if (showSimpleCard) {
    // Simple card for non-logged users
    return (
      <div className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
        {/* Provider Image & Name */}
        <div className="flex items-center gap-4 mb-4">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-aqua-500 to-sky-500 flex items-center justify-center shadow-lg">
            {provider.profilePhoto ? (
              <Image
                src={provider.profilePhoto}
                alt={provider.businessName}
                width={64}
                height={64}
                className="w-16 h-16 rounded-2xl object-cover"
              />
            ) : (
              <span className="text-white font-bold text-lg">
                {provider.businessName?.split(' ').map(n => n[0]).join('') || 'P'}
              </span>
            )}
          </div>
          <div className="flex-1">
            <h3 className="font-bold text-lg text-gray-900 group-hover:text-aqua-600 transition-colors">
              {provider.businessName}
            </h3>
            <div className="flex items-center gap-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(provider.rating || 0)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
              <span className="text-sm text-gray-600 ml-1">
                {provider.rating?.toFixed(1) || 'New'}
              </span>
            </div>
          </div>
        </div>

        {/* Location */}
        <div className="flex items-center gap-2 mb-3">
          <MapPin className="w-4 h-4 text-aqua-500" />
          <span className="text-gray-700">{provider.city}, {provider.state}</span>
        </div>

        {/* Type of Service */}
        <div className="mb-3">
          <span className="px-3 py-1 bg-gradient-to-r from-aqua-100 to-sky-100 text-aqua-700 rounded-full text-sm font-medium">
            {provider.serviceType}
          </span>
        </div>

        {/* Book Now Button */}
        <button
          onClick={() => window.location.href = '/auth/signup'}
          className="w-full bg-gradient-to-r from-aqua-500 to-sky-500 hover:from-aqua-600 hover:to-sky-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
        >
          <Calendar className="w-4 h-4" />
          Book Now
        </button>
      </div>
    );
  }

  // Full card for logged-in users
  return (
    <div className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-3xl p-6 hover:shadow-2xl hover:bg-white/95 transition-all duration-300 group hover:scale-[1.02]">
      {/* Header with provider info */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-aqua-500 to-sky-500 flex items-center justify-center shadow-lg">
            {provider.profilePhoto ? (
              <Image
                src={provider.profilePhoto}
                alt={provider.businessName}
                width={64}
                height={64}
                className="w-16 h-16 rounded-2xl object-cover"
              />
            ) : (
              <span className="text-white font-bold text-lg">
                {provider.businessName?.split(' ').map(n => n[0]).join('') || 'P'}
              </span>
            )}
          </div>

          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-xl font-bold text-gray-900 group-hover:text-aqua-700 transition-colors">
                {provider.businessName}
              </h3>
              {provider.verified && (
                <Verified className="w-5 h-5 text-green-500" />
              )}
              {provider.featured && (
                <Award className="w-5 h-5 text-yellow-500" />
              )}
            </div>
            <p className="text-gray-600 font-medium">{provider.ownerName}</p>
            <p className="text-sm text-gray-500">{provider.serviceType}</p>
          </div>
        </div>

        <div className="flex gap-2">
          <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
            <Heart className="w-5 h-5" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <Share2 className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Rating and Reviews */}
      <div className="flex items-center gap-4 mb-4">
        <div className="flex items-center gap-1">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`w-4 h-4 ${
                i < Math.floor(provider.rating || 0)
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              }`}
            />
          ))}
          <span className="font-semibold text-gray-900 ml-2">
            {provider.rating?.toFixed(1) || 'New'}
          </span>
          <span className="text-gray-500">
            ({provider.reviewCount || 0} reviews)
          </span>
        </div>
      </div>

      {/* Description */}
      <p className="text-gray-600 mb-4 line-clamp-2">{provider.description}</p>

      {/* Specialties */}
      {provider.specialties && provider.specialties.length > 0 && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {provider.specialties.slice(0, 3).map((specialty, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gradient-to-r from-aqua-100 to-sky-100 text-aqua-700 rounded-full text-xs font-medium"
              >
                {specialty}
              </span>
            ))}
            {provider.specialties.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                +{provider.specialties.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Contact Info */}
      <div className="space-y-2 mb-6">
        <div className="flex items-center gap-3 text-sm text-gray-600">
          <MapPin className="w-4 h-4 text-aqua-500" />
          <span>{provider.address}, {provider.city}, {provider.state}</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-gray-600">
          <Phone className="w-4 h-4 text-aqua-500" />
          <span>{provider.phone}</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-gray-600">
          <Clock className="w-4 h-4 text-aqua-500" />
          <span>Responds {provider.responseTime}</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button className="flex-1 bg-gradient-to-r from-aqua-500 to-sky-500 hover:from-aqua-600 hover:to-sky-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2">
          <Calendar className="w-4 h-4" />
          Book Now
        </button>
        <button className="px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
          <MessageCircle className="w-5 h-5 text-gray-600" />
        </button>
        <button className="px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
          <Mail className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    </div>
  );
}
