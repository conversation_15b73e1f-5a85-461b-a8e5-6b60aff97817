'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Search,
  MapPin,
  Star,
  Phone,
  Calendar,
  Clock,
  Award,
  Verified,
  SlidersHorizontal,
  Heart,
  Share2,
  MessageCircle,
  Mail
} from 'lucide-react';
import Image from 'next/image';
import { getActiveProviders, getEmergencyProviders, searchProviders, type SearchFilters } from '@/lib/firebase/providers';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';

interface Provider {
  id: string;
  businessName: string;
  ownerName: string;
  serviceType: string;
  rating: number;
  reviewCount: number;
  city: string;
  state: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  description: string;
  specialties: string[];
  verified: boolean;
  featured: boolean;
  profilePhoto?: string;
  responseTime: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  activeServices?: any[];
  hasActiveServices?: boolean;
  distance?: number;
  businessHours?: any;
}

// Remove hardcoded data - we'll fetch from Firebase

const serviceTypes = ['All Services', 'Grooming', 'Veterinary', 'Pet Hotel', 'Dog Walking', 'Training', 'Pet Sitting'];
const sortOptions = ['Recommended', 'Highest Rated', 'Nearest', 'Lowest Price', 'Most Reviews'];

export default function SearchPage() {
  const searchParams = useSearchParams();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);

  // Debug authentication state
  console.log('🔐 Auth Debug:', {
    user: user ? 'User exists' : 'No user',
    isAuthenticated,
    authLoading,
    shouldShowSimpleCard: !isAuthenticated && !authLoading
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All Services');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedPetType, setSelectedPetType] = useState('');
  const [sortBy, setSortBy] = useState('Recommended');
  const [showFilters, setShowFilters] = useState(false);
  const [favorites, setFavorites] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    minRating: 0,
    maxDistance: 20,
    priceRange: 'any',
    availability: 'any',
    verified: false
  });

  // Initialize search from URL parameters
  useEffect(() => {
    const location = searchParams.get('location') || '';
    const service = searchParams.get('service') || 'All Services';
    const date = searchParams.get('date') || '';
    const petType = searchParams.get('petType') || '';

    setSelectedLocation(location);
    setSelectedService(service);
    setSelectedDate(date);
    setSelectedPetType(petType);

    // Perform initial search with URL parameters
    if (location || service !== 'All Services' || date || petType) {
      performSearch({ location, service, date, petType });
    } else {
      loadProviders();
    }
  }, [searchParams]);

  const loadProviders = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading active providers for search...');

      // Get only approved providers with active services
      const activeProviders = await getActiveProviders();
      console.log(`📊 Found ${activeProviders.length} active providers`);

      setProviders(activeProviders.map((p: any) => ({ ...p, id: p.id ?? '' })));
    } catch (error) {
      console.error('Error loading providers:', error);
      toast.error('Failed to load providers');
    } finally {
      setLoading(false);
    }
  };

  // Enhanced search function using the new searchProviders function
  const performSearch = async (searchFilters?: Partial<SearchFilters>) => {
    try {
      setLoading(true);

      const filters: SearchFilters = {
        location: searchFilters?.location || selectedLocation,
        service: searchFilters?.service || selectedService,
        date: searchFilters?.date || selectedDate,
        petType: searchFilters?.petType || selectedPetType,
        maxDistance: 20
      };

      // Check for emergency keywords
      const emergencyKeywords = ['emergency', 'vet', 'veterinary', 'hospital', 'clinic'];
      const isEmergencySearch = searchQuery && emergencyKeywords.some(keyword =>
        searchQuery.toLowerCase().includes(keyword)
      );

      let results;
      if (isEmergencySearch) {
        results = await getEmergencyProviders();
      } else {
        results = await searchProviders(filters);
      }

      setProviders(results.map((p: any) => ({ ...p, id: p.id ?? '' })));
      toast.success(`Found ${results.length} providers`);
    } catch (error) {
      console.error('Error searching providers:', error);
      toast.error('Search failed');
    } finally {
      setLoading(false);
    }
  };

  // Handle search with current form values
  const handleSearch = async () => {
    await performSearch();
  };

  // Filter providers based on search criteria
  const filteredProviders = providers.filter(provider => {
    // Search query filter
    const matchesSearch = !searchQuery ||
      provider.businessName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.serviceType?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.specialties?.some(specialty => specialty.toLowerCase().includes(searchQuery.toLowerCase()));

    // Service type filter - improved logic
    const matchesService = selectedService === 'All Services' ||
      provider.serviceType?.toLowerCase() === selectedService.toLowerCase() ||
      provider.serviceType?.toLowerCase().includes(selectedService.toLowerCase()) ||
      // Handle common variations
      (selectedService === 'Grooming' && provider.serviceType?.toLowerCase().includes('groom')) ||
      (selectedService === 'Pet Sitting' && provider.serviceType?.toLowerCase().includes('sitting')) ||
      (selectedService === 'Dog Walking' && provider.serviceType?.toLowerCase().includes('walking')) ||
      (selectedService === 'Veterinary' && (provider.serviceType?.toLowerCase().includes('vet') || provider.serviceType?.toLowerCase().includes('veterinary'))) ||
      (selectedService === 'Training' && provider.serviceType?.toLowerCase().includes('train'));

    // Location filter
    const matchesLocation = !selectedLocation ||
      provider.city?.toLowerCase().includes(selectedLocation.toLowerCase()) ||
      provider.state?.toLowerCase().includes(selectedLocation.toLowerCase()) ||
      provider.address?.toLowerCase().includes(selectedLocation.toLowerCase());

    // Pet type filter
    const matchesPetType = !selectedPetType ||
      provider.specialties?.some(specialty => specialty.toLowerCase().includes(selectedPetType.toLowerCase()));

    // Rating filter
    const matchesRating = !filters.minRating || (provider.rating && provider.rating >= filters.minRating);

    // Verified filter
    const matchesVerified = !filters.verified || provider.verified;

    console.log(`🔍 Provider ${provider.businessName}:`, {
      serviceType: provider.serviceType,
      selectedService,
      matchesService,
      matchesSearch,
      matchesLocation,
      matchesPetType,
      matchesRating,
      matchesVerified,
      finalMatch: matchesSearch && matchesService && matchesLocation && matchesPetType && matchesRating && matchesVerified
    });

    return matchesSearch && matchesService && matchesLocation && matchesPetType && matchesRating && matchesVerified;
  });

  const toggleFavorite = (providerId: string) => {
    setFavorites(prev =>
      prev.includes(providerId)
        ? prev.filter(id => id !== providerId)
        : [...prev, providerId]
    );
  };



  return (
    <div className="min-h-screen bg-gradient-to-r from-green-600 to-blue-600 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-green-400/30 to-blue-400/20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-blue-400/30 to-green-400/20 blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full bg-gradient-to-r from-green-300/20 to-blue-300/20 blur-3xl"></div>
      </div>

      <div className="relative z-10 pt-24 pb-12">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 text-white">
              Find Pet Services
            </h1>
            <p className="text-xl text-green-100 max-w-2xl mx-auto leading-relaxed">
              Search for trusted pet care providers and their services in your area
            </p>
          </div>

          {/* Search Form */}
          <div className="max-w-6xl mx-auto mb-12">
            <div className="bg-white/95 backdrop-blur-sm border border-white/20 rounded-3xl p-8 shadow-2xl">
              {/* Main Search Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {/* Location */}
                <div className="relative group">
                  <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-500 group-focus-within:text-blue-600 transition-colors" />
                  <input
                    type="text"
                    placeholder="Enter location"
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 placeholder-gray-500 shadow-sm hover:shadow-md"
                  />
                </div>

                {/* Service Type */}
                <div className="relative group">
                  <select
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="w-full px-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 shadow-sm hover:shadow-md appearance-none cursor-pointer"
                  >
                    {serviceTypes.map(service => (
                      <option key={service} value={service}>{service}</option>
                    ))}
                  </select>
                </div>

                {/* Date */}
                <div className="relative group">
                  <Calendar className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-500 group-focus-within:text-blue-600 transition-colors" />
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 shadow-sm hover:shadow-md"
                  />
                </div>

                {/* Pet Type */}
                <div className="relative group">
                  <select
                    value={selectedPetType}
                    onChange={(e) => setSelectedPetType(e.target.value)}
                    className="w-full px-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 shadow-sm hover:shadow-md appearance-none cursor-pointer"
                  >
                    <option value="">All Pets</option>
                    <option value="dog">Dogs</option>
                    <option value="cat">Cats</option>
                    <option value="bird">Birds</option>
                    <option value="rabbit">Rabbits</option>
                    <option value="fish">Fish</option>
                    <option value="reptile">Reptiles</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              {/* Search Bar and Button */}
              <div className="flex gap-4">
                <div className="flex-1 relative group">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-500 group-focus-within:text-blue-600 transition-colors" />
                  <input
                    type="text"
                    placeholder="Search services, emergency care, veterinary..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800 placeholder-gray-500 shadow-sm hover:shadow-md"
                  />
                </div>

                <button
                  onClick={handleSearch}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 flex items-center gap-3 min-w-[140px] justify-center"
                >
                  <Search className="w-5 h-5" />
                  {loading ? 'Searching...' : 'Search'}
                </button>
              </div>
            </div>
          </div>

          {/* Results Section */}
          <div className="max-w-7xl mx-auto">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-8 gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Available Providers
                </h2>
                <p className="text-gray-600">
                  <span className="font-semibold text-blue-600">{filteredProviders.length}</span> providers found
                </p>
              </div>

              <div className="flex items-center gap-4">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 px-4 py-2 rounded-xl border border-blue-200 text-blue-600 hover:bg-blue-50 transition-all duration-200"
                >
                  <SlidersHorizontal className="w-4 h-4" />
                  Filters
                </button>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 rounded-xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="mb-8 p-6 backdrop-blur-xl bg-white/80 border border-blue-200/30 rounded-2xl shadow-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Minimum Rating</label>
                    <select
                      value={filters.minRating}
                      onChange={(e) => setFilters({...filters, minRating: Number(e.target.value)})}
                      className="w-full px-4 py-3 rounded-xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
                    >
                      <option value={0}>Any Rating</option>
                      <option value={3}>3+ Stars</option>
                      <option value={4}>4+ Stars</option>
                      <option value={4.5}>4.5+ Stars</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Max Distance</label>
                    <select
                      value={filters.maxDistance}
                      onChange={(e) => setFilters({...filters, maxDistance: Number(e.target.value)})}
                      className="w-full px-4 py-3 rounded-xl border border-blue-200/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-200/50 outline-none transition-all bg-white/90 backdrop-blur-sm text-gray-800"
                    >
                      <option value={10}>Within 10 miles</option>
                      <option value={25}>Within 25 miles</option>
                      <option value={50}>Within 50 miles</option>
                      <option value={100}>Within 100 miles</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Verified Only</label>
                    <label className="flex items-center p-3 rounded-xl border border-blue-200/50 bg-white/90 backdrop-blur-sm cursor-pointer hover:bg-blue-50/50 transition-all">
                      <input
                        type="checkbox"
                        checked={filters.verified}
                        onChange={(e) => setFilters({...filters, verified: e.target.checked})}
                        className="rounded border-blue-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">Show only verified providers</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* No Results Message */}
            {!loading && filteredProviders.length === 0 && (
              <div className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-2xl p-12 text-center shadow-lg">
                <Search className="w-16 h-16 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">No providers available</h3>
                <p className="text-gray-600 mb-6">
                  {providers.length === 0
                    ? "No providers are currently available in your area."
                    : "No providers match your search criteria. Try adjusting your filters or search terms."
                  }
                </p>
                <div className="flex gap-4 justify-center">
                  <button
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedService('All Services');
                      setSelectedLocation('');
                      setSelectedDate('');
                      setSelectedPetType('');
                      setFilters({
                        minRating: 0,
                        maxDistance: 20,
                        priceRange: 'any',
                        availability: 'any',
                        verified: false
                      });
                    }}
                    className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
                  >
                    Clear All Filters
                  </button>
                  <button
                    onClick={loadProviders}
                    className="px-6 py-3 border border-blue-600 text-blue-600 rounded-xl hover:bg-blue-50 transition-colors"
                  >
                    Refresh Results
                  </button>
                </div>
              </div>
            )}

            {/* Provider Cards */}
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredProviders.map((provider) => {
                  // Determine which card to show
                  const showSimpleCard = !user || !isAuthenticated;
                  console.log('🎯 Card decision for', provider.businessName, ':', {
                    showSimpleCard,
                    hasUser: !!user,
                    isAuthenticated,
                    authLoading
                  });

                  return (
                    <div key={provider.id}>
                      {showSimpleCard ? (
                        // SIMPLE CARD FOR NON-LOGGED-IN USERS
                        <div className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
                          {/* Provider Image & Name */}
                          <div className="flex items-center gap-4 mb-4">
                            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                              {provider.profilePhoto ? (
                                <Image
                                  src={provider.profilePhoto}
                                  alt={provider.businessName}
                                  width={64}
                                  height={64}
                                  className="w-16 h-16 rounded-2xl object-cover"
                                />
                              ) : (
                                <span className="text-white font-bold text-lg">
                                  {provider.businessName?.split(' ').map(n => n[0]).join('') || 'P'}
                                </span>
                              )}
                            </div>
                            <div className="flex-1">
                              <h3 className="font-bold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                                {provider.businessName}
                              </h3>
                              <div className="flex items-center gap-1">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`w-4 h-4 ${
                                      i < Math.floor(provider.rating || 0)
                                        ? 'text-yellow-400 fill-current'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                                <span className="text-sm text-gray-600 ml-1">
                                  {provider.rating?.toFixed(1) || 'New'}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Location */}
                          <div className="flex items-center gap-2 mb-3">
                            <MapPin className="w-4 h-4 text-blue-500" />
                            <span className="text-gray-700">{provider.city}, {provider.state}</span>
                          </div>

                          {/* Type of Service */}
                          <div className="mb-3">
                            <span className="px-3 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 rounded-full text-sm font-medium">
                              {provider.serviceType}
                            </span>
                          </div>

                          {/* Availability/Hours */}
                          {provider.businessHours && (
                            <div className="flex items-center gap-2 mb-4">
                              <Clock className="w-4 h-4 text-green-500" />
                              <span className="text-gray-700 text-sm">
                                {(() => {
                                  const today = new Date().toLocaleString('en-US', { weekday: 'short' }).toLowerCase();
                                  const todayHours = provider.businessHours[today] || provider.businessHours['monday'];
                                  if (todayHours?.closed) {
                                    return 'Closed today';
                                  }
                                  return `Available today: ${todayHours?.open || '9:00 AM'} - ${todayHours?.close || '5:00 PM'}`;
                                })()}
                              </span>
                            </div>
                          )}

                          {/* Book Now Button */}
                          <button
                            onClick={() => window.location.href = '/auth/signup'}
                            className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                          >
                            <Calendar className="w-4 h-4" />
                            Book Now
                          </button>
                        </div>
                      ) : (
                        // FULL CARD FOR LOGGED-IN USERS
                        <div className="backdrop-blur-xl bg-white/90 border border-blue-200/30 rounded-3xl p-6 hover:shadow-2xl hover:bg-white/95 transition-all duration-300 group hover:scale-[1.02]">
                          {/* Modern Header */}
                          <div className="flex items-start justify-between mb-6">
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                                {provider.profilePhoto ? (
                                  <Image
                                    src={provider.profilePhoto}
                                    alt={provider.businessName}
                                    width={64}
                                    height={64}
                                    className="w-16 h-16 rounded-2xl object-cover"
                                  />
                                ) : (
                                  <span className="text-white font-bold text-lg">
                                    {provider.businessName?.split(' ').map(n => n[0]).join('') || 'P'}
                                  </span>
                                )}
                              </div>

                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-700 transition-colors">
                                    {provider.businessName}
                                  </h3>
                                  {provider.verified && (
                                    <Verified className="w-5 h-5 text-blue-500" />
                                  )}
                                </div>

                                <div className="flex items-center gap-4 text-sm text-gray-600">
                                  <div className="flex items-center gap-1">
                                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                    <span className="font-medium">{provider.rating || 'New'}</span>
                                    {provider.reviewCount > 0 && (
                                      <span>({provider.reviewCount} reviews)</span>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <MapPin className="w-4 h-4 text-blue-500" />
                                    <span>{provider.city}, {provider.state}</span>
                                    {provider.distance && (
                                      <span className="text-blue-600 font-medium">• {provider.distance} mi</span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="flex gap-2">
                              <button
                                onClick={() => toggleFavorite(provider.id)}
                                className={`p-2 rounded-xl transition-all duration-200 ${
                                  favorites.includes(provider.id)
                                    ? 'bg-red-100 text-red-500 shadow-md'
                                    : 'bg-gray-100 text-gray-500 hover:bg-red-100 hover:text-red-500 hover:shadow-md'
                                }`}
                              >
                                <Heart className={`w-5 h-5 ${favorites.includes(provider.id) ? 'fill-current' : ''}`} />
                              </button>
                              <button className="p-2 rounded-xl bg-gray-100 text-gray-500 hover:bg-blue-100 hover:text-blue-600 transition-all duration-200">
                                <Share2 className="w-5 h-5" />
                              </button>
                            </div>
                          </div>

                          {/* Description */}
                          <p className="text-gray-700 mb-4 leading-relaxed line-clamp-2">{provider.description}</p>

                          {/* Service Type & Specialties */}
                          <div className="mb-4">
                            <div className="flex flex-wrap gap-2">
                              <span className="px-3 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 rounded-full text-sm font-medium">
                                {provider.serviceType}
                              </span>
                              {provider.specialties?.slice(0, 2).map((specialty, index) => (
                                <span key={index} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                                  {specialty}
                                </span>
                              ))}
                              {provider.specialties?.length > 2 && (
                                <span className="px-3 py-1 bg-gray-200 text-gray-600 rounded-full text-sm">
                                  +{provider.specialties.length - 2} more
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Contact Info & Services */}
                          <div className="mb-6 space-y-4">
                            {/* Contact Information */}
                            <div className="grid grid-cols-1 gap-3">
                              <div className="flex items-center gap-3">
                                <span className="text-gray-700">{provider.address}</span>
                              </div>
                              {provider.email && (
                                <div className="flex items-center gap-3">
                                  <Mail className="w-4 h-4 text-blue-500" />
                                  <span className="text-gray-700">{provider.email}</span>
                                </div>
                              )}
                              {provider.phone && (
                                <div className="flex items-center gap-3">
                                  <Phone className="w-4 h-4 text-green-500" />
                                  <span className="text-gray-700">{provider.phone}</span>
                                </div>
                              )}
                            </div>

                            {/* Active Services */}
                            {provider.activeServices && provider.activeServices.length > 0 && (
                              <div className="p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200">
                                <div className="flex items-center gap-2 text-sm text-blue-700">
                                  <Award className="w-4 h-4" />
                                  <span className="font-medium">{provider.activeServices.length} Active Services Available</span>
                                </div>
                              </div>
                            )}

                            {/* Business Hours */}
                            {provider.businessHours && (
                              <div className="p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
                                <div className="flex items-center gap-2 text-sm text-gray-700">
                                  <Clock className="w-4 h-4" />
                                  <span className="font-medium">
                                    {(() => {
                                      const today = new Date().toLocaleString('en-US', { weekday: 'short' }).toLowerCase();
                                      const todayHours = provider.businessHours[today] || provider.businessHours['monday'];
                                      if (todayHours?.closed) {
                                        return 'Closed today';
                                      }
                                      return `Open today: ${todayHours?.open || '9:00 AM'} - ${todayHours?.close || '5:00 PM'}`;
                                    })()}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Modern Action Buttons */}
                          <div className="flex gap-3">
                            <button
                              onClick={() => window.location.href = `/appointments?providerId=${provider.id}`}
                              className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                            >
                              <Calendar className="w-4 h-4" />
                              Book Appointment
                            </button>
                            <button
                              onClick={() => window.location.href = `/provider/public/${provider.id}`}
                              className="border border-blue-300 text-blue-600 hover:bg-blue-50 py-3 px-4 rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
                            >
                              <MessageCircle className="w-4 h-4" />
                              View Profile
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

          </div>
        </div>
      </div>
    </div>
  );
}