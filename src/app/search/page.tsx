'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams as useNextSearchParams, useRouter } from 'next/navigation';
import { Search, MapPin, Star, Phone, Calendar, Clock, MessageCircle, SlidersHorizontal } from 'lucide-react';
import Image from 'next/image';
import { searchProviders, getActiveProviders, getEmergencyProviders, type SearchFilters, type Provider as ProviderType } from '@/lib/firebase/providers';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

type SortOption = 'Recommended' | 'Highest Rated' | 'Lowest Price' | 'Most Reviews';

interface SearchPageParams extends SearchFilters {
  searchQuery?: string;
  emergency?: boolean;
}

type SearchParams = SearchPageParams;

// Sample data for dropdowns
const SERVICE_TYPES = ['All Services', 'Dog Walking', 'Pet Sitting', 'Grooming', 'Boarding', 'Training'] as const;
type ServiceType = typeof SERVICE_TYPES[number];

const PET_TYPES = ['All Pets', 'Dog', '<PERSON>', '<PERSON>', 'Rabbit', 'Other'] as const;
type PetType = typeof PET_TYPES[number];

const LOCATIONS = ['Anywhere', 'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'] as const;
type Location = typeof LOCATIONS[number];

const SORT_OPTIONS: SortOption[] = ['Recommended', 'Highest Rated', 'Most Reviews', 'Lowest Price'];

interface Provider {
  id: string;
  businessName: string;
  ownerName: string;
  serviceType: string;
  rating: number;
  reviewCount: number;
  city: string;
  state: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  description: string;
  specialties: string[];
  verified: boolean;
  featured: boolean;
  profilePhoto?: string;
  responseTime: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  activeServices?: any[];
  hasActiveServices?: boolean;
  distance?: number;
  businessHours?: Record<string, { open: string; close: string; closed?: boolean }>;
}

export default function SearchPage() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useNextSearchParams();
  
  // State
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Search form state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<Location>('Anywhere');
  const [selectedService, setSelectedService] = useState<ServiceType>('All Services');
  const [selectedPetType, setSelectedPetType] = useState<PetType>('All Pets');
  const [selectedDate, setSelectedDate] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('Recommended');
  const [showFilters, setShowFilters] = useState(false);

  // Initialize from URL params
  useEffect(() => {
    const query = searchParams.get('q') || '';
    const location = searchParams.get('location') || 'Anywhere';
    const service = searchParams.get('service') || 'All Services';
    const emergency = searchParams.get('emergency') === 'true';

    setSearchQuery(query);
    setSelectedLocation(location as Location);
    setSelectedService(service as ServiceType);

    // Perform initial search
    performSearch({
      searchQuery: query || undefined,
      location: location !== 'Anywhere' ? location : undefined,
      service: service !== 'All Services' ? service : undefined,
      emergency
    });
  }, []);

  const emergencyKeywords = useMemo(() => ['emergency', 'vet', 'veterinary', 'hospital', 'clinic'], []);

  // Perform search with the given parameters
  const performSearch = useCallback(async (params: SearchParams) => {
    try {
      setLoading(true);
      setError(null);
      
      const isEmergencySearch = params.emergency || 
        (params.searchQuery && emergencyKeywords.some(keyword => 
          params.searchQuery!.toLowerCase().includes(keyword)
        ));

      let results: Provider[];
      
      if (isEmergencySearch) {
        results = await getEmergencyProviders();
      } else {
        // Get only approved providers with active services
        const activeProviders = await getActiveProviders();
        console.log(`📊 Found ${activeProviders.length} active providers`);

        // If no providers found, add sample data for demonstration
        if (activeProviders.length === 0) {
          console.log('🔧 No providers found, using sample data for demonstration');
          const sampleProviders: Provider[] = [
            {
              id: 'sample-1',
              businessName: 'Happy Paws Pet Care',
              ownerName: 'Sarah Johnson',
              email: '<EMAIL>',
              phone: '(*************',
              address: '123 Main St',
              city: 'Austin',
              state: 'TX',
              serviceType: 'Pet Sitting',
              description: 'Professional pet sitting services with over 10 years of experience.',
              specialties: ['Dog Walking', 'Pet Sitting', 'Overnight Care'],
              rating: 4.8,
              reviewCount: 127,
              status: 'approved' as const,
              verified: true,
              featured: false,
              responseTime: '< 30 minutes',
              website: 'https://happypaws.com',
              profilePhoto: '',
              hasActiveServices: true,
              businessHours: {
                monday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                tuesday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                wednesday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                thursday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                friday: { open: '7:00 AM', close: '7:00 PM', closed: false },
                saturday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                sunday: { open: '9:00 AM', close: '5:00 PM', closed: false }
              }
            },
            {
              id: 'sample-2',
              businessName: 'Paws & Claws Veterinary',
              ownerName: 'Dr. Michael Chen',
              email: '<EMAIL>',
              phone: '(*************',
              address: '456 Oak Ave',
              city: 'Austin',
              state: 'TX',
              serviceType: 'Veterinary Care',
              description: 'Full-service veterinary clinic providing comprehensive medical care.',
              specialties: ['Emergency Care', 'Surgery', 'Dental Care', 'Vaccinations'],
              rating: 4.9,
              reviewCount: 89,
              status: 'approved' as const,
              verified: true,
              featured: true,
              responseTime: '< 15 minutes',
              website: 'https://pawsclaws.com',
              profilePhoto: '',
              hasActiveServices: true,
              businessHours: {
                monday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                tuesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                wednesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                thursday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                friday: { open: '8:00 AM', close: '6:00 PM', closed: false },
                saturday: { open: '9:00 AM', close: '4:00 PM', closed: false },
                sunday: { closed: true }
              }
            },
            {
              id: 'sample-3',
              businessName: 'Furry Friends Grooming',
              ownerName: 'Lisa Rodriguez',
              email: '<EMAIL>',
              phone: '(*************',
              address: '789 Elm St',
              city: 'Austin',
              state: 'TX',
              serviceType: 'Pet Grooming',
              description: 'Professional pet grooming services for all breeds.',
              specialties: ['Full Grooming', 'Nail Trimming', 'Teeth Cleaning'],
              rating: 4.7,
              reviewCount: 156,
              status: 'approved' as const,
              verified: true,
              featured: false,
              responseTime: '< 1 hour',
              website: 'https://furryfriends.com',
              profilePhoto: '',
              hasActiveServices: true,
              businessHours: {
                monday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                tuesday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                wednesday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                thursday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                friday: { open: '9:00 AM', close: '5:00 PM', closed: false },
                saturday: { open: '10:00 AM', close: '3:00 PM', closed: false },
                sunday: { closed: true }
              }
            }
          ];
          results = sampleProviders;
        } else {
          results = activeProviders.map((p: any) => ({ ...p, id: p.id ?? '' }));
        }
      }

      setProviders(results);
      toast.success(`Found ${results.length} providers`);
    } catch (error) {
      console.error('Error searching providers:', error);
      toast.error('Search failed');
      setError('Failed to search providers');
    } finally {
      setLoading(false);
    }
  }, [emergencyKeywords]);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    const searchParams: SearchParams = {
      searchQuery: searchQuery || undefined,
      location: selectedLocation !== 'Anywhere' ? selectedLocation : undefined,
      service: selectedService !== 'All Services' ? selectedService : undefined,
      petType: selectedPetType !== 'All Pets' ? selectedPetType : undefined,
      date: selectedDate || undefined,
    };

    // Update URL
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedLocation !== 'Anywhere') params.set('location', selectedLocation);
    if (selectedService !== 'All Services') params.set('service', selectedService);
    
    router.push(`/search?${params.toString()}`);
    
    // Check if this is an emergency search
    const isEmergencySearch = emergencyKeywords.some(keyword => 
      searchQuery.toLowerCase().includes(keyword)
    );

    if (isEmergencySearch) {
      // Show emergency providers first
      const emergencyParams: SearchParams = { ...searchParams, emergency: true };
      performSearch(emergencyParams);
    } else {
      // Regular search
      performSearch(searchParams);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-teal-50">
      {/* Search Header */}
      <div className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <form onSubmit={handleSearch} className="space-y-4">
            {/* Main Search Bar */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search for pet services..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value as Location)}
                  className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  {LOCATIONS.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
                
                <select
                  value={selectedService}
                  onChange={(e) => setSelectedService(e.target.value as ServiceType)}
                  className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  {SERVICE_TYPES.map(service => (
                    <option key={service} value={service}>{service}</option>
                  ))}
                </select>
                
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all disabled:opacity-50"
                >
                  {loading ? 'Searching...' : 'Search'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Searching for providers...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {providers.map((provider) => (
              <ProviderCard key={provider.id} provider={provider} isLoggedIn={!!user} />
            ))}
          </div>
        )}

        {!loading && providers.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-600">No providers found. Try adjusting your search criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}

// Provider Card Component
function ProviderCard({ provider, isLoggedIn }: { provider: Provider; isLoggedIn: boolean }) {
  if (!isLoggedIn) {
    // Simple card for non-logged users
    return (
      <div className="bg-white/95 backdrop-blur-sm rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="font-bold text-lg text-gray-800">{provider.businessName}</h3>
            <p className="text-gray-600">{provider.serviceType}</p>
          </div>
          {provider.verified && (
            <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
              Verified
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2 mb-3">
          <div className="flex items-center">
            <Star className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="ml-1 text-sm font-medium">{provider.rating}</span>
            <span className="ml-1 text-sm text-gray-500">({provider.reviewCount} reviews)</span>
          </div>
        </div>
        
        <div className="flex items-center gap-2 mb-4">
          <MapPin className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600">{provider.city}, {provider.state}</span>
        </div>
        
        <button className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-2 px-4 rounded-lg hover:from-green-700 hover:to-blue-700 transition-all">
          Book Now
        </button>
      </div>
    );
  }

  // Detailed card for logged-in users
  return (
    <div className="bg-white/95 backdrop-blur-sm rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="font-bold text-lg text-gray-800">{provider.businessName}</h3>
          <p className="text-gray-600">{provider.ownerName}</p>
          <p className="text-sm text-gray-500">{provider.serviceType}</p>
        </div>
        {provider.verified && (
          <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
            Verified
          </div>
        )}
      </div>
      
      <div className="flex items-center gap-2 mb-3">
        <div className="flex items-center">
          <Star className="w-4 h-4 text-yellow-400 fill-current" />
          <span className="ml-1 text-sm font-medium">{provider.rating}</span>
          <span className="ml-1 text-sm text-gray-500">({provider.reviewCount} reviews)</span>
        </div>
      </div>
      
      <p className="text-sm text-gray-600 mb-4 line-clamp-2">{provider.description}</p>
      
      <div className="space-y-2 mb-4">
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600">{provider.address}, {provider.city}, {provider.state}</span>
        </div>
        <div className="flex items-center gap-2">
          <Phone className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600">{provider.phone}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-600">Responds {provider.responseTime}</span>
        </div>
      </div>
      
      <div className="flex gap-2">
        <button className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 text-white py-2 px-4 rounded-lg hover:from-green-700 hover:to-blue-700 transition-all">
          Book Now
        </button>
        <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
          <MessageCircle className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    </div>
  );
}
