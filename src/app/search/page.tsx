'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  Search,
  MapPin,
  Star,
  Phone,
  Calendar,
  Clock,
  Award,
  Verified,
  SlidersHorizontal,
  Heart,
  Share2,
  MessageCircle,
  Mail,
  Loader2,
  RefreshCw
} from 'lucide-react';
import Image from 'next/image';
import { type Provider } from '@/lib/firebase/providers';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';

const serviceTypes = ['All Services', 'Grooming', 'Veterinary', 'Pet Hotel', 'Dog Walking', 'Training', 'Pet Sitting'];
const sortOptions = ['Recommended', 'Highest Rated', 'Nearest', 'Lowest Price', 'Most Reviews'];

export default function SearchPage() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // State
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Search form state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('All Services');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [sortBy, setSortBy] = useState('Recommended');
  const [showFilters, setShowFilters] = useState(false);

  // Initialize from URL params
  useEffect(() => {
    const query = searchParams.get('q') || '';
    const service = searchParams.get('service') || 'All Services';
    const location = searchParams.get('location') || '';

    setSearchQuery(query);
    setSelectedService(service);
    setSelectedLocation(location);

    // Perform initial search if there are params, otherwise load all
    if (query || location || service !== 'All Services') {
      performSearch(query, location, service);
    } else {
      loadAllProviders();
    }
  }, [searchParams]);

  // Load all active providers (initial load)
  const loadAllProviders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setHasSearched(false);

      console.log('🔍 Loading all active providers from Firebase...');

      // Try simple approach first - just get approved providers without services check
      console.log('🔍 Attempting simple provider query...');

      try {
        // Direct Firebase query without complex service checking
        const { collection, getDocs, query, where } = await import('firebase/firestore');
        const { db } = await import('@/lib/firebase/config');

        const providersQuery = query(
          collection(db, 'providers'),
          where('status', '==', 'approved')
        );

        const providersSnapshot = await getDocs(providersQuery);
        console.log(`📊 Direct query found ${providersSnapshot.size} approved providers`);

        const providers = providersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Provider[];

        // Debug: Log the first provider to see structure
        if (providers.length > 0) {
          console.log('🔍 First provider structure:', providers[0]);
          setProviders(providers);
        } else {
          // Fallback: try getting ALL providers regardless of status
          console.log('❌ No approved providers, trying all providers...');
          const allProvidersQuery = collection(db, 'providers');
          const allProvidersSnapshot = await getDocs(allProvidersQuery);
          console.log(`📊 Found ${allProvidersSnapshot.size} total providers`);

          const allProviders = allProvidersSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Provider[];

          if (allProviders.length > 0) {
            console.log('🔍 First provider from all:', allProviders[0]);
            setProviders(allProviders);
          }
        }
      } catch (directError) {
        console.error('❌ Direct query failed:', directError);
        // Set empty providers if query fails
        setProviders([]);
      }

      setHasSearched(true);

      if (providers.length === 0) {
        console.log('❌ No providers found');
      } else {
        console.log('✅ Providers loaded successfully');
      }
    } catch (error) {
      console.error('❌ Error loading providers:', error);
      setError('Failed to load providers. Please try again.');
      toast.error('Failed to load providers');
    } finally {
      setLoading(false);
    }
  }, []);

  // Perform search with Firebase
  const performSearch = useCallback(async (query: string, location: string, serviceType: string) => {
    try {
      setSearching(true);
      setError(null);
      setHasSearched(false);

      console.log('🔍 Searching Firebase with:', { query, location, serviceType });

      // Get all providers first using direct query to avoid permissions issues
      console.log('🔍 Performing direct search query...');

      const { collection, getDocs, query: firestoreQuery, where } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase/config');

      // Simple query for approved providers
      const providersQuery = firestoreQuery(
        collection(db, 'providers'),
        where('status', '==', 'approved')
      );

      const providersSnapshot = await getDocs(providersQuery);
      let results = providersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Provider[];

      console.log(`📊 Got ${results.length} approved providers from direct query`);

      // Filter by search criteria
      if (query || location || (serviceType && serviceType !== 'All Services')) {
        results = results.filter(provider => {
          let matches = true;

          // Search query filter (search all text fields)
          if (query) {
            const searchTerm = query.toLowerCase();
            const searchableText = [
              provider.businessName,
              provider.ownerName,
              provider.serviceType,
              provider.description,
              provider.city,
              provider.state,
              provider.zipCode,
              provider.address,
              ...(provider.specialties || [])
            ].join(' ').toLowerCase();

            matches = matches && searchableText.includes(searchTerm);
          }

          // Location filter (city, state, or zipcode)
          if (location) {
            const locationTerm = location.toLowerCase();
            const locationMatch =
              provider.city?.toLowerCase().includes(locationTerm) ||
              provider.state?.toLowerCase().includes(locationTerm) ||
              provider.zipCode?.includes(location) ||
              provider.address?.toLowerCase().includes(locationTerm);

            matches = matches && locationMatch;
          }

          // Service type filter
          if (serviceType && serviceType !== 'All Services') {
            const serviceMatch = provider.serviceType?.toLowerCase().includes(serviceType.toLowerCase());
            matches = matches && serviceMatch;
          }

          return matches;
        });
      }

      console.log(`📊 Search results: ${results.length} providers found`);
      setProviders(results);
      setHasSearched(true);

      if (results.length === 0) {
        console.log('❌ No providers match search criteria');
      } else {
        toast.success(`Found ${results.length} providers`);
      }
    } catch (error) {
      console.error('❌ Error searching providers:', error);
      setError('Search failed. Please try again.');
      toast.error('Search failed');
    } finally {
      setSearching(false);
    }
  }, []);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Update URL with search params
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedLocation) params.set('location', selectedLocation);
    if (selectedService !== 'All Services') params.set('service', selectedService);
    
    router.push(`/search?${params.toString()}`);
    
    // Perform search
    performSearch(searchQuery, selectedLocation, selectedService);
  };

  const refreshSearch = () => {
    if (searchQuery || selectedLocation || selectedService !== 'All Services') {
      performSearch(searchQuery, selectedLocation, selectedService);
    } else {
      loadAllProviders();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-teal-50">
      {/* Hero Section with Search */}
      <div className="relative bg-gradient-to-br from-green-400 via-blue-400 to-teal-500 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Find Perfect Pet Care
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Connect with verified pet service professionals in your area
            </p>
          </div>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="max-w-4xl mx-auto">
            <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl p-6 shadow-2xl">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search Input */}
                <div className="md:col-span-2 relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search services, providers, or locations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800 placeholder-gray-500"
                  />
                </div>

                {/* Service Type */}
                <div>
                  <select
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="w-full py-4 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-2xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                  >
                    {serviceTypes.map(service => (
                      <option key={service} value={service}>{service}</option>
                    ))}
                  </select>
                </div>

                {/* Search Button */}
                <div>
                  <button
                    type="submit"
                    disabled={loading || searching}
                    className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 flex items-center justify-center gap-2"
                  >
                    {searching ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Searching...
                      </>
                    ) : (
                      <>
                        <Search className="w-5 h-5" />
                        Search
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Additional Filters */}
              <div className="mt-4 flex flex-wrap gap-4">
                <input
                  type="text"
                  placeholder="Location (city, state, zip code)"
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="flex-1 min-w-[200px] py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800 placeholder-gray-500"
                />
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="py-3 px-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl focus:ring-2 focus:ring-white/50 focus:border-transparent text-gray-800"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Results Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Results Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {loading || searching ? 'Searching...' : 
               hasSearched ? `${providers.length} Providers Found` : 'Loading Providers...'}
            </h2>
            {searchQuery && hasSearched && (
              <p className="text-gray-600 mt-1">
                Results for "{searchQuery}"
                {selectedLocation && ` in ${selectedLocation}`}
              </p>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={refreshSearch}
              disabled={loading || searching}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${(loading || searching) ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors"
            >
              <SlidersHorizontal className="w-4 h-4" />
              Filters
            </button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="backdrop-blur-xl bg-red-50/90 border border-red-200/30 rounded-3xl p-6 mb-8">
            <p className="text-red-600 text-center">{error}</p>
            <button
              onClick={refreshSearch}
              className="mt-4 mx-auto block px-6 py-2 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Loading State */}
        {(loading || searching) && (
          <div className="flex justify-center items-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">
                {searching ? 'Searching providers...' : 'Loading providers...'}
              </p>
            </div>
          </div>
        )}

        {/* Provider Cards */}
        {!loading && !searching && hasSearched && (
          <>
            {providers.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {providers.map((provider) => {
                  // Show simple cards for non-logged users, detailed for logged users
                  const showSimpleCard = !isAuthenticated;

                  return (
                    <ProviderCard
                      key={provider.id}
                      provider={provider}
                      showSimpleCard={showSimpleCard}
                    />
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="backdrop-blur-xl bg-white/90 border border-gray-200/30 rounded-3xl p-12 max-w-md mx-auto">
                  <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No providers found</h3>
                  <p className="text-gray-600 mb-6">
                    {selectedLocation 
                      ? `No providers available in ${selectedLocation}. Try expanding your search area or adjusting your criteria.`
                      : 'No providers match your search criteria. Try adjusting your filters or search terms.'
                    }
                  </p>
                  <button
                    onClick={refreshSearch}
                    className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all"
                  >
                    Refresh Search
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

// Provider Card Component
function ProviderCard({ provider, showSimpleCard }: { provider: Provider; showSimpleCard: boolean }) {
  if (showSimpleCard) {
    // Simple card for non-logged users
    return (
      <div className="backdrop-blur-xl bg-white/90 border border-green-200/30 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group">
        {/* Provider Image & Name */}
        <div className="flex items-center gap-4 mb-4">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center shadow-lg">
            {provider.profilePhoto ? (
              <Image
                src={provider.profilePhoto}
                alt={provider.businessName}
                width={64}
                height={64}
                className="w-16 h-16 rounded-2xl object-cover"
              />
            ) : (
              <span className="text-white font-bold text-lg">
                {provider.businessName?.split(' ').map(n => n[0]).join('') || 'P'}
              </span>
            )}
          </div>
          <div className="flex-1">
            <h3 className="font-bold text-lg text-gray-900 group-hover:text-green-600 transition-colors">
              {provider.businessName}
            </h3>
            <div className="flex items-center gap-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(provider.rating || 0)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
              <span className="text-sm text-gray-600 ml-1">
                {provider.rating?.toFixed(1) || 'New'}
              </span>
            </div>
          </div>
        </div>

        {/* Location */}
        <div className="flex items-center gap-2 mb-3">
          <MapPin className="w-4 h-4 text-green-500" />
          <span className="text-gray-700">{provider.city}, {provider.state} {provider.zipCode}</span>
        </div>

        {/* Type of Service */}
        <div className="mb-3">
          <span className="px-3 py-1 bg-gradient-to-r from-green-100 to-blue-100 text-green-700 rounded-full text-sm font-medium">
            {provider.serviceType}
          </span>
        </div>

        {/* Book Now Button */}
        <button
          onClick={() => window.location.href = '/auth/signup'}
          className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
        >
          <Calendar className="w-4 h-4" />
          Book Now
        </button>
      </div>
    );
  }

  // Full card for logged-in users
  return (
    <div className="backdrop-blur-xl bg-white/90 border border-green-200/30 rounded-3xl p-6 hover:shadow-2xl hover:bg-white/95 transition-all duration-300 group hover:scale-[1.02]">
      {/* Header with provider info */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center shadow-lg">
            {provider.profilePhoto ? (
              <Image
                src={provider.profilePhoto}
                alt={provider.businessName}
                width={64}
                height={64}
                className="w-16 h-16 rounded-2xl object-cover"
              />
            ) : (
              <span className="text-white font-bold text-lg">
                {provider.businessName?.split(' ').map(n => n[0]).join('') || 'P'}
              </span>
            )}
          </div>

          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-xl font-bold text-gray-900 group-hover:text-green-700 transition-colors">
                {provider.businessName}
              </h3>
              {provider.verified && (
                <Verified className="w-5 h-5 text-green-500" />
              )}
              {provider.featured && (
                <Award className="w-5 h-5 text-yellow-500" />
              )}
            </div>
            <p className="text-gray-600 font-medium">{provider.ownerName}</p>
            <p className="text-sm text-gray-500">{provider.serviceType}</p>
          </div>
        </div>

        <div className="flex gap-2">
          <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
            <Heart className="w-5 h-5" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <Share2 className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Rating and Reviews */}
      <div className="flex items-center gap-4 mb-4">
        <div className="flex items-center gap-1">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`w-4 h-4 ${
                i < Math.floor(provider.rating || 0)
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              }`}
            />
          ))}
          <span className="font-semibold text-gray-900 ml-2">
            {provider.rating?.toFixed(1) || 'New'}
          </span>
          <span className="text-gray-500">
            ({provider.reviewCount || 0} reviews)
          </span>
        </div>
      </div>

      {/* Description */}
      <p className="text-gray-600 mb-4 line-clamp-2">{provider.description}</p>

      {/* Specialties */}
      {provider.specialties && provider.specialties.length > 0 && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {provider.specialties.slice(0, 3).map((specialty, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gradient-to-r from-green-100 to-blue-100 text-green-700 rounded-full text-xs font-medium"
              >
                {specialty}
              </span>
            ))}
            {provider.specialties.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                +{provider.specialties.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Contact Info */}
      <div className="space-y-2 mb-6">
        <div className="flex items-center gap-3 text-sm text-gray-600">
          <MapPin className="w-4 h-4 text-green-500" />
          <span>{provider.address}, {provider.city}, {provider.state} {provider.zipCode}</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-gray-600">
          <Phone className="w-4 h-4 text-green-500" />
          <span>{provider.phone}</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-gray-600">
          <Clock className="w-4 h-4 text-green-500" />
          <span>Responds {provider.responseTime}</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2">
          <Calendar className="w-4 h-4" />
          Book Now
        </button>
        <button className="px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
          <MessageCircle className="w-5 h-5 text-gray-600" />
        </button>
        <button className="px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
          <Mail className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    </div>
  );
}
