const puppeteer = require('puppeteer');

async function testDashboard() {
  console.log('🚀 Starting comprehensive dashboard test...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  const page = await browser.newPage();
  
  // Enable console logging
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log('❌ Console Error:', msg.text());
    } else if (msg.type() === 'warning') {
      console.log('⚠️ Console Warning:', msg.text());
    }
  });
  
  // Enable error logging
  page.on('pageerror', error => {
    console.log('💥 Page Error:', error.message);
  });
  
  try {
    console.log('📱 Testing provider dashboard...');
    await page.goto('http://localhost:3000/provider/dashboard', { 
      waitUntil: 'networkidle0',
      timeout: 10000 
    });
    
    console.log('✅ Dashboard loaded successfully');
    
    // Test all sidebar tabs
    const tabs = [
      'overview',
      'bookings', 
      'services',
      'customers',
      'analytics',
      'profile',
      'integrations',
      'subscription',
      'settings'
    ];
    
    for (const tab of tabs) {
      try {
        console.log(`🔍 Testing ${tab} tab...`);
        
        // Click the tab
        await page.click(`[data-tab="${tab}"]`);
        await page.waitForTimeout(1000);
        
        // Check for any errors
        const errors = await page.evaluate(() => {
          return window.console.errors || [];
        });
        
        if (errors.length === 0) {
          console.log(`✅ ${tab} tab working correctly`);
        } else {
          console.log(`❌ ${tab} tab has errors:`, errors);
        }
        
      } catch (error) {
        console.log(`❌ Error testing ${tab} tab:`, error.message);
      }
    }
    
    // Test mobile responsiveness
    console.log('📱 Testing mobile responsiveness...');
    await page.setViewport({ width: 375, height: 812 });
    await page.waitForTimeout(1000);
    
    // Test mobile menu
    try {
      const mobileMenuButton = await page.$('button[aria-label="Open mobile menu"]');
      if (mobileMenuButton) {
        await mobileMenuButton.click();
        console.log('✅ Mobile menu opens correctly');
        await page.waitForTimeout(500);
        
        // Close mobile menu
        const overlay = await page.$('.fixed.inset-0.bg-black\\/50');
        if (overlay) {
          await overlay.click();
          console.log('✅ Mobile menu closes correctly');
        }
      } else {
        console.log('⚠️ Mobile menu button not found');
      }
    } catch (error) {
      console.log('❌ Mobile menu test failed:', error.message);
    }
    
    console.log('🎉 Dashboard test completed successfully!');
    
  } catch (error) {
    console.log('💥 Dashboard test failed:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the test if puppeteer is available
if (typeof require !== 'undefined') {
  try {
    testDashboard().catch(console.error);
  } catch (error) {
    console.log('⚠️ Puppeteer not available, skipping automated test');
    console.log('✅ Manual testing required - please check dashboard in browser');
  }
} else {
  console.log('✅ Manual testing required - please check dashboard in browser');
}
