#!/usr/bin/env node

/**
 * Migration script to transfer data from PostgreSQL to Firebase Firestore
 * Run this script to migrate your existing Fetchly data to Firebase
 */

const { Pool } = require('pg');
const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccount = {
  type: "service_account",
  project_id: "fetchly-724b6",
  // You'll need to add your service account key here
  // Download it from Firebase Console > Project Settings > Service Accounts
};

// Uncomment and configure when you have the service account key
// admin.initializeApp({
//   credential: admin.credential.cert(serviceAccount),
//   databaseURL: `https://fetchly-724b6-default-rtdb.firebaseio.com`
// });

// const db = admin.firestore();

// PostgreSQL configuration
const pgConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3005'),
  database: process.env.DB_NAME || 'fetchly_db',
  user: process.env.DB_USER || 'fetchly_user',
  password: process.env.DB_PASSWORD || 'fetchly_password',
};

async function migrateUsers() {
  console.log('🔄 Migrating users...');
  
  const pool = new Pool(pgConfig);
  
  try {
    const result = await pool.query(`
      SELECT 
        id, email, name, phone, address, date_of_birth, profile_picture,
        fetchly_balance, membership_status, reward_points, saved_providers,
        notification_preferences, emergency_contact, role, verified,
        created_at, updated_at
      FROM users
      WHERE role != 'admin'
    `);

    console.log(`Found ${result.rows.length} users to migrate`);

    for (const user of result.rows) {
      const userData = {
        email: user.email,
        name: user.name,
        phone: user.phone,
        address: user.address,
        dateOfBirth: user.date_of_birth,
        profilePicture: user.profile_picture,
        fetchlyBalance: parseFloat(user.fetchly_balance) || 0,
        membershipStatus: user.membership_status || 'free',
        rewardPoints: parseInt(user.reward_points) || 0,
        savedProviders: user.saved_providers || [],
        notificationPreferences: user.notification_preferences || {
          email: true,
          sms: false,
          push: true,
          marketing: false,
          bookingReminders: true,
          promotions: false
        },
        emergencyContact: user.emergency_contact,
        role: user.role || 'user',
        verified: user.verified || false,
        joinedDate: admin.firestore.Timestamp.fromDate(user.created_at),
        createdAt: admin.firestore.Timestamp.fromDate(user.created_at),
        updatedAt: admin.firestore.Timestamp.fromDate(user.updated_at)
      };

      // await db.collection('users').doc(user.id).set(userData);
      console.log(`✅ User ${user.email} prepared for migration`);
    }

    console.log('✅ Users migration prepared');
  } catch (error) {
    console.error('❌ Error migrating users:', error);
  } finally {
    await pool.end();
  }
}

async function migratePets() {
  console.log('🔄 Migrating pets...');
  
  const pool = new Pool(pgConfig);
  
  try {
    const result = await pool.query(`
      SELECT 
        id, user_id, name, type, breed, date_of_birth, weight, color, gender,
        microchip_id, photo, vaccinations, allergies, medications, medical_notes,
        vet_info, created_at, updated_at
      FROM pets
    `);

    console.log(`Found ${result.rows.length} pets to migrate`);

    for (const pet of result.rows) {
      const petData = {
        userId: pet.user_id,
        name: pet.name,
        type: pet.type,
        breed: pet.breed,
        dateOfBirth: pet.date_of_birth ? admin.firestore.Timestamp.fromDate(pet.date_of_birth) : null,
        weight: parseFloat(pet.weight) || null,
        color: pet.color,
        gender: pet.gender,
        microchipId: pet.microchip_id,
        photo: pet.photo,
        vaccinations: pet.vaccinations || [],
        allergies: pet.allergies || [],
        medications: pet.medications || [],
        medicalNotes: pet.medical_notes,
        vetInfo: pet.vet_info,
        createdAt: admin.firestore.Timestamp.fromDate(pet.created_at),
        updatedAt: admin.firestore.Timestamp.fromDate(pet.updated_at)
      };

      // await db.collection('pets').doc(pet.id).set(petData);
      console.log(`✅ Pet ${pet.name} prepared for migration`);
    }

    console.log('✅ Pets migration prepared');
  } catch (error) {
    console.error('❌ Error migrating pets:', error);
  } finally {
    await pool.end();
  }
}

async function migrateBookings() {
  console.log('🔄 Migrating bookings...');
  
  const pool = new Pool(pgConfig);
  
  try {
    const result = await pool.query(`
      SELECT 
        id, user_id, provider_id, service_id, pet_id, scheduled_date, duration,
        status, total_amount, special_instructions, created_at, updated_at
      FROM bookings
    `);

    console.log(`Found ${result.rows.length} bookings to migrate`);

    for (const booking of result.rows) {
      const bookingData = {
        userId: booking.user_id,
        providerId: booking.provider_id,
        serviceId: booking.service_id,
        petId: booking.pet_id,
        scheduledDate: admin.firestore.Timestamp.fromDate(booking.scheduled_date),
        duration: booking.duration,
        status: booking.status,
        totalAmount: parseFloat(booking.total_amount) || 0,
        specialInstructions: booking.special_instructions,
        createdAt: admin.firestore.Timestamp.fromDate(booking.created_at),
        updatedAt: admin.firestore.Timestamp.fromDate(booking.updated_at)
      };

      // await db.collection('bookings').doc(booking.id).set(bookingData);
      console.log(`✅ Booking ${booking.id} prepared for migration`);
    }

    console.log('✅ Bookings migration prepared');
  } catch (error) {
    console.error('❌ Error migrating bookings:', error);
  } finally {
    await pool.end();
  }
}

async function main() {
  console.log('🚀 Starting PostgreSQL to Firebase migration...');
  console.log('');
  
  console.log('⚠️  IMPORTANT: This script is prepared but not yet active.');
  console.log('   To complete the migration:');
  console.log('   1. Download your Firebase service account key');
  console.log('   2. Update the serviceAccount configuration in this file');
  console.log('   3. Uncomment the Firebase initialization code');
  console.log('   4. Uncomment the actual migration calls (db.collection...)');
  console.log('   5. Run this script again');
  console.log('');

  try {
    await migrateUsers();
    await migratePets();
    await migrateBookings();
    
    console.log('');
    console.log('✅ Migration preparation completed!');
    console.log('   All data structures have been validated and are ready for Firebase.');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  migrateUsers,
  migratePets,
  migrateBookings
};
