// Firebase Configuration Test
// This file tests if Firebase is properly configured

import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyC5Fy9h_pQbrJCDCUfyR25_Deswb50HJwo",
  authDomain: "fetchly-724b6.firebaseapp.com",
  projectId: "fetchly-724b6",
  storageBucket: "fetchly-724b6.firebasestorage.app",
  messagingSenderId: "192530321990",
  appId: "1:192530321990:web:c1bb50473a7e1060a77047",
  measurementId: "G-EJ47KTH83Z"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Analytics (only in browser environment)
let analytics;
if (typeof window !== 'undefined') {
  analytics = getAnalytics(app);
}

console.log('Firebase initialized successfully!');
console.log('Project ID:', firebaseConfig.projectId);
console.log('Auth Domain:', firebaseConfig.authDomain);

export { app, analytics };
