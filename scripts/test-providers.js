// Test script to check providers in the database
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, where } = require('firebase/firestore');

// Firebase config (you'll need to add your actual config)
const firebaseConfig = {
  // Add your Firebase config here
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function testProviders() {
  try {
    console.log('🔍 Testing provider queries...');
    
    // Test 1: Get all providers
    const allProvidersQuery = collection(db, 'providers');
    const allProvidersSnapshot = await getDocs(allProvidersQuery);
    console.log(`📊 Total providers in database: ${allProvidersSnapshot.size}`);
    
    // Test 2: Get approved providers
    const approvedProvidersQuery = query(
      collection(db, 'providers'),
      where('status', '==', 'approved')
    );
    const approvedProvidersSnapshot = await getDocs(approvedProvidersQuery);
    console.log(`✅ Approved providers: ${approvedProvidersSnapshot.size}`);
    
    // Test 3: List provider details
    if (approvedProvidersSnapshot.size > 0) {
      console.log('\n📋 Provider Details:');
      approvedProvidersSnapshot.docs.forEach((doc, index) => {
        const provider = doc.data();
        console.log(`${index + 1}. ${provider.businessName || 'No business name'}`);
        console.log(`   - Email: ${provider.email || 'No email'}`);
        console.log(`   - Location: ${provider.city || 'No city'}, ${provider.state || 'No state'}`);
        console.log(`   - Service Type: ${provider.serviceType || 'No service type'}`);
        console.log(`   - Status: ${provider.status || 'No status'}`);
        console.log('');
      });
    }
    
    // Test 4: Get services
    const servicesQuery = collection(db, 'services');
    const servicesSnapshot = await getDocs(servicesQuery);
    console.log(`🛠️ Total services in database: ${servicesSnapshot.size}`);
    
    // Test 5: Get active services
    const activeServicesQuery = query(
      collection(db, 'services'),
      where('active', '==', true)
    );
    const activeServicesSnapshot = await getDocs(activeServicesQuery);
    console.log(`🟢 Active services: ${activeServicesSnapshot.size}`);
    
  } catch (error) {
    console.error('❌ Error testing providers:', error);
  }
}

// Run the test
testProviders();
