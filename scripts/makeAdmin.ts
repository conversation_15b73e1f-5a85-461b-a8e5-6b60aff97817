import { getAuth } from 'firebase/auth';
import { doc, setDoc, getFirestore } from 'firebase/firestore';
import { initializeApp } from 'firebase/app';
import { firebaseConfig } from '../src/lib/firebase';

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Function to make the current user an admin
async function makeCurrentUserAdmin() {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      console.error('No user is currently signed in!');
      console.log('Please sign in first using Firebase Authentication in your app.');
      return;
    }

    // Update the user's role in Firestore
    await setDoc(
      doc(db, 'users', user.uid),
      {
        role: 'admin',
        name: user.displayName || 'Admin User',
        email: user.email,
        updatedAt: new Date().toISOString()
      },
      { merge: true }
    );

    console.log('Success! You are now an admin.');
    console.log('Please refresh your admin dashboard.');
  } catch (error) {
    console.error('Error making user admin:', error);
  }
}

// Run the function
makeCurrentUserAdmin();
