// Sample provider data for testing
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, doc, setDoc } from 'firebase/firestore';

// This would be run manually to add sample data
const sampleProviders = [
  {
    id: 'provider-1',
    businessName: 'Happy Paws Pet Care',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Main St, Austin, TX 78701',
    city: 'Austin',
    state: 'TX',
    zipCode: '78701',
    serviceType: 'Pet Sitting',
    description: 'Professional pet sitting services with over 10 years of experience. We provide loving care for your pets in the comfort of your own home.',
    specialties: ['Dog Walking', 'Pet Sitting', 'Overnight Care'],
    rating: 4.8,
    reviewCount: 127,
    status: 'approved',
    verified: true,
    responseTime: '< 30 minutes',
    website: 'https://happypaws.com',
    businessHours: {
      monday: { open: '7:00 AM', close: '7:00 PM', closed: false },
      tuesday: { open: '7:00 AM', close: '7:00 PM', closed: false },
      wednesday: { open: '7:00 AM', close: '7:00 PM', closed: false },
      thursday: { open: '7:00 AM', close: '7:00 PM', closed: false },
      friday: { open: '7:00 AM', close: '7:00 PM', closed: false },
      saturday: { open: '8:00 AM', close: '6:00 PM', closed: false },
      sunday: { open: '9:00 AM', close: '5:00 PM', closed: false }
    },
    coordinates: { lat: 30.2672, lng: -97.7431 }
  },
  {
    id: 'provider-2',
    businessName: 'Paws & Claws Veterinary',
    email: '<EMAIL>',
    phone: '(*************',
    address: '456 Oak Ave, Austin, TX 78702',
    city: 'Austin',
    state: 'TX',
    zipCode: '78702',
    serviceType: 'Veterinary Care',
    description: 'Full-service veterinary clinic providing comprehensive medical care for dogs, cats, and exotic pets.',
    specialties: ['Emergency Care', 'Surgery', 'Dental Care', 'Vaccinations'],
    rating: 4.9,
    reviewCount: 89,
    status: 'approved',
    verified: true,
    responseTime: '< 15 minutes',
    website: 'https://pawsclaws.com',
    businessHours: {
      monday: { open: '8:00 AM', close: '6:00 PM', closed: false },
      tuesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
      wednesday: { open: '8:00 AM', close: '6:00 PM', closed: false },
      thursday: { open: '8:00 AM', close: '6:00 PM', closed: false },
      friday: { open: '8:00 AM', close: '6:00 PM', closed: false },
      saturday: { open: '9:00 AM', close: '4:00 PM', closed: false },
      sunday: { closed: true }
    },
    coordinates: { lat: 30.2672, lng: -97.7431 }
  },
  {
    id: 'provider-3',
    businessName: 'Furry Friends Grooming',
    email: '<EMAIL>',
    phone: '(*************',
    address: '789 Elm St, Austin, TX 78703',
    city: 'Austin',
    state: 'TX',
    zipCode: '78703',
    serviceType: 'Pet Grooming',
    description: 'Professional pet grooming services for all breeds. We make your pets look and feel their best!',
    specialties: ['Full Grooming', 'Nail Trimming', 'Teeth Cleaning', 'Flea Treatment'],
    rating: 4.7,
    reviewCount: 156,
    status: 'approved',
    verified: true,
    responseTime: '< 1 hour',
    website: 'https://furryfriends.com',
    businessHours: {
      monday: { open: '9:00 AM', close: '5:00 PM', closed: false },
      tuesday: { open: '9:00 AM', close: '5:00 PM', closed: false },
      wednesday: { open: '9:00 AM', close: '5:00 PM', closed: false },
      thursday: { open: '9:00 AM', close: '5:00 PM', closed: false },
      friday: { open: '9:00 AM', close: '5:00 PM', closed: false },
      saturday: { open: '10:00 AM', close: '3:00 PM', closed: false },
      sunday: { closed: true }
    },
    coordinates: { lat: 30.2672, lng: -97.7431 }
  }
];

const sampleServices = [
  // Services for Happy Paws Pet Care
  {
    providerId: 'provider-1',
    name: 'Dog Walking',
    description: 'Professional dog walking service for busy pet owners',
    category: 'Pet Sitting',
    price: 25,
    duration: 30,
    petTypes: ['dog'],
    active: true
  },
  {
    providerId: 'provider-1',
    name: 'Pet Sitting',
    description: 'In-home pet sitting while you\'re away',
    category: 'Pet Sitting',
    price: 50,
    duration: 480,
    petTypes: ['dog', 'cat'],
    active: true
  },
  // Services for Paws & Claws Veterinary
  {
    providerId: 'provider-2',
    name: 'Wellness Exam',
    description: 'Comprehensive health examination for your pet',
    category: 'Veterinary Care',
    price: 75,
    duration: 45,
    petTypes: ['dog', 'cat'],
    active: true
  },
  {
    providerId: 'provider-2',
    name: 'Emergency Care',
    description: '24/7 emergency veterinary services',
    category: 'Veterinary Care',
    price: 150,
    duration: 60,
    petTypes: ['dog', 'cat', 'bird', 'rabbit'],
    active: true
  },
  // Services for Furry Friends Grooming
  {
    providerId: 'provider-3',
    name: 'Full Grooming Package',
    description: 'Complete grooming including bath, cut, and nail trim',
    category: 'Pet Grooming',
    price: 65,
    duration: 120,
    petTypes: ['dog'],
    active: true
  },
  {
    providerId: 'provider-3',
    name: 'Cat Grooming',
    description: 'Gentle grooming services for cats',
    category: 'Pet Grooming',
    price: 45,
    duration: 90,
    petTypes: ['cat'],
    active: true
  }
];

console.log('Sample data prepared. Use Firebase Admin SDK or console to add this data to your database.');
console.log('Providers:', sampleProviders.length);
console.log('Services:', sampleServices.length);

export { sampleProviders, sampleServices };
