# Environment Configuration for Fetchly

# Firebase Configuration
# Get these values from your Firebase project settings
# Go to: https://console.firebase.google.com -> Your Project -> Project Settings -> General -> Your apps
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Stripe Configuration (for payment processing)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Firebase Admin SDK (Server-side authentication)
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com
FIREBASE_CLIENT_ID=your_client_id

# PostgreSQL Database Configuration
DATABASE_URL=postgresql://fetchly_user:fetchly_password@localhost:5432/fetchly_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=fetchly_db
DB_USER=fetchly_user
DB_PASSWORD=fetchly_password
POSTGRES_PASSWORD=your_postgres_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_2024_fetchly
REFRESH_TOKEN_SECRET=your_super_secret_refresh_token_key_change_this_in_production_2024_fetchly
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Socket.IO Configuration
SOCKET_IO_PORT=3002

# File Upload Configuration
UPLOAD_MAX_SIZE=********
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Environment
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Optional: Analytics and monitoring
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id

# Optional: Error tracking
SENTRY_DSN=your-sentry-dsn
